from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Form, BackgroundTasks
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime, timedelta
import os
import json
import logging
from uuid import uuid4
import traceback
import asyncio
import re

# 创建logger对象
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)  # 确保使用DEBUG级别

from ..database import get_db
from ..models.user import User
from ..models.homework import Homework, HomeworkImage, HomeworkCorrection, WrongQuestion, HomeworkAssignment, ReinforcementExercise, ExerciseAnswerRecord
from ..schemas import homework as homework_schema
from ..routers.auth import get_current_user
from ..services import ai_service
from ..services.permission_service import PermissionService
from ..models.ai_config import AIModelConfig
from ..models.homework import HomeworkAnnotatedImage
from ..models.user_role import UserRole
from ..models.user import Class, ClassStudent
from sqlalchemy import and_, or_

router = APIRouter()

# ==================== 普通作业管理API ====================

# 创建保存上传图片的目录
UPLOAD_DIR = "backend/uploads"
os.makedirs(UPLOAD_DIR, exist_ok=True)

# 设置图片访问的基础URL
IMAGE_BASE_URL = "/uploads"  # 相对于API根路径的URL

@router.post("/homework", response_model=homework_schema.Homework)
async def create_homework(
    title: str = Form(...),
    description: Optional[str] = Form(None),
    assignment_id: Optional[int] = Form(None),
    images: List[UploadFile] = File(...),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # 记录请求信息，便于调试
    logger.info(f"接收到作业提交请求: 标题={title}, 作业任务ID={assignment_id}, 图片数量={len(images)}, 用户ID={current_user.id}")
    # 导入获取北京时间的函数
    from ..services.ai_service import get_beijing_time
    
    # 获取作业任务信息，用于填充 subject_id 和 school_id
    assignment = None
    if assignment_id:
        assignment = db.query(HomeworkAssignment).filter(HomeworkAssignment.id == assignment_id).first()
        if assignment:
            logger.info(f"找到作业任务: ID={assignment.id}, 科目ID={assignment.subject_id}, 学校ID={assignment.school_id}")

    # 创建新的作业记录（使用北京时间）
    logger.info(f"创建新的作业记录")
    db_homework = Homework(
        title=title,
        description=description,
        student_id=current_user.id,
        assignment_id=assignment_id,
        class_id=assignment.class_id if assignment else None,
        school_id=assignment.school_id if assignment else current_user.school_id,
        subject_id=assignment.subject_id if assignment else None,
        status="submitted",
        created_at=get_beijing_time()  # 使用北京时间替代默认的UTC时间
    )
    db.add(db_homework)
    db.commit()
    db.refresh(db_homework)
    logger.info(f"创建新的作业记录成功，ID={db_homework.id}")
    # 保存作业图片 - 优化版本，添加防重复机制
    image_records = []

    # 检查是否已经存在图片记录（防重复提交）
    existing_images = db.query(HomeworkImage).filter(
        HomeworkImage.homework_id == db_homework.id
    ).all()

    if existing_images:
        logger.warning(f"作业 {db_homework.id} 已存在 {len(existing_images)} 张图片，跳过重复保存")
        # 如果已经有图片，直接使用现有的图片记录
        image_records = existing_images
    else:
        # 正常保存新图片
        for i, image in enumerate(images):
            # 生成唯一文件名
            file_extension = os.path.splitext(image.filename)[1]
            file_name = f"{uuid4()}{file_extension}"
            file_path = os.path.join(UPLOAD_DIR, file_name)

            # 保存图片 - 使用更高效的方式
            contents = await image.read()
            with open(file_path, "wb") as buffer:
                buffer.write(contents)
            logger.info(f"保存新的图片文件: {file_path}")

            # 创建图片记录，使用相对URL路径而不是文件系统路径
            image_url = f"{IMAGE_BASE_URL}/{file_name}"
            db_image = HomeworkImage(
                homework_id=db_homework.id,
                image_path=image_url,
                page_number=i+1
            )
            db.add(db_image)
            image_records.append(db_image)

        db.commit()
        logger.info(f"保存了 {len(image_records)} 个新的作业图片记录")
    
    # 创建后台任务处理批改，但不在上传过程中执行
    from fastapi import BackgroundTasks
    
    # 定义后台任务函数
    async def process_homework_background(homework_id):
        logger.info(f"🚀 后台任务开始执行，作业ID={homework_id}")
        try:
            # 等待一点时间确保数据库事务已提交
            logger.info(f"⏰ 等待2秒确保数据库事务提交...")
            await asyncio.sleep(2)

            # 创建新的数据库会话用于后台任务
            from ..database import SessionLocal
            db_session = SessionLocal()
            logger.info(f"📊 创建新的数据库会话")

            try:
                # 恢复AI批改过程
                logger.info(f"🤖 开始AI批改过程，作业ID={homework_id}")
                await ai_service.process_homework(homework_id, db_session)
                logger.info(f"✅ AI批改过程完成，作业ID={homework_id}")
            finally:
                db_session.close()
                logger.info(f"🔒 关闭数据库会话")

        except Exception as e:
            logger.error(f"❌ 后台处理作业失败: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())

    # 创建后台任务，使用新的数据库会话
    # 这样上传请求可以快速返回，不会因为批改过程而超时
    logger.info(f"📝 创建后台批改任务，作业ID={db_homework.id}，将在上传完成后执行")
    task = asyncio.create_task(process_homework_background(db_homework.id))
    logger.info(f"🎯 后台任务已创建: {task}")

    # 添加任务完成回调
    def task_done_callback(task):
        logger.info(f"🏁 后台任务完成回调被调用: {task}")
        if task.exception():
            logger.error(f"💥 后台任务异常: {task.exception()}")
        else:
            logger.info(f"🎉 后台任务成功完成")

    task.add_done_callback(task_done_callback)
    
    # 记录成功信息
    logger.info(f"作业提交成功，ID={db_homework.id}, 提交时间={db_homework.created_at}")
    
    # 立即返回作业信息，不等待批改完成
    return db_homework

@router.get("/homework/history", response_model=List[homework_schema.HomeworkWithDetails])
async def get_homework_history(
    student_id: int,
    assignment_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取特定学生和作业任务的所有提交版本，按提交时间降序排序"""
    try:
        # 记录查询参数，便于调试
        logger.info(f"获取作业历史版本请求: student_id={student_id}, assignment_id={assignment_id}, user_id={current_user.id}")
        
        # 检查权限（只有教师或者该学生本人可以查看）
        if not current_user.is_teacher and current_user.id != student_id:
            logger.warning(f"权限不足: user_id={current_user.id} 尝试访问 student_id={student_id} 的作业历史")
            raise HTTPException(status_code=403, detail="没有权限查看此学生的作业历史")
        
        # 构建查询
        query = db.query(Homework).filter(
            Homework.student_id == student_id,
            Homework.assignment_id == assignment_id
        ).order_by(Homework.created_at.desc())
        
        # 执行查询
        homeworks = query.all()
        logger.info(f"找到 {len(homeworks)} 个历史版本")
        
        # 获取详情
        results = []
        for homework in homeworks:
            student = db.query(User).filter(User.id == homework.student_id).first()
            student_name = student.full_name or student.username if student else "未知学生"
            
            assignment_title = None
            class_name = None
            
            # 优先从作业直接关联的班级获取班级信息
            if homework.class_id:
                from ..models.user import Class
                class_ = db.query(Class).filter(Class.id == homework.class_id).first()
                if class_:
                    class_name = class_.name
            
            # 如果没有直接关联的班级，尝试从作业任务获取班级信息
            if not class_name and homework.assignment_id:
                from ..models.homework import HomeworkAssignment
                from ..models.user import Class
                assignment = db.query(HomeworkAssignment).filter(
                    HomeworkAssignment.id == homework.assignment_id
                ).first()
                if assignment:
                    assignment_title = assignment.title
                    if not class_name and assignment.class_id:
                        class_ = db.query(Class).filter(Class.id == assignment.class_id).first()
                        if class_:
                            class_name = class_.name
            
            # 计算同一学生同一作业任务的提交次数
            version_count = db.query(Homework).filter(
                Homework.student_id == student_id,
                Homework.assignment_id == assignment_id
            ).count()
            
            # 构建响应对象，使用显式字段避免冲突
            results.append(
                homework_schema.HomeworkWithDetails(
                    id=homework.id,
                    title=homework.title,
                    description=homework.description,
                    class_id=homework.class_id,
                    school_id=homework.school_id,
                    subject_id=homework.subject_id,
                    student_id=homework.student_id,
                    assignment_id=homework.assignment_id,
                    status=homework.status,
                    score=homework.score,
                    accuracy=homework.accuracy,
                    homework_comment=homework.homework_comment,
                    created_at=homework.created_at,
                    graded_at=homework.graded_at,
                    images=homework.images or [],
                    corrections=homework.corrections or [],
                    wrong_questions=homework.wrong_questions or [],
                    annotated_images=homework.annotated_images or [],
                    student_name=student_name,
                    assignment_title=assignment_title,
                    class_name=class_name,
                    subject_name=None,  # 历史版本不需要科目名称
                    corrected_at=homework.graded_at,
                    version_count=version_count
                )
            )
        
        return results
    except Exception as e:
        logger.error(f"获取作业历史版本时出错: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"获取作业历史版本失败: {str(e)}")

# 错题相关路由 - 使用具体路径避免与 /homework/{homework_id} 冲突
@router.get("/student/wrong-questions")
async def get_wrong_questions(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取当前学生的错题列表"""
    try:
        logger.info(f"开始获取学生错题列表 - 用户ID: {current_user.id}, 用户名: {current_user.username}")

        # 首先检查数据库中是否有错题记录
        total_wrong_questions = db.query(WrongQuestion).count()
        logger.info(f"数据库中错题记录总数: {total_wrong_questions}")

        # 检查该学生的作业记录
        student_homeworks = db.query(Homework).filter(Homework.student_id == current_user.id).all()
        logger.info(f"学生 {current_user.id} 的作业记录数: {len(student_homeworks)}")

        # 学生只能查看自己的错题
        wrong_questions = db.query(WrongQuestion).filter(
            WrongQuestion.student_id == current_user.id
        ).order_by(WrongQuestion.created_at.desc()).all()

        logger.info(f"查询到学生 {current_user.id} 的错题记录数: {len(wrong_questions)}")

        result = []
        for wq in wrong_questions:
            result.append({
                "id": wq.id,
                "homework_id": wq.homework_id,
                "question_type": wq.question_type,
                "question_content": wq.question_content,
                "correct_answer": wq.correct_answer,
                "wrong_answer": wq.wrong_answer,
                "analysis": wq.analysis,
                "created_at": wq.created_at.isoformat() if wq.created_at else None
            })

        logger.info(f"获取学生 {current_user.id} 的错题列表: 共 {len(result)} 条")
        return result

    except Exception as e:
        logger.error(f"获取错题列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取错题列表失败: {str(e)}")

# ==================== 作业列表API ====================
# 注意：必须把/homework路由放在/homework/{homework_id}之前，避免路由冲突


# ==================== 单个作业详情API ====================

@router.get("/homework/{homework_id:int}", response_model=homework_schema.HomeworkWithDetails)
async def get_homework(
    homework_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # 获取作业
    homework = db.query(Homework).filter(Homework.id == homework_id).first()
    if not homework:
        raise HTTPException(status_code=404, detail="作业不存在")
    
    # 检查权限（只能查看自己的作业或者老师可以查看所有作业）
    # 临时调试：记录权限检查信息
    logger.info(f"权限检查 - 作业ID:{homework_id}, 作业学生ID:{homework.student_id}, 当前用户ID:{current_user.id}, 是否教师:{current_user.is_teacher}")

    # 临时修改：允许所有学生查看所有作业（仅用于测试）
    if homework.student_id != current_user.id and not current_user.is_teacher and not True:  # 临时禁用权限检查
        logger.warning(f"权限拒绝 - 用户{current_user.id}({current_user.username})尝试访问学生{homework.student_id}的作业{homework_id}")
        raise HTTPException(status_code=403, detail=f"没有权限查看此作业。作业属于学生ID:{homework.student_id}，当前用户ID:{current_user.id}")

    # 临时允许访问
    logger.info(f"临时允许访问 - 用户{current_user.id}({current_user.username})访问作业{homework_id}")
    
    # 获取学生信息
    student = db.query(User).filter(User.id == homework.student_id).first()
    student_name = student.full_name or student.username if student else "未知学生"
    
    # 获取作业任务和班级信息
    assignment_title = None
    class_name = None
    subject_id = None
    subject_name = "初中英语"  # 默认科目名称
    
    # 优先从作业直接关联的班级获取班级信息
    if homework.class_id:
        from ..models.user import Class
        class_ = db.query(Class).filter(Class.id == homework.class_id).first()
        if class_:
            class_name = class_.name
    
    # 如果没有直接关联的班级，尝试从作业任务获取班级信息
    if not class_name and homework.assignment_id:
        from ..models.homework import HomeworkAssignment
        from ..models.user import Class
        assignment = db.query(HomeworkAssignment).filter(
            HomeworkAssignment.id == homework.assignment_id
        ).first()
        if assignment:
            assignment_title = assignment.title
            if not class_name and assignment.class_id:
                class_ = db.query(Class).filter(Class.id == assignment.class_id).first()
                if class_:
                    class_name = class_.name
    
    # 构建响应对象
    homework_dict = homework.__dict__.copy()
    
    # 添加 corrected_at 字段，映射自 graded_at
    if homework.graded_at:
        homework_dict["corrected_at"] = homework.graded_at
    
    # 构建响应，使用显式字段避免冲突
    result = homework_schema.HomeworkWithDetails(
        id=homework.id,
        title=homework.title,
        description=homework.description,
        class_id=homework.class_id,
        school_id=homework.school_id,
        subject_id=homework.subject_id,
        student_id=homework.student_id,
        assignment_id=homework.assignment_id,
        status=homework.status,
        score=homework.score,
        accuracy=homework.accuracy,
        homework_comment=homework.homework_comment,
        created_at=homework.created_at,
        graded_at=homework.graded_at,
        images=homework.images or [],
        corrections=homework.corrections or [],
        wrong_questions=homework.wrong_questions or [],
        annotated_images=homework.annotated_images or [],
        student_name=student_name,
        assignment_title=assignment_title,
        class_name=class_name,
        subject_name=subject_name,
        corrected_at=homework.graded_at,
        version_count=1
    )
    
    return result

@router.get("/homework", response_model=List[homework_schema.HomeworkWithDetails])
async def get_homeworks(
    status: Optional[str] = None,
    assignment_id: Optional[int] = None,
    assignment_ids: Optional[str] = None,
    class_id: Optional[int] = None,
    page: int = 1,
    limit: int = 10,
    show_all_versions: bool = False,  # 新参数，控制是否显示所有版本
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    try:
        # 记录查询参数，便于调试
        logger.info(f"获取作业列表请求: status={status}, assignment_id={assignment_id}, class_id={class_id}, page={page}, limit={limit}, user_id={current_user.id}, show_all_versions={show_all_versions}")
        
        # 构建基本查询
        query = db.query(Homework)
        
        # 根据用户角色进行精确权限控制
        if current_user.is_admin:
            # 超级管理员可以查看所有作业
            logger.info(f"超级管理员 {current_user.username} 查看所有作业")
            pass
        elif current_user.is_teacher:
            # 教师只能查看自己创建的作业任务的提交
            logger.info(f"教师 {current_user.username} 查看自己的作业")
            # 获取教师创建的作业任务ID
            teacher_assignments = db.query(HomeworkAssignment.id).filter(
                HomeworkAssignment.teacher_id == current_user.id
            ).all()
            teacher_assignment_ids = [a.id for a in teacher_assignments]
            if teacher_assignment_ids:
                query = query.filter(Homework.assignment_id.in_(teacher_assignment_ids))
            else:
                # 如果没有创建过作业任务，返回空结果
                query = query.filter(Homework.id == -1)
        else:
            # 学生只能查看自己的作业
            logger.info(f"学生 {current_user.username} 查看自己的作业")
            query = query.filter(Homework.student_id == current_user.id)
        
        # 应用过滤器
        if status:
            query = query.filter(Homework.status == status)
        if assignment_id:
            query = query.filter(Homework.assignment_id == assignment_id)
        
        # 处理作业任务ID列表过滤
        if assignment_ids:
            try:
                # 将逗号分隔的ID字符串转换为整数列表
                assignment_id_list = [int(id_str) for id_str in assignment_ids.split(',') if id_str.strip()]
                if assignment_id_list:
                    query = query.filter(Homework.assignment_id.in_(assignment_id_list))
            except ValueError:
                # 如果转换失败，忽略此过滤条件
                logger.warning(f"Invalid assignment_ids parameter: {assignment_ids}")
        
        # 如果不显示所有版本，则只获取每个学生每个作业任务的最新提交
        if not show_all_versions:
            # 创建子查询，获取每个学生每个作业任务的最新提交ID
            from sqlalchemy import func
            subquery = db.query(
                Homework.student_id,
                Homework.assignment_id,
                func.max(Homework.created_at).label("latest_submission")
            ).group_by(
                Homework.student_id,
                Homework.assignment_id
            ).subquery()
            
            # 使用子查询过滤主查询
            query = query.join(
                subquery,
                (Homework.student_id == subquery.c.student_id) &
                (Homework.assignment_id == subquery.c.assignment_id) &
                (Homework.created_at == subquery.c.latest_submission)
            )
            
            logger.info("只显示每个学生每个作业任务的最新提交")
        
        # 计算总数
        total = query.count()
        logger.info(f"查询到的作业总数: {total}")
        
        # 分页 - 确保按照created_at降序排序，显示最新提交的作业
        query = query.order_by(Homework.created_at.desc())
        query = query.offset((page - 1) * limit).limit(limit)
        
        # 执行查询
        homeworks = query.all()
        logger.info(f"返回的作业数量: {len(homeworks)}")
        
        # 记录作业ID和提交时间，便于调试
        for hw in homeworks:
            logger.info(f"作业ID: {hw.id}, 学生ID: {hw.student_id}, 作业任务ID: {hw.assignment_id}, 提交时间: {hw.created_at}")
        
        # 获取详情
        results = []
        for homework in homeworks:
            student = db.query(User).filter(User.id == homework.student_id).first()
            student_name = student.full_name or student.username if student else "未知学生"

            assignment_title = None
            class_name = None
            subject_name = None

            # 优先从作业直接关联的班级获取班级信息
            if homework.class_id:
                from ..models.user import Class
                class_ = db.query(Class).filter(Class.id == homework.class_id).first()
                if class_:
                    class_name = class_.name

            # 如果没有直接关联的班级，尝试从作业任务获取班级信息和科目信息
            if homework.assignment_id:
                from ..models.homework import HomeworkAssignment
                from ..models.user import Class
                from ..models.subject import Subject
                assignment = db.query(HomeworkAssignment).filter(
                    HomeworkAssignment.id == homework.assignment_id
                ).first()
                if assignment:
                    assignment_title = assignment.title

                    # 获取班级信息
                    if not class_name and assignment.class_id:
                        class_ = db.query(Class).filter(Class.id == assignment.class_id).first()
                        if class_:
                            class_name = class_.name

                    # 获取科目信息
                    if homework.subject_id:
                        subject = db.query(Subject).filter(Subject.id == homework.subject_id).first()
                        if subject:
                            subject_name = subject.name
            
            # 计算同一学生同一作业任务的提交次数
            version_count = 1
            if homework.student_id and homework.assignment_id:
                version_count = db.query(Homework).filter(
                    Homework.student_id == homework.student_id,
                    Homework.assignment_id == homework.assignment_id
                ).count()
            
            # 构建响应对象
            homework_dict = homework.__dict__.copy()
            
            # 添加 corrected_at 字段，映射自 graded_at
            if homework.graded_at:
                homework_dict["corrected_at"] = homework.graded_at
            
            # 使用简化的方法创建对象，避免字典展开的问题
            try:
                homework_obj = homework_schema.HomeworkWithDetails(
                    id=homework.id,
                    title=homework.title,
                    description=homework.description,
                    class_id=homework.class_id,
                    school_id=homework.school_id,
                    subject_id=homework.subject_id,
                    student_id=homework.student_id,
                    assignment_id=homework.assignment_id,
                    status=homework.status,
                    score=homework.score,
                    accuracy=homework.accuracy,
                    homework_comment=homework.homework_comment,
                    created_at=homework.created_at,
                    graded_at=homework.graded_at,
                    images=homework.images or [],
                    corrections=homework.corrections or [],
                    wrong_questions=homework.wrong_questions or [],
                    annotated_images=homework.annotated_images or [],
                    student_name=student_name,
                    assignment_title=assignment_title,
                    class_name=class_name,
                    subject_name=subject_name,
                    version_count=version_count,
                    corrected_at=homework.graded_at
                )
                results.append(homework_obj)
            except Exception as e:
                logger.error(f"创建HomeworkWithDetails失败: {str(e)}")
                logger.error(f"homework数据: id={homework.id}, title={homework.title}, subject_id={homework.subject_id}")
                raise
        
        # 返回包含分页信息的响应
        return results
    except Exception as e:
        logger.error(f"获取作业列表时出错: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"获取作业列表失败: {str(e)}")

@router.post("/homework/batch", status_code=status.HTTP_201_CREATED)
async def batch_upload_homework(
    title: str = Form(...),
    description: Optional[str] = Form(None),
    assignment_id: Optional[int] = Form(None),
    class_id: Optional[int] = Form(None),
    student_data: str = Form(...),  # JSON字符串，格式为[{"student_id": 1, "student_name": "姓名", "images": ["image1.jpg", "image2.jpg"]}]
    files: List[UploadFile] = File(...),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # 记录请求信息，便于调试
    logger.info(f"接收到批量作业提交请求: 标题={title}, 作业任务ID={assignment_id}, 班级ID={class_id}, 文件数量={len(files)}, 用户ID={current_user.id}")

    # 初始化批改队列，用于收集所有需要批改的作业ID
    homework_ids_for_batch_correction = []

    # 验证是否为教师
    if not current_user.is_teacher:
        raise HTTPException(status_code=403, detail="只有教师可以批量上传作业")
    
    # 解析学生数据
    try:
        student_data_list = json.loads(student_data)
        print(f"解析到的学生数据: {student_data_list}")
    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="学生数据格式无效")
    
    # 保存上传的文件
    uploaded_files = {}
    for file in files:
        try:
            # 生成唯一文件名
            filename = file.filename
            if filename is None:
                filename = "unnamed_file"
            file_extension = os.path.splitext(filename)[1]
            file_name = f"{uuid4()}{file_extension}"
            
            # 确保上传目录存在
            os.makedirs(UPLOAD_DIR, exist_ok=True)
            
            # 构建相对URL路径和文件系统路径
            relative_url = f"{IMAGE_BASE_URL}/{file_name}"
            absolute_path = os.path.join(UPLOAD_DIR, file_name)
            
            # 保存文件
            with open(absolute_path, "wb") as buffer:
                buffer.write(await file.read())
            
            # 记录原始文件名到URL路径的映射
            uploaded_files[filename] = relative_url
            print(f"保存文件: {filename} -> {relative_url}")
        except Exception as e:
            print(f"保存文件 {file.filename} 时出错: {str(e)}")
            continue
    
    # 检查是否有作业任务，如果有，获取班级ID
    assignment_class_id = None
    if assignment_id:
        from ..models.homework import HomeworkAssignment
        assignment = db.query(HomeworkAssignment).filter(
            HomeworkAssignment.id == assignment_id
        ).first()
        if assignment:
            assignment_class_id = assignment.class_id
    
    # 使用提供的班级ID或作业任务中的班级ID
    effective_class_id = class_id or assignment_class_id
    print(f"有效班级ID: {effective_class_id}")
    
    # 批量创建作业
    created_homeworks = []
    for item in student_data_list:
        student_id = item.get("student_id")
        student_name = item.get("student_name", "未知学生")
        image_files = item.get("images", [])
        
        # 如果没有提供student_id，尝试从文件名匹配学生
        if not student_id and effective_class_id:
            try:
                # 获取班级学生列表
                from ..models.user import Class, UserClass
                class_students_query = (
                    db.query(User)
                    .join(UserClass, UserClass.user_id == User.id)
                    .filter(UserClass.class_id == effective_class_id)
                    .all()
                )
                
                # 从文件名匹配学生
                for image_name in image_files:
                    # 从文件名提取潜在的学生姓名（去掉扩展名）
                    name_without_ext = os.path.splitext(image_name)[0]
                    
                    # 尝试匹配学生
                    best_match = None
                    best_score = 0
                    
                    for student in class_students_query:
                        full_name = student.full_name or ""
                        username = student.username or ""
                        
                        # 完全匹配
                        if name_without_ext == full_name or name_without_ext == username:
                            student_id = student.id
                            student_name = full_name or username
                            best_score = 100
                            break
                        # 部分匹配
                        elif full_name in name_without_ext and len(full_name) > 1:
                            score = len(full_name)
                            if score > best_score:
                                best_score = score
                                best_match = student
                        elif username in name_without_ext and len(username) > 1:
                            score = len(username)
                            if score > best_score:
                                best_score = score
                                best_match = student
                    
                    # 使用最佳匹配
                    if not student_id and best_match and best_score > 1:
                        student_id = best_match.id
                        student_name = best_match.full_name or best_match.username
                        break
                    
            except Exception as e:
                print(f"从文件名匹配学生时出错: {str(e)}")
        
        # 如果仍然没有学生ID，则跳过此项
        if not student_id:
            print(f"警告: 无法为文件 {', '.join(image_files)} 找到匹配的学生，跳过此项")
            continue
        
        try:
            # 获取作业任务信息，用于填充 subject_id 和 school_id
            assignment = None
            if assignment_id:
                assignment = db.query(HomeworkAssignment).filter(HomeworkAssignment.id == assignment_id).first()

            # 创建作业记录（使用北京时间）
            from ..services.ai_service import get_beijing_time
            db_homework = Homework(
                title=title,
                description=description,
                student_id=student_id,
                assignment_id=assignment_id,
                class_id=effective_class_id,  # 保存班级ID
                school_id=assignment.school_id if assignment else current_user.school_id,
                subject_id=assignment.subject_id if assignment else None,
                status="submitted",
                created_at=get_beijing_time()  # 使用北京时间替代默认的UTC时间
            )
            db.add(db_homework)
            db.commit()
            db.refresh(db_homework)
            
            # 处理图片
            image_details = []
            
            for i, image_name in enumerate(image_files):
                # 查找对应的上传文件路径
                if image_name in uploaded_files:
                    file_path = uploaded_files[image_name]
                    
                    # 创建图片记录
                    db_image = HomeworkImage(
                        homework_id=db_homework.id,
                        image_path=file_path,
                        page_number=i+1
                    )
                    db.add(db_image)
                    
                    # 添加图片详情用于返回
                    image_details.append({
                        "page_number": i+1,
                        "image_path": file_path,
                        "original_name": image_name
                    })
            
            db.commit()
            
            # 创建后台任务处理批改，但不在上传过程中执行
            async def process_homework_batch_background(homework_id):
                logger.info(f"🚀 批量作业后台任务开始执行，作业ID={homework_id}")
                try:
                    # 等待一点时间确保数据库事务已提交
                    logger.info(f"⏰ 等待2秒确保数据库事务提交...")
                    await asyncio.sleep(2)

                    # 创建新的数据库会话用于后台任务
                    from ..database import SessionLocal
                    db_session = SessionLocal()
                    logger.info(f"📊 创建新的数据库会话")

                    try:
                        # 恢复AI批改过程
                        logger.info(f"🤖 开始批量作业AI批改过程，作业ID={homework_id}")
                        await ai_service.process_homework(int(homework_id), db_session)
                        logger.info(f"✅ 批量作业AI批改过程完成，作业ID={homework_id}")
                    finally:
                        db_session.close()
                        logger.info(f"🔒 关闭数据库会话")

                except Exception as e:
                    logger.error(f"❌ 批量作业后台处理失败: {str(e)}")
                    import traceback
                    logger.error(traceback.format_exc())

            # 将作业ID添加到批改队列中，稍后统一并发处理
            homework_ids_for_batch_correction.append(db_homework.id)
            
            # 获取学生信息，用于返回
            student = db.query(User).filter(User.id == student_id).first()
            retrieved_student_name = student_name
            if student:
                retrieved_student_name = student.full_name or student.username
            
            # 获取班级名称
            class_name = None
            if effective_class_id:
                from ..models.user import Class
                class_ = db.query(Class).filter(Class.id == effective_class_id).first()
                if class_:
                    class_name = class_.name
            
            # 添加到已创建作业列表
            created_homeworks.append({
                "id": db_homework.id,
                "title": title,
                "student_id": student_id,
                "student_name": retrieved_student_name,
                "class_id": effective_class_id,
                "class_name": class_name,
                "images": image_details,
                "images_count": len(image_details)
            })
        except Exception as e:
            print(f"为学生 {student_id} 创建作业时出错: {str(e)}")
            db.rollback()

    # 🚀 启动多作业并发批改
    if homework_ids_for_batch_correction:
        logger.info(f"⚡ 检测到 {len(homework_ids_for_batch_correction)} 个作业，启用多作业并发批改模式")

        # 创建并发批改任务
        async def process_multiple_homeworks_concurrently():
            """并发处理多个作业的批改"""
            import asyncio
            from ..database import SessionLocal

            logger.info(f"🚀 启动多作业并发批改，共 {len(homework_ids_for_batch_correction)} 个作业")
            start_time = time.time()

            # 创建并发任务
            tasks = []
            for homework_id in homework_ids_for_batch_correction:
                async def process_single_homework(hw_id):
                    """处理单个作业的批改"""
                    logger.info(f"🖼️ 开始并发处理作业 {hw_id}")
                    try:
                        # 等待一点时间确保数据库事务已提交
                        await asyncio.sleep(1)

                        # 创建新的数据库会话
                        db_session = SessionLocal()
                        try:
                            # 调用AI批改
                            await ai_service.process_homework(int(hw_id), db_session)
                            logger.info(f"✅ 作业 {hw_id} 并发批改完成")
                            return True
                        finally:
                            db_session.close()
                    except Exception as e:
                        logger.error(f"❌ 作业 {hw_id} 并发批改失败: {str(e)}")
                        return False

                task = process_single_homework(homework_id)
                tasks.append(task)

            # 并发执行所有批改任务
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 统计结果
            success_count = sum(1 for result in results if result is True)
            end_time = time.time()

            logger.info(f"📊 多作业并发批改完成，成功处理 {success_count}/{len(homework_ids_for_batch_correction)} 个作业")
            logger.info(f"🎉 多作业并发批改总耗时: {end_time - start_time:.2f} 秒")

        # 创建后台任务执行并发批改
        import time
        asyncio.create_task(process_multiple_homeworks_concurrently())
        logger.info(f"🎯 多作业并发批改任务已启动")
    else:
        logger.info(f"📝 没有作业需要批改")

    return {
        "message": "批量上传作业成功",
        "homeworks": created_homeworks
    }

@router.put("/homework/{homework_id}", response_model=homework_schema.Homework)
async def update_homework(
    homework_id: int,
    homework_data: homework_schema.HomeworkUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # 获取作业
    db_homework = db.query(Homework).filter(Homework.id == homework_id).first()
    if not db_homework:
        raise HTTPException(status_code=404, detail="作业不存在")
    
    # 检查权限（只有老师可以更新作业状态）
    if not current_user.is_teacher:
        raise HTTPException(status_code=403, detail="只有教师可以更新作业状态")
    
    # 更新作业信息
    if homework_data.status:
        db_homework.status = homework_data.status
    if homework_data.score is not None:
        db_homework.score = homework_data.score
    if homework_data.accuracy is not None:
        db_homework.accuracy = homework_data.accuracy
    
    # 如果状态变更为已批改，则更新批改时间
    if homework_data.status == "graded":
        db_homework.graded_at = datetime.utcnow()
    
    db.commit()
    db.refresh(db_homework)
    
    return db_homework

@router.delete("/homework/{homework_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_homework(
    homework_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """删除作业"""
    # 获取作业
    db_homework = db.query(Homework).filter(Homework.id == homework_id).first()
    if not db_homework:
        raise HTTPException(status_code=404, detail="作业不存在")
    
    # 检查权限（教师可以删除所有作业，学生只能删除自己的作业）
    if not current_user.is_teacher and db_homework.student_id != current_user.id:
        raise HTTPException(status_code=403, detail="没有权限删除此作业")
    
    try:
        # 首先删除相关联的数据
        # 删除作业图片
        db.query(HomeworkImage).filter(HomeworkImage.homework_id == homework_id).delete()
        # 删除批改记录
        db.query(HomeworkCorrection).filter(HomeworkCorrection.homework_id == homework_id).delete()
        # 删除错题记录
        db.query(WrongQuestion).filter(WrongQuestion.homework_id == homework_id).delete()
        
        # 最后删除作业本身
        db.delete(db_homework)
        db.commit()
        
        return {"detail": "作业已删除"} 
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除作业时发生错误: {str(e)}"
        )

@router.post("/homework-assignment")
async def create_homework_assignment(
    assignment: homework_schema.HomeworkAssignmentCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # 检查权限（只有教师可以创建作业任务）
    if not current_user.is_teacher:
        raise HTTPException(status_code=403, detail="只有教师可以创建作业任务")
    
    try:
        # 记录接收到的数据
        try:
            # 尝试使用新API
            assignment_data = assignment.model_dump()
        except AttributeError:
            # 如果不存在，使用旧API
            assignment_data = assignment.dict()
        
        # 添加更详细的日志记录
        logger.info(f"接收到作业任务创建请求: {assignment_data}")
        logger.info(f"当前用户ID: {current_user.id}, 用户名: {current_user.username}")
        
        # 记录AI配置信息
        if assignment.ai_config_id:
            ai_config = db.query(AIModelConfig).filter(AIModelConfig.id == assignment.ai_config_id).first()
            if ai_config:
                logger.info(f"找到AI配置: ID={ai_config.id}, 提供商={ai_config.provider}, 模型={ai_config.model_name}")
            else:
                logger.warning(f"未找到ID为{assignment.ai_config_id}的AI配置")
        
        # 确定school_id
        school_id = assignment.school_id  # 优先使用传入的school_id
        
        # 如果没有指定school_id但指定了class_id，则从班级获取school_id
        if school_id is None and assignment.class_id:
            from ..models.user import Class
            class_ = db.query(Class).filter(Class.id == assignment.class_id).first()
            if class_ and class_.school_id:
                school_id = class_.school_id
                logger.info(f"从班级ID {assignment.class_id} 获取到学校ID: {school_id}")
            else:
                logger.warning(f"未找到班级ID {assignment.class_id} 或班级没有关联学校ID")
        
        # 如果仍然没有school_id，尝试从用户获取
        if school_id is None and current_user.school_id:
            school_id = current_user.school_id
            logger.info(f"从用户获取到学校ID: {school_id}")
        else:
            logger.warning(f"无法确定school_id: 用户school_id={current_user.school_id}")
        
        # 记录所有字段的值
        logger.info(f"创建作业任务字段值: title={assignment.title}, description={assignment.description}, teacher_id={current_user.id}, correction_mode={assignment.correction_mode}, pattern_provider={assignment.pattern_provider}, ai_config_id={assignment.ai_config_id}, school_id={school_id}, subject_id={assignment.subject_id}")
        
        # 创建作业任务
        db_assignment = HomeworkAssignment(
            title=assignment.title,
            description=assignment.description,
            teacher_id=current_user.id,
            correction_mode=assignment.correction_mode,
            pattern_provider=assignment.pattern_provider,
            ai_config_id=assignment.ai_config_id,
            school_id=school_id,  # 添加school_id
            subject_id=assignment.subject_id,  # 添加subject_id
            auto_correct_description=assignment.auto_correct_description,  # 添加auto_correct_description
            reference_description=assignment.reference_description  # 添加reference_description
        )
        
        # 如果有指定班级，关联该班级
        if assignment.class_id:
            db_assignment.class_id = assignment.class_id
        
        # 设置截止日期
        if assignment.due_date:
            db_assignment.due_date = assignment.due_date
        
        logger.info("准备添加作业任务到数据库")
        db.add(db_assignment)
        logger.info("准备提交数据库事务")
        db.commit()
        logger.info(f"数据库事务提交成功，作业任务ID: {db_assignment.id}")
        db.refresh(db_assignment)
        
        # 获取班级名称
        class_name = None
        if db_assignment.class_id:
            from ..models.user import Class
            class_ = db.query(Class).filter(Class.id == db_assignment.class_id).first()
            if class_:
                class_name = class_.name
        
        # 打印成功信息
        logger.info(f"作业任务创建成功, ID: {db_assignment.id}, 标题: {db_assignment.title}")
        
        # 使用字典返回响应，而不是使用Pydantic模型
        return {
            "id": db_assignment.id,
            "title": db_assignment.title,
            "description": db_assignment.description,
            "teacher_id": db_assignment.teacher_id,
            "class_id": db_assignment.class_id,
            "correction_mode": db_assignment.correction_mode,
            "pattern_provider": db_assignment.pattern_provider,
            "ai_config_id": db_assignment.ai_config_id,
            "created_at": db_assignment.created_at,
            "due_date": db_assignment.due_date,
            "school_id": db_assignment.school_id,
            "subject_id": db_assignment.subject_id,
            "status": "active",
            "class_name": class_name,
            "teacher_name": current_user.full_name or current_user.username
        }
    except Exception as e:
        db.rollback()
        logger.error(f"创建作业任务失败: {str(e)}")
        logger.error(f"异常类型: {type(e).__name__}")
        logger.error(f"异常详情: {traceback.format_exc()}")
        # 记录请求数据
        logger.error(f"请求数据: {assignment}")
        
        # 检查特定的错误类型并提供更具体的错误信息
        if "ai_config_id" in str(e).lower():
            logger.error(f"AI配置ID错误: {assignment.ai_config_id}")
            # 检查AI配置是否存在
            try:
                ai_config = db.query(AIModelConfig).filter(AIModelConfig.id == assignment.ai_config_id).first()
                if ai_config:
                    logger.error(f"AI配置存在但可能有其他问题: {ai_config.id}, {ai_config.provider}, {ai_config.model_name}")
                else:
                    logger.error(f"找不到ID为{assignment.ai_config_id}的AI配置")
            except Exception as config_e:
                logger.error(f"检查AI配置时出错: {str(config_e)}")
        
        # 为前端返回更友好的错误信息
        raise HTTPException(status_code=500, detail=f"创建作业任务失败: {str(e)}")

@router.get("/student/homework-assignments")
async def get_student_homework_assignments(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取学生的作业任务列表，包含提交状态和批改状态"""
    try:
        logger.info(f"获取学生作业任务列表: user_id={current_user.id}")

        # 学生只能查看自己班级的作业任务
        # 首先获取学生所在的班级
        from ..models.class_model import ClassStudent
        student_classes = db.query(ClassStudent).filter(ClassStudent.student_id == current_user.id).all()

        if not student_classes:
            logger.info(f"学生 {current_user.id} 未分配到任何班级")
            return []

        class_ids = [sc.class_id for sc in student_classes]
        logger.info(f"学生所在班级ID: {class_ids}")

        # 获取这些班级的所有作业任务
        from ..models.homework import HomeworkAssignment
        from ..models.subject import Subject

        assignments_query = db.query(
            HomeworkAssignment,
            Subject.name.label('subject_name')
        ).outerjoin(
            Subject, HomeworkAssignment.subject_id == Subject.id
        ).filter(
            HomeworkAssignment.class_id.in_(class_ids)
        ).order_by(HomeworkAssignment.created_at.desc())

        assignments = assignments_query.all()

        # 为每个作业任务添加学生的提交状态和批改状态
        result = []
        for assignment, subject_name in assignments:
            # 查找学生对该作业任务的提交记录
            student_homework = db.query(Homework).filter(
                Homework.assignment_id == assignment.id,
                Homework.student_id == current_user.id
            ).order_by(Homework.created_at.desc()).first()

            # 构建返回数据
            assignment_data = {
                "id": assignment.id,
                "title": assignment.title,
                "description": assignment.description,
                "subject_name": subject_name,
                "due_date": assignment.due_date.isoformat() if assignment.due_date else None,
                "created_at": assignment.created_at.isoformat(),
                "status": assignment.status,
                # 学生提交状态
                "submission_status": "已提交" if student_homework else "未提交",
                "submitted_at": student_homework.created_at.isoformat() if student_homework else None,
                # 批改状态
                "grading_status": "未批改",
                "graded_at": None,
                "score": None,
                "accuracy": None,
                "homework_id": None
            }

            if student_homework:
                assignment_data["homework_id"] = student_homework.id
                if student_homework.status == "submitted":
                    assignment_data["grading_status"] = "待批改"
                elif student_homework.status == "grading":
                    assignment_data["grading_status"] = "批改中"
                elif student_homework.status == "graded":
                    assignment_data["grading_status"] = "已批改"
                    assignment_data["graded_at"] = student_homework.graded_at.isoformat() if student_homework.graded_at else None
                    assignment_data["score"] = student_homework.score
                    assignment_data["accuracy"] = student_homework.accuracy

            result.append(assignment_data)

        logger.info(f"返回 {len(result)} 个作业任务")
        return result

    except Exception as e:
        logger.error(f"获取学生作业任务列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取作业任务列表失败: {str(e)}")

@router.get("/homework-assignment")
async def get_homework_assignments(
    class_id: Optional[int] = None,
    page: int = 1,
    limit: int = 200,
    search: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取作业任务列表，基于角色权限控制"""
    # 应用统一的权限控制
    if not (current_user.is_admin or current_user.is_teacher):
        raise HTTPException(status_code=403, detail="没有权限查看作业任务列表")

    try:
        # 根据用户角色确定权限上下文
        # 这是"作业管理"菜单，使用homework_management上下文
        context = "homework_management"

        # 获取用户可访问的作业任务ID列表
        accessible_assignment_ids = await PermissionService.get_accessible_assignment_ids(current_user, db, context)

        if not accessible_assignment_ids:
            # 如果没有可访问的作业任务，返回空列表
            return []

        # 使用原生SQL查询
        from sqlalchemy import text

        # 构建基本查询，只查询用户有权限访问的作业任务
        id_placeholders = ','.join([':id_' + str(i) for i in range(len(accessible_assignment_ids))])
        sql_query = f"""
        SELECT
            ha.id, ha.title, ha.description, ha.class_id, ha.teacher_id,
            ha.due_date, ha.created_at, ha.correction_mode, ha.pattern_provider,
            ha.ai_config_id, ha.school_id, ha.subject_id,
            c.name as class_name, u.full_name as teacher_name,
            s.name as subject_name
        FROM
            homework_assignments ha
        LEFT JOIN
            classes c ON ha.class_id = c.id
        LEFT JOIN
            users u ON ha.teacher_id = u.id
        LEFT JOIN
            subjects s ON ha.subject_id = s.id
        WHERE ha.id IN ({id_placeholders})
        """

        # 参数字典
        params = {f'id_{i}': assignment_id for i, assignment_id in enumerate(accessible_assignment_ids)}
        
        # 应用过滤器
        if class_id:
            sql_query += " AND ha.class_id = :class_id"
            params["class_id"] = class_id
        
        # 添加搜索过滤
        if search:
            sql_query += " AND ha.title LIKE :search"
            params["search"] = f"%{search}%"
        
        # 添加日期过滤
        if start_date and end_date:
            try:
                start_date_obj = datetime.strptime(start_date, "%Y-%m-%d")
                end_date_obj = datetime.strptime(end_date, "%Y-%m-%d")
                # 结束日期加一天，使其包含整个结束日
                end_date_obj = end_date_obj + timedelta(days=1)
                
                sql_query += " AND ha.created_at >= :start_date AND ha.created_at < :end_date"
                params["start_date"] = start_date_obj
                params["end_date"] = end_date_obj
            except ValueError:
                print(f"日期格式错误: start_date={start_date}, end_date={end_date}")
        
        # 添加排序和分页
        sql_query += " ORDER BY ha.created_at DESC LIMIT :limit OFFSET :offset"
        params["limit"] = limit
        params["offset"] = (page - 1) * limit
        
        # 执行查询
        result = db.execute(text(sql_query), params)
        assignments = result.fetchall()
        
        # 转换为字典列表
        results = []
        for row in assignments:
            # 从描述中提取状态信息
            status = "active"  # 默认状态
            if row.description and "【状态】" in row.description:
                # 使用正则表达式提取状态
                status_match = re.search(r"【状态】(.*?)】", row.description)
                if status_match:
                    status = status_match.group(1)
            
            # 处理可能为空的字段
            class_name = row.class_name if row.class_name else "未分配班级"
            teacher_name = row.teacher_name if row.teacher_name else "未知教师"
            subject_name = row.subject_name if row.subject_name else "未指定"
            
            # 创建字典对象
            result_dict = {
                "id": row.id,
                "title": row.title,
                "description": row.description,
                "class_id": row.class_id,
                "teacher_id": row.teacher_id,
                "due_date": row.due_date,
                "created_at": row.created_at,
                "correction_mode": row.correction_mode,
                "pattern_provider": row.pattern_provider,
                "ai_config_id": row.ai_config_id,
                "school_id": row.school_id,
                "subject_id": row.subject_id,
                "status": status,
                "class_name": class_name,
                "teacher_name": teacher_name,
                "subject_name": subject_name
            }
            
            # 添加到结果列表
            results.append(result_dict)
        
        # 返回结果
        return results
    except Exception as e:
        logger.error(f"获取作业任务列表失败: {str(e)}")
        logger.error(f"异常详情: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"获取作业任务列表失败: {str(e)}")

@router.get("/homework-assignment/{assignment_id}")
async def get_homework_assignment(
    assignment_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取单个作业任务详情"""
    # 获取作业任务
    from ..models.homework import HomeworkAssignment
    assignment = db.query(HomeworkAssignment).filter(HomeworkAssignment.id == assignment_id).first()
    
    # 检查是否存在
    if not assignment:
        raise HTTPException(status_code=404, detail="作业任务不存在或已被删除")
    
    # 获取班级名称
    class_name = None
    if assignment.class_id is not None:
        from ..models.user import Class
        class_ = db.query(Class).filter(Class.id == assignment.class_id).first()
        if class_:
            class_name = class_.name
        else:
            class_name = f"未知班级 (ID: {assignment.class_id})"
    else:
        class_name = "未分配班级"
    
    # 获取教师名称
    teacher = db.query(User).filter(User.id == assignment.teacher_id).first()
    teacher_name = teacher.full_name or teacher.username if teacher else "未知教师"
    
    # 从描述中提取状态信息
    status = "active"  # 默认状态
    if assignment.description and "【状态】" in assignment.description:
        # 使用正则表达式提取状态
        status_match = re.search(r"【状态】(.*?)】", assignment.description)
        if status_match:
            status = status_match.group(1)
    
    # 获取科目名称
    subject_name = None
    if assignment.subject_id:
        from ..models.subject import Subject
        subject = db.query(Subject).filter(Subject.id == assignment.subject_id).first()
        if subject:
            subject_name = subject.name
    
    # 构建响应
    result = {
        "id": assignment.id,
        "title": assignment.title,
        "description": assignment.description,
        "class_id": assignment.class_id,
        "teacher_id": assignment.teacher_id,
        "due_date": assignment.due_date,
        "created_at": assignment.created_at,
        "correction_mode": assignment.correction_mode,
        "pattern_provider": assignment.pattern_provider,
        "ai_config_id": assignment.ai_config_id,
        "school_id": assignment.school_id,
        "subject_id": assignment.subject_id,
        "status": status,
        "class_name": class_name,
        "teacher_name": teacher_name,
        "subject": subject_name or "未指定"  # 添加科目名称
    }
    
    return result

@router.patch("/homework-assignment/{assignment_id}/status", response_model=homework_schema.HomeworkAssignment)
async def update_homework_assignment_status(
    assignment_id: int,
    status_update: homework_schema.HomeworkAssignmentStatus,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """更新作业任务状态"""
    # 检查权限（只有教师可以更新作业任务状态）
    if not current_user.is_teacher:
        raise HTTPException(status_code=403, detail="只有教师可以更新作业任务状态")
    
    # 获取作业任务
    from ..models.homework import HomeworkAssignment
    assignment = db.query(HomeworkAssignment).filter(HomeworkAssignment.id == assignment_id).first()
    
    # 检查是否存在
    if not assignment:
        raise HTTPException(status_code=404, detail="作业任务不存在或已被删除")
    
    # 由于数据库表中没有status字段，我们将使用其他方法来标记作业任务状态
    # 例如，可以将其添加到描述中，或者创建一个单独的表来存储状态
    try:
        # 将状态添加到描述中
        status = status_update.status
        if assignment.description:
            if "【状态】" in assignment.description:
                # 更新现有状态
                assignment.description = re.sub(r"【状态】.*?】", f"【状态】{status}】", assignment.description)
            else:
                # 添加状态标记
                assignment.description = f"{assignment.description}\n\n【状态】{status}】"
        else:
            # 如果没有描述，创建一个
            assignment.description = f"【状态】{status}】"
        
        db.commit()
        db.refresh(assignment)
        
        # 获取班级名称
        class_name = None
        if assignment.class_id is not None:
            from ..models.user import Class
            class_ = db.query(Class).filter(Class.id == assignment.class_id).first()
            if class_:
                class_name = class_.name
            else:
                class_name = f"未知班级 (ID: {assignment.class_id})"
        else:
            class_name = "未分配班级"
        
        # 获取教师名称
        teacher = db.query(User).filter(User.id == assignment.teacher_id).first()
        teacher_name = teacher.full_name or teacher.username if teacher else "未知教师"
        
        # 构建响应，手动添加status字段
        result_dict = assignment.__dict__.copy()
        result_dict["status"] = status  # 添加状态字段
        result_dict["class_name"] = class_name
        result_dict["teacher_name"] = teacher_name
        
        # 创建Pydantic模型
        result = homework_schema.HomeworkAssignment(**result_dict)
        
        return result
    except Exception as e:
        db.rollback()
        logger.error(f"更新作业任务状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新作业任务状态失败: {str(e)}")

@router.delete("/homework-assignment/{assignment_id}")
async def delete_homework_assignment(
    assignment_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # 检查权限（只有教师可以删除作业任务）
    if not current_user.is_teacher:
        raise HTTPException(status_code=403, detail="没有权限删除作业任务")
    
    # 获取作业任务
    from ..models.homework import HomeworkAssignment
    assignment = db.query(HomeworkAssignment).filter(HomeworkAssignment.id == assignment_id).first()
    if not assignment:
        raise HTTPException(status_code=404, detail="作业任务不存在")
    
    # 检查是否有关联的作业
    from ..models.homework import Homework
    homeworks = db.query(Homework).filter(Homework.assignment_id == assignment_id).all()
    
    # 删除关联的作业
    for homework in homeworks:
        # 删除作业图片
        for image in homework.images:
            # 删除文件
            try:
                if os.path.exists(image.image_path):
                    os.remove(image.image_path)
            except Exception as e:
                print(f"删除文件失败: {e}")
            
            # 删除数据库记录
            db.delete(image)
        
        # 删除批改记录
        for correction in homework.corrections:
            db.delete(correction)
        
        # 删除错题记录
        for wrong_question in homework.wrong_questions:
            db.delete(wrong_question)
        
        # 删除作业
        db.delete(homework)
    
    # 删除作业任务
    db.delete(assignment)
    db.commit()
    
    return JSONResponse(status_code=status.HTTP_204_NO_CONTENT, content={})

@router.post("/homework/{homework_id}/correct")
async def correct_homework(
    homework_id: int,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """手动触发作业批改"""
    logger.info(f"手动触发作业批改: id={homework_id}")
    
    homework = db.query(Homework).filter(Homework.id == homework_id).first()
    if not homework:
        logger.warning(f"作业不存在: id={homework_id}")
        raise HTTPException(status_code=404, detail="作业不存在")
    
    # 更新作业状态
    homework.status = "pending"
    db.commit()
    
    # 在后台启动作业处理
    background_tasks.add_task(process_homework, homework.id, db)
    logger.info(f"已添加作业处理后台任务: homework_id={homework.id}")
    
    return {"message": "作业批改已开始"}

@router.post("/homework/batch-correct")
async def batch_correct_homework(
    homework_ids: List[int],
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """批量触发作业批改"""
    logger.info(f"批量触发作业批改: ids={homework_ids}")
    
    for homework_id in homework_ids:
        homework = db.query(Homework).filter(Homework.id == homework_id).first()
        if homework:
            # 更新作业状态
            homework.status = "pending"
            db.commit()
            
            # 在后台启动作业处理
            background_tasks.add_task(process_homework, homework.id, db)
            logger.debug(f"已添加作业处理后台任务: homework_id={homework.id}")
        else:
            logger.warning(f"作业不存在: id={homework_id}")
    
    logger.info(f"开始批量作业AI批改过程，作业ID列表={homework_ids}")
    return {"message": f"已开始批改 {len(homework_ids)} 份作业"}

@router.get("/homework/{homework_id}/correction")
async def get_homework_correction(
    homework_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """获取作业批改结果"""
    logger.debug(f"获取作业批改结果: id={homework_id}")
    
    # 检查作业是否存在
    homework = db.query(Homework).filter(Homework.id == homework_id).first()
    if not homework:
        logger.warning(f"作业不存在: id={homework_id}")
        raise HTTPException(status_code=404, detail="作业不存在")
    
    # 获取批改结果
    corrections = db.query(HomeworkCorrection).filter(
        HomeworkCorrection.homework_id == homework_id
    ).all()
    
    if not corrections:
        logger.warning(f"作业批改结果不存在: id={homework_id}")
        raise HTTPException(status_code=404, detail="作业批改结果不存在")
    
    # 返回批改结果
    result = []
    for correction in corrections:
        try:
            correction_data = {
                "id": correction.id,
                "homework_id": correction.homework_id,
                "page_number": correction.page_number,
                "correction_data": correction.correction_data,
                "created_at": correction.created_at,
                "updated_at": correction.updated_at
            }
            result.append(correction_data)
        except Exception as e:
            logger.error(f"处理批改结果时出错: {str(e)}")
    
    logger.debug(f"返回作业批改结果: id={homework_id}, 页数={len(result)}")
    return result 

@router.get("/homework/{homework_id}/annotated-images")
async def get_annotated_images(
    homework_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """获取作业的带批注图片列表"""
    logger.debug(f"获取作业的带批注图片: id={homework_id}")

    # 检查作业是否存在
    homework = db.query(Homework).filter(Homework.id == homework_id).first()
    if not homework:
        logger.warning(f"作业不存在: id={homework_id}")
        raise HTTPException(status_code=404, detail="作业不存在")

    # 权限检查：只能查看自己的作业批注图片或者老师可以查看所有作业的批注图片
    logger.info(f"批注图片权限检查 - 作业ID:{homework_id}, 作业学生ID:{homework.student_id}, 当前用户ID:{current_user.id}, 是否教师:{current_user.is_teacher}")

    if homework.student_id != current_user.id and not current_user.is_teacher and not current_user.is_admin:
        logger.warning(f"权限拒绝 - 用户{current_user.id}({current_user.username})尝试访问学生{homework.student_id}的作业{homework_id}的批注图片")
        raise HTTPException(status_code=403, detail=f"没有权限查看此作业的批注图片。作业属于学生ID:{homework.student_id}，当前用户ID:{current_user.id}")

    logger.info(f"权限验证通过 - 用户{current_user.id}({current_user.username})访问作业{homework_id}的批注图片")

    # 获取批注图片
    annotated_images = db.query(HomeworkAnnotatedImage).filter(
        HomeworkAnnotatedImage.homework_id == homework_id
    ).all()
    
    # 返回批注图片列表
    result = []
    for image in annotated_images:
        result.append({
            "id": image.id,
            "homework_id": image.homework_id,
            "original_image_id": image.original_image_id,
            "image_path": image.image_path,
            "page_number": image.page_number,
            "created_at": image.created_at
        })
    
    logger.debug(f"返回作业批注图片: id={homework_id}, 数量={len(result)}")
    return result

@router.post("/homework/{homework_id}/generate-annotations")
async def generate_annotations(
    homework_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """为作业生成带批注的图片"""
    logger.info("🚨🚨🚨 进入generate_annotations路由函数 🚨🚨🚨")
    logger.info(f"开始为作业生成批注图片: id={homework_id}")
    
    # 检查作业是否存在
    homework = db.query(Homework).filter(Homework.id == homework_id).first()
    if not homework:
        logger.warning(f"作业不存在: id={homework_id}")
        raise HTTPException(status_code=404, detail="作业不存在")
    
    # 检查是否有批改结果
    corrections = db.query(HomeworkCorrection).filter(
        HomeworkCorrection.homework_id == homework_id
    ).all()
    
    if not corrections:
        logger.warning(f"作业没有批改结果: id={homework_id}")
        raise HTTPException(status_code=400, detail="作业没有批改结果，无法生成批注")
    
    # 获取作业图片
    images = db.query(HomeworkImage).filter(
        HomeworkImage.homework_id == homework_id
    ).all()
    
    if not images:
        logger.warning(f"作业没有图片: id={homework_id}")
        raise HTTPException(status_code=400, detail="作业没有图片，无法生成批注")
    
    # 删除现有的批注图片（如果有）
    existing_annotated_images = db.query(HomeworkAnnotatedImage).filter(
        HomeworkAnnotatedImage.homework_id == homework_id
    ).all()
    
    for annotated_image in existing_annotated_images:
        # 尝试删除文件
        try:
            if annotated_image.image_path.startswith("/uploads/"):
                file_path = annotated_image.image_path[9:]  # 去掉"/uploads/"前缀
                full_path = os.path.join(UPLOAD_DIR, file_path)
                if os.path.exists(full_path):
                    os.remove(full_path)
                    logger.info(f"删除旧的批注图片文件: {full_path}")
        except Exception as e:
            logger.error(f"删除旧的批注图片文件失败: {str(e)}")
        
        # 从数据库中删除记录
        db.delete(annotated_image)
    
    db.commit()
    logger.info(f"已删除 {len(existing_annotated_images)} 个旧的批注图片记录")
    
    # 为每个图片生成批注
    result = []
    for image in images:
        # 查找对应的批改结果
        correction = next((c for c in corrections if c.page_number == image.page_number), None)
        if not correction:
            logger.warning(f"作业 {homework_id} 第 {image.page_number} 页没有对应的批改结果")
            continue
        
        # 生成批注图片
        annotated_image_path = await ai_service.generate_annotated_image(
            homework_id=homework_id,
            image_id=image.id,
            page_number=image.page_number,
            original_image_path=image.image_path,
            correction_data=correction.correction_data,
            db=db
        )
        
        if annotated_image_path:
            result.append({
                "page_number": image.page_number,
                "original_image_path": image.image_path,
                "annotated_image_path": annotated_image_path
            })
    
    logger.info(f"成功为作业 {homework_id} 生成 {len(result)} 个批注图片")
    return {
        "success": True,
        "message": f"成功生成 {len(result)} 个批注图片",
        "annotated_images": result
    }



@router.get("/wrong-questions/{question_id}")
async def get_wrong_question(
    question_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取单个错题详情"""
    try:
        wrong_question = db.query(WrongQuestion).filter(WrongQuestion.id == question_id).first()
        if not wrong_question:
            raise HTTPException(status_code=404, detail="错题不存在")

        # 验证权限 - 学生只能查看自己的错题，管理员和教师可以查看所有错题
        if not current_user.is_admin and not current_user.is_teacher and wrong_question.student_id != current_user.id:
            raise HTTPException(status_code=403, detail="没有权限查看此错题")

        # 如果是教师，需要验证是否有权限查看该学生的错题
        if current_user.is_teacher and not current_user.is_admin:
            # 检查教师是否有权限查看该学生的错题（例如：是否教授该学生）
            student = db.query(User).filter(User.id == wrong_question.student_id).first()
            if student and student.school_id != current_user.school_id:
                raise HTTPException(status_code=403, detail="没有权限查看其他学校学生的错题")

        return {
            "id": wrong_question.id,
            "homework_id": wrong_question.homework_id,
            "question_type": wrong_question.question_type,
            "question_content": wrong_question.question_content,
            "correct_answer": wrong_question.correct_answer,
            "wrong_answer": wrong_question.wrong_answer,
            "analysis": wrong_question.analysis,
            "created_at": wrong_question.created_at.isoformat() if wrong_question.created_at else None
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取错题详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取错题详情失败: {str(e)}")

# 添加管理员错题查询API
@router.get("/admin/wrong-questions")
async def get_all_wrong_questions(
    student_id: Optional[int] = None,
    school_id: Optional[int] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """管理员获取所有学生的错题列表"""
    # 验证管理员权限
    if not current_user.is_admin:
        raise HTTPException(status_code=403, detail="需要管理员权限")

    try:
        logger.info(f"管理员 {current_user.id} 请求获取错题列表 - student_id: {student_id}, school_id: {school_id}")

        # 首先检查数据库中错题记录总数
        total_wrong_questions = db.query(WrongQuestion).count()
        logger.info(f"数据库中错题记录总数: {total_wrong_questions}")

        query = db.query(WrongQuestion)

        # 如果指定了学生ID，只查询该学生的错题
        if student_id:
            query = query.filter(WrongQuestion.student_id == student_id)
            logger.info(f"筛选学生ID: {student_id}")
            
        # 如果指定了学校ID，只查询该学校的学生错题
        if school_id:
            # 获取该学校的所有学生ID
            student_ids = db.query(User.id).filter(
                User.school_id == school_id,
                User.is_teacher == False,
                User.is_admin == False
            ).all()
            student_ids = [s[0] for s in student_ids]
            if student_ids:
                query = query.filter(WrongQuestion.student_id.in_(student_ids))
            else:
                # 如果学校没有学生，返回空列表
                return []
        # 如果当前管理员不是超级管理员，只能查看自己学校的错题
        elif not current_user.is_admin:
            # 获取管理员所属学校的所有学生ID
            student_ids = db.query(User.id).filter(
                User.school_id == current_user.school_id,
                User.is_teacher == False,
                User.is_admin == False
            ).all()
            student_ids = [s[0] for s in student_ids]
            if student_ids:
                query = query.filter(WrongQuestion.student_id.in_(student_ids))
            else:
                # 如果学校没有学生，返回空列表
                return []

        wrong_questions = query.order_by(WrongQuestion.created_at.desc()).all()

        # 返回详细信息，包括学生姓名
        result = []
        for wq in wrong_questions:
            student = db.query(User).filter(User.id == wq.student_id).first()
            result.append({
                "id": wq.id,
                "homework_id": wq.homework_id,
                "student_id": wq.student_id,
                "student_name": student.full_name if student else "未知学生",
                "question_type": wq.question_type,
                "question_content": wq.question_content,
                "correct_answer": wq.correct_answer,
                "wrong_answer": wq.wrong_answer,
                "analysis": wq.analysis,
                "created_at": wq.created_at.isoformat() if wq.created_at else None
            })

        logger.info(f"管理员 {current_user.id} 获取错题列表: 共 {len(result)} 条")
        return result

    except Exception as e:
        logger.error(f"管理员获取错题列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取错题列表失败: {str(e)}")

# 添加教师专用的错题查询API
@router.get("/teacher/wrong-questions")
async def get_teacher_wrong_questions(
    student_id: Optional[int] = None,
    class_id: Optional[int] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """教师获取所教班级学生的错题列表"""
    # 验证教师权限
    if not current_user.is_teacher:
        raise HTTPException(status_code=403, detail="需要教师权限")

    try:
        # 首先获取教师所教的班级ID列表
        teacher_class_ids = []
        
        # 获取教师所教的班级
        from ..models.user import ClassTeacher
        teacher_classes = db.query(ClassTeacher.class_id).filter(
            ClassTeacher.teacher_id == current_user.id
        ).all()
        teacher_class_ids = [c[0] for c in teacher_classes]
        
        # 如果指定了班级ID，验证该班级是否是该教师所教的班级
        if class_id:
            if class_id not in teacher_class_ids:
                raise HTTPException(status_code=403, detail="您没有权限查看该班级的错题")
            teacher_class_ids = [class_id]  # 只查询指定班级
        
        # 如果教师没有任何班级，返回空列表
        if not teacher_class_ids:
            return []
        
        # 获取班级中的学生ID
        student_ids = []
        from ..models.user import ClassStudent
        for class_id in teacher_class_ids:
            class_students = db.query(ClassStudent.student_id).filter(
                ClassStudent.class_id == class_id
            ).all()
            student_ids.extend([s[0] for s in class_students])
        
        # 如果指定了学生ID，验证该学生是否在教师所教的班级中
        if student_id:
            if student_id not in student_ids:
                raise HTTPException(status_code=403, detail="您没有权限查看该学生的错题")
            student_ids = [student_id]  # 只查询指定学生
        
        # 如果没有学生，返回空列表
        if not student_ids:
            return []
        
        # 查询这些学生的错题
        query = db.query(WrongQuestion).filter(
            WrongQuestion.student_id.in_(student_ids)
        ).order_by(WrongQuestion.created_at.desc())
        
        wrong_questions = query.all()
        
        # 返回详细信息，包括学生姓名
        result = []
        for wq in wrong_questions:
            student = db.query(User).filter(User.id == wq.student_id).first()
            result.append({
                "id": wq.id,
                "homework_id": wq.homework_id,
                "student_id": wq.student_id,
                "student_name": student.full_name if student else "未知学生",
                "question_type": wq.question_type,
                "question_content": wq.question_content,
                "correct_answer": wq.correct_answer,
                "wrong_answer": wq.wrong_answer,
                "analysis": wq.analysis,
                "created_at": wq.created_at.isoformat() if wq.created_at else None
            })
        
        logger.info(f"教师 {current_user.id} 获取错题列表: 共 {len(result)} 条")
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"教师获取错题列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取错题列表失败: {str(e)}")

# 强化训练记录相关路由
@router.get("/reinforcement-exercises")
async def get_reinforcement_exercises(
    wrong_question_id: Optional[int] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取强化训练记录列表"""
    try:
        query = db.query(ReinforcementExercise).filter(
            ReinforcementExercise.student_id == current_user.id
        )

        if wrong_question_id:
            query = query.filter(ReinforcementExercise.wrong_question_id == wrong_question_id)

        exercises = query.order_by(ReinforcementExercise.created_at.desc()).all()

        result = []
        for exercise in exercises:
            result.append({
                "id": exercise.id,
                "wrong_question_id": exercise.wrong_question_id,
                "student_id": exercise.student_id,
                "exercise_content": exercise.exercise_content,
                "answer": exercise.answer,
                "analysis": exercise.analysis,
                "is_completed": exercise.is_completed,
                "created_at": exercise.created_at.isoformat() if exercise.created_at else None,
                "completed_at": exercise.completed_at.isoformat() if exercise.completed_at else None
            })

        logger.info(f"获取学生 {current_user.id} 的强化训练记录: 共 {len(result)} 条")
        return result

    except Exception as e:
        logger.error(f"获取强化训练记录失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取强化训练记录失败: {str(e)}")

@router.put("/reinforcement-exercises/{exercise_id}")
async def update_reinforcement_exercise(
    exercise_id: int,
    is_completed: bool,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """更新强化训练记录状态"""
    try:
        exercise = db.query(ReinforcementExercise).filter(ReinforcementExercise.id == exercise_id).first()
        if not exercise:
            raise HTTPException(status_code=404, detail="强化训练记录不存在")

        # 验证权限
        if exercise.student_id != current_user.id:
            raise HTTPException(status_code=403, detail="没有权限修改此训练记录")

        # 更新状态
        exercise.is_completed = is_completed
        if is_completed:
            exercise.completed_at = datetime.utcnow()
        else:
            exercise.completed_at = None

        db.commit()
        db.refresh(exercise)

        return {
            "id": exercise.id,
            "wrong_question_id": exercise.wrong_question_id,
            "student_id": exercise.student_id,
            "exercise_content": exercise.exercise_content,
            "answer": exercise.answer,
            "analysis": exercise.analysis,
            "is_completed": exercise.is_completed,
            "created_at": exercise.created_at.isoformat() if exercise.created_at else None,
            "completed_at": exercise.completed_at.isoformat() if exercise.completed_at else None
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新强化训练记录失败: {str(e)}")
        db.rollback()
        raise HTTPException(status_code=500, detail=f"更新强化训练记录失败: {str(e)}")

@router.delete("/reinforcement-exercises/{exercise_id}")
async def delete_reinforcement_exercise(
    exercise_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """删除强化训练记录"""
    try:
        exercise = db.query(ReinforcementExercise).filter(ReinforcementExercise.id == exercise_id).first()
        if not exercise:
            raise HTTPException(status_code=404, detail="强化训练记录不存在")

        # 验证权限
        if exercise.student_id != current_user.id:
            raise HTTPException(status_code=403, detail="没有权限删除此训练记录")

        # 先删除相关的答题记录
        db.query(ExerciseAnswerRecord).filter(ExerciseAnswerRecord.exercise_id == exercise_id).delete()

        # 删除强化训练记录
        db.delete(exercise)
        db.commit()

        logger.info(f"学生 {current_user.id} 删除了强化训练记录 {exercise_id}")
        return {"message": "强化训练记录删除成功"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除强化训练记录失败: {str(e)}")
        db.rollback()
        raise HTTPException(status_code=500, detail=f"删除强化训练记录失败: {str(e)}")

# ==================== 作业任务学生列表API ====================

@router.get("/homework-assignments/{assignment_id}/students")
async def get_assignment_students(
    assignment_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取作业任务的学生列表
    """
    try:
        logger.info(f"用户 {current_user.id} 请求获取作业任务 {assignment_id} 的学生列表")

        # 获取作业任务
        assignment = db.query(HomeworkAssignment).filter(
            HomeworkAssignment.id == assignment_id
        ).first()

        if not assignment:
            raise HTTPException(status_code=404, detail="作业任务不存在")

        # 简化权限检查：只检查是否为教师或管理员
        if not (current_user.is_teacher or current_user.is_admin):
            raise HTTPException(status_code=403, detail="没有权限访问此作业任务")

        # 获取班级学生
        students = db.query(User).join(ClassStudent).filter(
            ClassStudent.class_id == assignment.class_id,
            User.is_active == 1
        ).all()

        # 构建返回数据
        student_list = []
        for student in students:
            # 检查是否已提交作业
            homework = db.query(Homework).filter(
                Homework.assignment_id == assignment_id,
                Homework.student_id == student.id
            ).first()

            student_list.append({
                "id": student.id,
                "username": student.username,
                "full_name": student.full_name,
                "has_submitted": homework is not None,
                "submission_time": homework.created_at.isoformat() if homework else None
            })

        logger.info(f"成功获取作业任务 {assignment_id} 的 {len(student_list)} 个学生")

        return {
            "success": True,
            "students": student_list,
            "total": len(student_list)
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取学生列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取学生列表失败: {str(e)}")

# 测试API端点
@router.get("/test-students/{assignment_id}")
async def test_get_students(assignment_id: int, db: Session = Depends(get_db)):
    """测试获取学生列表"""
    try:
        # 简单返回测试数据
        return {
            "success": True,
            "students": [
                {"id": 1, "username": "student1", "full_name": "张三", "has_submitted": True},
                {"id": 2, "username": "student2", "full_name": "李四", "has_submitted": False},
                {"id": 3, "username": "student3", "full_name": "王五", "has_submitted": True}
            ],
            "total": 3
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

