{"ast": null, "code": "var _jsxFileName = \"D:\\\\pythonproject\\\\checkingsys\\\\frontend\\\\src\\\\components\\\\PendingReviewHomeworks.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useRef } from 'react';\nimport { useNavigate, useSearchParams, useLocation } from 'react-router-dom';\nimport { Table, Tag, Button, Input, Select, DatePicker, Space, message, Alert, Empty, Spin, Typography, Popconfirm, List, Card, Row, Col, Avatar } from 'antd';\nimport { SearchOutlined, FileTextOutlined, CheckCircleOutlined, DeleteOutlined, SyncOutlined, ClockCircleOutlined, CalendarOutlined, UserOutlined } from '@ant-design/icons';\nimport { getHomeworks, deleteHomework, getHomeworkAssignments } from '../utils/api';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Option\n} = Select;\nconst {\n  RangePicker\n} = DatePicker;\nconst {\n  Title,\n  Text\n} = Typography;\n\n// 简单的防抖函数\nconst debounce = (fn, delay) => {\n  let timer = null;\n  return function (...args) {\n    if (timer) clearTimeout(timer);\n    timer = setTimeout(() => {\n      fn.apply(this, args);\n    }, delay);\n  };\n};\nconst PendingReviewHomeworks = ({\n  user\n}) => {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [searchParams] = useSearchParams();\n  const [loading, setLoading] = useState(false);\n  const [homeworks, setHomeworks] = useState([]);\n  const [assignments, setAssignments] = useState([]);\n  const [pagination, setPagination] = useState({\n    current: 1,\n    pageSize: 10,\n    total: 0\n  });\n  const [error, setError] = useState(null);\n  const [assignmentsLoading, setAssignmentsLoading] = useState(false);\n  const [lastSelectedAssignment, setLastSelectedAssignment] = useState(null);\n  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);\n\n  // 过滤条件\n  const [filters, setFilters] = useState({\n    status: '',\n    // 默认不过滤状态，显示所有进行中的作业\n    search: searchParams.get('search') || '',\n    dateRange: null,\n    assignmentId: null\n  });\n\n  // 监听窗口大小变化\n  useEffect(() => {\n    const handleResize = () => {\n      setIsMobile(window.innerWidth <= 768);\n    };\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, []);\n\n  // 获取作业任务列表\n  const fetchAssignments = async () => {\n    try {\n      setAssignmentsLoading(true);\n      console.log('开始获取作业任务列表');\n      const response = await getHomeworkAssignments();\n      console.log('获取作业任务列表响应:', response);\n      let assignmentsList = [];\n      if (response && response.items) {\n        assignmentsList = response.items;\n      } else if (Array.isArray(response)) {\n        assignmentsList = response;\n      }\n      console.log('解析后的作业任务列表:', assignmentsList);\n\n      // 处理作业任务数据，确保每个作业任务都有正确的status字段\n      const processedAssignments = assignmentsList.map(assignment => {\n        let status = assignment.status;\n\n        // 如果没有status字段，从description中提取状态\n        if (!status && assignment.description) {\n          // 使用正则表达式提取状态\n          const statusMatch = assignment.description.match(/【状态】(.*?)】/);\n          if (statusMatch) {\n            status = statusMatch[1];\n          } else {\n            status = 'active'; // 默认状态为active\n          }\n        } else if (!status) {\n          status = 'active'; // 默认状态为active\n        }\n        return {\n          ...assignment,\n          status: status\n        };\n      });\n      console.log('处理后的作业任务列表:', processedAssignments);\n\n      // 过滤已结束的作业任务，只保留未结束的作业任务\n      const activeAssignments = processedAssignments.filter(assignment => assignment.status !== 'finished');\n      console.log('未结束的作业任务:', activeAssignments);\n      setAssignments(activeAssignments);\n\n      // 如果有作业任务，默认选择第一个\n      if (activeAssignments.length > 0 && !filters.assignmentId) {\n        console.log('选择第一个作业任务:', activeAssignments[0]);\n        const firstAssignmentId = activeAssignments[0].id;\n\n        // 同时更新lastSelectedAssignment，避免重复刷新\n        setLastSelectedAssignment(firstAssignmentId);\n        setFilters(prev => ({\n          ...prev,\n          assignmentId: firstAssignmentId\n        }));\n\n        // 加载该作业任务的作业\n        fetchHomeworks({\n          assignment_id: firstAssignmentId\n        });\n      } else if (activeAssignments.length === 0) {\n        // 如果没有作业任务，清空作业列表\n        console.log('没有未结束的作业任务，清空作业列表');\n        setHomeworks([]);\n        setLoading(false);\n      }\n    } catch (error) {\n      console.error('获取作业任务列表失败:', error);\n      message.error('获取作业任务列表失败');\n      setHomeworks([]);\n      setLoading(false);\n    } finally {\n      setAssignmentsLoading(false);\n    }\n  };\n\n  // 获取作业列表\n  const fetchHomeworks = async (params = {}) => {\n    // 如果没有选择作业任务，不进行请求\n    if (!params.assignment_id && !filters.assignmentId) {\n      console.log('没有选择作业任务，不请求作业列表');\n      setHomeworks([]);\n      setLoading(false);\n      return;\n    }\n    try {\n      setLoading(true);\n      setError(null);\n\n      // 构建API参数\n      const apiParams = {\n        page: params.page || pagination.current,\n        limit: params.pageSize || pagination.pageSize,\n        status: params.status || filters.status,\n        search: params.search || filters.search,\n        assignment_id: params.assignment_id || filters.assignmentId\n      };\n\n      // 添加日期范围\n      if (params.dateRange || filters.dateRange) {\n        const dateRange = params.dateRange || filters.dateRange;\n        if (dateRange && dateRange.length === 2) {\n          apiParams.start_date = dateRange[0].format('YYYY-MM-DD');\n          apiParams.end_date = dateRange[1].format('YYYY-MM-DD');\n        }\n      }\n      console.log('请求待批改作业列表，参数:', apiParams);\n\n      // 获取数据\n      const data = await getHomeworks(apiParams);\n      console.log('待批改作业列表响应:', data);\n\n      // 检查响应数据\n      if (data && Array.isArray(data.items)) {\n        setHomeworks(data.items);\n        setPagination({\n          ...pagination,\n          current: params.page || pagination.current,\n          total: data.total || data.items.length\n        });\n      } else if (Array.isArray(data)) {\n        setHomeworks(data);\n        setPagination({\n          ...pagination,\n          current: params.page || pagination.current,\n          total: data.length\n        });\n      } else if (data && typeof data === 'object' && data.id) {\n        setHomeworks([data]);\n        setPagination({\n          ...pagination,\n          current: 1,\n          total: 1\n        });\n      } else {\n        console.warn('待批改作业列表响应格式不符合预期:', data);\n        setHomeworks([]);\n        setPagination({\n          ...pagination,\n          current: 1,\n          total: 0\n        });\n        setError('获取待批改作业列表格式异常，暂无数据');\n        message.warning('暂无待批改作业数据');\n      }\n    } catch (error) {\n      console.error('获取待批改作业列表失败:', error);\n      setError(`获取待批改作业列表失败: ${error.message || '未知错误'}`);\n      setHomeworks([]);\n      setPagination({\n        ...pagination,\n        current: 1,\n        total: 0\n      });\n      message.error('获取待批改作业列表失败，请稍后重试');\n    } finally {\n      // 确保无论如何都设置loading为false\n      setLoading(false);\n      console.log('待批改作业列表加载完成，loading状态已重置');\n    }\n  };\n\n  // 首次加载时获取作业任务列表和作业列表\n  useEffect(() => {\n    fetchAssignments();\n  }, []);\n\n  // 处理location.state中的refresh参数\n  useEffect(() => {\n    if (location.state && location.state.refresh) {\n      console.log('检测到需要刷新待批改作业列表');\n\n      // 检查是否需要强制刷新\n      const forceRefresh = location.state.forceRefresh === true;\n      const timestamp = location.state.timestamp || new Date().getTime();\n      console.log(`强制刷新: ${forceRefresh}, 时间戳: ${timestamp}`);\n\n      // 先获取作业任务列表\n      fetchAssignments();\n\n      // 同时刷新当前选中的作业任务的作业列表\n      if (filters.assignmentId) {\n        console.log('同时刷新当前作业任务的作业列表:', filters.assignmentId);\n\n        // 更新lastSelectedAssignment，避免刷新后重复请求\n        setLastSelectedAssignment(filters.assignmentId);\n        fetchHomeworks({\n          assignment_id: filters.assignmentId,\n          page: 1,\n          // 重置到第一页\n          cache: false,\n          // 禁用缓存\n          _t: timestamp // 添加时间戳参数，确保不使用缓存\n        });\n      }\n\n      // 清除state，避免重复刷新\n      navigate(location.pathname, {\n        replace: true,\n        state: {}\n      });\n    }\n  }, [location.state, navigate, location.pathname, filters.assignmentId]);\n\n  // 处理表格变化\n  const handleTableChange = (pagination, filters) => {\n    fetchHomeworks({\n      page: pagination.current,\n      pageSize: pagination.pageSize,\n      ...filters\n    });\n  };\n\n  // 处理搜索\n  const handleSearch = () => {\n    fetchHomeworks({\n      page: 1,\n      ...filters\n    });\n  };\n\n  // 处理查看详情\n  const handleViewDetail = id => {\n    navigate(`/homework/${id}?from=today`);\n  };\n\n  // 处理刷新\n  const handleRefresh = () => {\n    console.log('手动刷新待批改作业列表');\n    fetchAssignments();\n\n    // 同时刷新当前选中的作业任务的作业列表\n    if (filters.assignmentId) {\n      console.log('同时刷新当前作业任务的作业列表:', filters.assignmentId);\n\n      // 更新lastSelectedAssignment，避免刷新后重复请求\n      setLastSelectedAssignment(filters.assignmentId);\n      fetchHomeworks({\n        assignment_id: filters.assignmentId,\n        page: 1,\n        // 重置到第一页\n        cache: false // 禁用缓存\n      });\n    }\n    message.info('正在刷新待批改作业列表...');\n  };\n\n  // 处理作业任务选择变更\n  const handleAssignmentChange = value => {\n    // 如果选择的是同一个作业任务，不重复请求\n    if (value === lastSelectedAssignment) {\n      console.log('选择了相同的作业任务，跳过刷新');\n      return;\n    }\n\n    // 更新上次选择的作业任务\n    setLastSelectedAssignment(value);\n    setFilters(prev => ({\n      ...prev,\n      assignmentId: value\n    }));\n\n    // 获取作业列表，只刷新一次\n    fetchHomeworks({\n      assignment_id: value,\n      page: 1\n    });\n  };\n\n  // 处理删除作业\n  const handleDelete = async id => {\n    try {\n      await deleteHomework(id);\n      message.success('作业删除成功');\n      fetchHomeworks();\n    } catch (error) {\n      console.error('删除作业失败:', error);\n      message.error(`删除作业失败: ${error.message || '未知错误'}`);\n    }\n  };\n\n  // 获取状态标签\n  const getStatusTag = status => {\n    switch (status) {\n      case 'submitted':\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"blue\",\n          icon: /*#__PURE__*/_jsxDEV(ClockCircleOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 40\n          }, this),\n          children: \"\\u5DF2\\u63D0\\u4EA4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 353,\n          columnNumber: 16\n        }, this);\n      case 'grading':\n      case 'correcting':\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"orange\",\n          icon: /*#__PURE__*/_jsxDEV(SyncOutlined, {\n            spin: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 356,\n            columnNumber: 42\n          }, this),\n          children: \"\\u6279\\u6539\\u4E2D\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 356,\n          columnNumber: 16\n        }, this);\n      case 'graded':\n      case 'corrected':\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"green\",\n          icon: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 41\n          }, this),\n          children: \"\\u5DF2\\u6279\\u6539\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 359,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"default\",\n          children: \"\\u672A\\u77E5\\u72B6\\u6001\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 361,\n          columnNumber: 16\n        }, this);\n    }\n  };\n\n  // 移动端卡片式布局渲染\n  const renderMobileCards = () => {\n    if (homeworks.length === 0) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '40px 16px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(FileTextOutlined, {\n          style: {\n            fontSize: '48px',\n            color: '#d9d9d9',\n            marginBottom: '16px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 370,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '16px',\n            color: '#666666',\n            marginBottom: '8px'\n          },\n          children: \"\\u6682\\u65E0\\u4F5C\\u4E1A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 371,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '14px',\n            color: '#999999'\n          },\n          children: \"\\u5F53\\u524D\\u7B5B\\u9009\\u6761\\u4EF6\\u4E0B\\u6CA1\\u6709\\u627E\\u5230\\u4F5C\\u4E1A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 374,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 369,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(List, {\n      dataSource: homeworks,\n      renderItem: homework => /*#__PURE__*/_jsxDEV(List.Item, {\n        style: {\n          padding: 0,\n          marginBottom: '12px'\n        },\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          hoverable: true,\n          style: {\n            width: '100%',\n            borderRadius: '12px',\n            border: '1px solid #e8e8e8',\n            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)'\n          },\n          bodyStyle: {\n            padding: '16px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '12px'\n            },\n            children: /*#__PURE__*/_jsxDEV(Row, {\n              justify: \"space-between\",\n              align: \"top\",\n              children: /*#__PURE__*/_jsxDEV(Col, {\n                flex: \"1\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '16px',\n                    fontWeight: '600',\n                    color: '#262626',\n                    marginBottom: '4px',\n                    lineHeight: '1.4'\n                  },\n                  children: homework.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 400,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Space, {\n                  size: \"small\",\n                  wrap: true,\n                  children: [getStatusTag(homework.status), homework.class_name && /*#__PURE__*/_jsxDEV(Tag, {\n                    color: \"blue\",\n                    children: homework.class_name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 412,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 409,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 399,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 398,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 397,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            gutter: [12, 8],\n            style: {\n              marginBottom: '12px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '12px',\n                  color: '#8c8c8c'\n                },\n                children: \"\\u5B66\\u751F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 424,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '14px',\n                  color: '#595959',\n                  fontWeight: '500'\n                },\n                children: [/*#__PURE__*/_jsxDEV(UserOutlined, {\n                  style: {\n                    marginRight: '4px'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 426,\n                  columnNumber: 21\n                }, this), user.is_teacher ? homework.student_name || `学生ID: ${homework.student_id}` : homework.assignment_title || homework.title || '-']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 425,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 423,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '12px',\n                  color: '#8c8c8c'\n                },\n                children: \"\\u63D0\\u4EA4\\u65F6\\u95F4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 434,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '14px',\n                  color: '#595959',\n                  fontWeight: '500'\n                },\n                children: [/*#__PURE__*/_jsxDEV(CalendarOutlined, {\n                  style: {\n                    marginRight: '4px'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 436,\n                  columnNumber: 21\n                }, this), homework.created_at ? new Date(homework.created_at).toLocaleDateString() : '-']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 435,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 433,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 422,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '8px',\n              justifyContent: 'flex-end'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              size: \"small\",\n              icon: /*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 450,\n                columnNumber: 25\n              }, this),\n              onClick: () => handleViewDetail(homework.id),\n              style: {\n                borderRadius: '6px',\n                height: '36px',\n                fontSize: '14px',\n                fontWeight: '500',\n                touchAction: 'manipulation'\n              },\n              children: \"\\u67E5\\u770B\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 447,\n              columnNumber: 17\n            }, this), user.is_teacher && homework.status === 'submitted' && /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              size: \"small\",\n              icon: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 467,\n                columnNumber: 27\n              }, this),\n              onClick: () => navigate(`/homework/${homework.id}?from=today`, {\n                state: {\n                  startCorrection: true\n                }\n              }),\n              style: {\n                borderRadius: '6px',\n                height: '36px',\n                fontSize: '14px',\n                fontWeight: '500',\n                background: '#52c41a',\n                borderColor: '#52c41a',\n                touchAction: 'manipulation'\n              },\n              children: \"\\u6279\\u6539\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 464,\n              columnNumber: 19\n            }, this), user.is_teacher && /*#__PURE__*/_jsxDEV(Popconfirm, {\n              title: \"\\u786E\\u5B9A\\u5220\\u9664\\u8BE5\\u4F5C\\u4E1A\\u5417\\uFF1F\",\n              onConfirm: () => handleDelete(homework.id),\n              okText: \"\\u786E\\u5B9A\",\n              cancelText: \"\\u53D6\\u6D88\",\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                danger: true,\n                size: \"small\",\n                icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 493,\n                  columnNumber: 29\n                }, this),\n                style: {\n                  borderRadius: '6px',\n                  height: '36px',\n                  fontSize: '14px',\n                  touchAction: 'manipulation'\n                },\n                children: \"\\u5220\\u9664\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 490,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 484,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 446,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 386,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 385,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 382,\n      columnNumber: 7\n    }, this);\n  };\n\n  // 表格列定义\n  const columns = [{\n    title: '标题',\n    dataIndex: 'title',\n    key: 'title',\n    render: (text, record) => /*#__PURE__*/_jsxDEV(\"a\", {\n      href: `/homework/${record.id}`,\n      onClick: e => {\n        e.preventDefault();\n        handleViewDetail(record.id);\n      },\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 520,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: user.is_teacher ? '学生' : '作业来源',\n    dataIndex: user.is_teacher ? 'student_name' : 'assignment_title',\n    key: user.is_teacher ? 'student_name' : 'assignment_title',\n    render: (text, record) => {\n      if (user.is_teacher) {\n        return text || `学生ID: ${record.student_id}`;\n      } else {\n        return text || record.title || '-';\n      }\n    }\n  }, {\n    title: '班级',\n    dataIndex: 'class_name',\n    key: 'class_name',\n    render: (text, record) => {\n      if (text) {\n        return text;\n      } else if (record.class_id) {\n        return `班级ID: ${record.class_id}`;\n      } else {\n        return '-';\n      }\n    }\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    render: status => getStatusTag(status)\n  }, {\n    title: '提交时间',\n    dataIndex: 'created_at',\n    key: 'created_at',\n    render: date => new Date(date).toLocaleString()\n  }, {\n    title: '操作',\n    key: 'action',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        icon: /*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 571,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleViewDetail(record.id),\n        children: \"\\u67E5\\u770B\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 569,\n        columnNumber: 11\n      }, this), user.is_teacher && record.status === 'submitted' && /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        icon: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 579,\n          columnNumber: 21\n        }, this),\n        onClick: () => navigate(`/homework/${record.id}`, {\n          state: {\n            startCorrection: true\n          }\n        }),\n        children: \"\\u6279\\u6539\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 577,\n        columnNumber: 13\n      }, this), user.is_teacher && /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: \"\\u786E\\u5B9A\\u5220\\u9664\\u8BE5\\u4F5C\\u4E1A\\u5417\\uFF1F\",\n        onConfirm: () => handleDelete(record.id),\n        okText: \"\\u786E\\u5B9A\",\n        cancelText: \"\\u53D6\\u6D88\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          danger: true,\n          icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 595,\n            columnNumber: 23\n          }, this),\n          children: \"\\u5220\\u9664\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 592,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 586,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 568,\n      columnNumber: 9\n    }, this)\n  }];\n\n  // 渲染内容\n  const renderContent = () => {\n    // 如果正在加载作业任务列表\n    if (assignmentsLoading) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '50px 0'\n        },\n        children: /*#__PURE__*/_jsxDEV(Spin, {\n          tip: \"\\u52A0\\u8F7D\\u4F5C\\u4E1A\\u4EFB\\u52A1\\u5217\\u8868...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 612,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 611,\n        columnNumber: 9\n      }, this);\n    }\n\n    // 如果没有作业任务\n    if (assignments.length === 0) {\n      return /*#__PURE__*/_jsxDEV(Empty, {\n        description: \"\\u6CA1\\u6709\\u4F5C\\u4E1A\\u4EFB\\u52A1\",\n        image: Empty.PRESENTED_IMAGE_SIMPLE,\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          onClick: () => navigate('/homework-assignment/create'),\n          children: \"\\u521B\\u5EFA\\u4F5C\\u4E1A\\u4EFB\\u52A1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 624,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 620,\n        columnNumber: 9\n      }, this);\n    }\n\n    // 如果有作业任务但没有作业\n    return /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Card, {\n        className: \"teacher-page\",\n        style: {\n          marginBottom: isMobile ? '16px' : '24px',\n          marginTop: '16px',\n          borderRadius: '12px',\n          border: '1px solid #E8E8E8'\n        },\n        bodyStyle: {\n          padding: isMobile ? '16px' : '20px'\n        },\n        children: isMobile ?\n        /*#__PURE__*/\n        // 移动端筛选器布局\n        _jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '8px',\n              marginBottom: '16px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(SearchOutlined, {\n              style: {\n                color: '#1890ff'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 654,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                fontWeight: 500,\n                color: '#262626'\n              },\n              children: \"\\u7B5B\\u9009\\u6761\\u4EF6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 655,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 648,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            gutter: [12, 12],\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 24,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginBottom: '4px'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontSize: '14px',\n                    color: '#666'\n                  },\n                  children: \"\\u4F5C\\u4E1A\\u4EFB\\u52A1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 661,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 660,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"\\u9009\\u62E9\\u4F5C\\u4E1A\\u4EFB\\u52A1\",\n                style: {\n                  width: '100%'\n                },\n                value: filters.assignmentId,\n                onChange: handleAssignmentChange,\n                loading: loading,\n                size: \"large\",\n                children: assignments.map(assignment => /*#__PURE__*/_jsxDEV(Option, {\n                  value: assignment.id,\n                  children: assignment.title\n                }, assignment.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 672,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 663,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 659,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginBottom: '4px'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontSize: '14px',\n                    color: '#666'\n                  },\n                  children: \"\\u641C\\u7D22\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 681,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 680,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u641C\\u7D22\\u4F5C\\u4E1A\\u6807\\u9898\\u6216\\u5B66\\u751F\",\n                value: filters.search,\n                onChange: e => setFilters({\n                  ...filters,\n                  search: e.target.value\n                }),\n                style: {\n                  width: '100%'\n                },\n                size: \"large\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 683,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 679,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginBottom: '4px'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontSize: '14px',\n                    color: '#666'\n                  },\n                  children: \"\\u72B6\\u6001\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 694,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 693,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"\\u9009\\u62E9\\u72B6\\u6001\",\n                style: {\n                  width: '100%'\n                },\n                value: filters.status,\n                onChange: value => setFilters({\n                  ...filters,\n                  status: value\n                }),\n                size: \"large\",\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: \"\",\n                  children: \"\\u5168\\u90E8\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 703,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"submitted\",\n                  children: \"\\u5F85\\u6279\\u6539\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 704,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"grading\",\n                  children: \"\\u6279\\u6539\\u4E2D\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 705,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"graded\",\n                  children: \"\\u5DF2\\u6279\\u6539\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 706,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 696,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 692,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                type: \"primary\",\n                icon: /*#__PURE__*/_jsxDEV(SearchOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 713,\n                  columnNumber: 27\n                }, this),\n                onClick: handleSearch,\n                block: true,\n                size: \"large\",\n                style: {\n                  height: '44px'\n                },\n                children: \"\\u641C\\u7D22\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 711,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 710,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                onClick: handleRefresh,\n                block: true,\n                size: \"large\",\n                style: {\n                  height: '44px'\n                },\n                children: \"\\u5237\\u65B0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 724,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 723,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 658,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 647,\n          columnNumber: 13\n        }, this) :\n        /*#__PURE__*/\n        // 桌面端筛选器布局\n        _jsxDEV(Space, {\n          style: {\n            marginBottom: 16\n          },\n          children: [/*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u9009\\u62E9\\u4F5C\\u4E1A\\u4EFB\\u52A1\",\n            style: {\n              width: 200\n            },\n            value: filters.assignmentId,\n            onChange: handleAssignmentChange,\n            loading: loading,\n            children: assignments.map(assignment => /*#__PURE__*/_jsxDEV(Option, {\n              value: assignment.id,\n              children: assignment.title\n            }, assignment.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 746,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 738,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u641C\\u7D22\\u4F5C\\u4E1A\\u6807\\u9898\\u6216\\u5B66\\u751F\",\n            value: filters.search,\n            onChange: e => setFilters({\n              ...filters,\n              search: e.target.value\n            }),\n            style: {\n              width: 200\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 752,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u9009\\u62E9\\u72B6\\u6001\",\n            style: {\n              width: 120\n            },\n            value: filters.status,\n            onChange: value => setFilters({\n              ...filters,\n              status: value\n            }),\n            children: [/*#__PURE__*/_jsxDEV(Option, {\n              value: \"\",\n              children: \"\\u5168\\u90E8\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 765,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"submitted\",\n              children: \"\\u5F85\\u6279\\u6539\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 766,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"grading\",\n              children: \"\\u6279\\u6539\\u4E2D\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 767,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"graded\",\n              children: \"\\u5DF2\\u6279\\u6539\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 768,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 759,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            icon: /*#__PURE__*/_jsxDEV(SearchOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 773,\n              columnNumber: 23\n            }, this),\n            onClick: handleSearch,\n            children: \"\\u641C\\u7D22\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 771,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleRefresh,\n            children: \"\\u5237\\u65B0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 779,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 737,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 635,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n        message: \"\\u83B7\\u53D6\\u6570\\u636E\\u65F6\\u51FA\\u73B0\\u9519\\u8BEF\",\n        description: error,\n        type: \"error\",\n        showIcon: true,\n        style: {\n          marginBottom: 16\n        },\n        action: /*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          onClick: handleRefresh,\n          children: \"\\u91CD\\u8BD5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 795,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 788,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"teacher-page\",\n        children: /*#__PURE__*/_jsxDEV(Spin, {\n          spinning: loading,\n          children: homeworks.length > 0 ? isMobile ?\n          /*#__PURE__*/\n          // 移动端卡片式布局\n          _jsxDEV(\"div\", {\n            style: {\n              padding: '0 4px'\n            },\n            children: [renderMobileCards(), pagination.total > pagination.pageSize && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                textAlign: 'center',\n                marginTop: '20px',\n                padding: '16px 0'\n              },\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  disabled: pagination.current === 1,\n                  onClick: () => handleTableChange({\n                    current: pagination.current - 1,\n                    pageSize: pagination.pageSize\n                  }),\n                  style: {\n                    height: '40px'\n                  },\n                  children: \"\\u4E0A\\u4E00\\u9875\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 818,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    padding: '0 16px',\n                    fontSize: '14px',\n                    color: '#666'\n                  },\n                  children: [pagination.current, \" / \", Math.ceil(pagination.total / pagination.pageSize)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 828,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  disabled: pagination.current >= Math.ceil(pagination.total / pagination.pageSize),\n                  onClick: () => handleTableChange({\n                    current: pagination.current + 1,\n                    pageSize: pagination.pageSize\n                  }),\n                  style: {\n                    height: '40px'\n                  },\n                  children: \"\\u4E0B\\u4E00\\u9875\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 835,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 817,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 812,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 808,\n            columnNumber: 17\n          }, this) :\n          /*#__PURE__*/\n          // 桌面端表格布局\n          _jsxDEV(Table, {\n            columns: columns,\n            dataSource: homeworks,\n            rowKey: \"id\",\n            pagination: pagination,\n            onChange: handleTableChange,\n            loading: false\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 851,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(Empty, {\n            description: loading ? \"加载中...\" : \"暂无今日作业\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 861,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 804,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 803,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `pending-review-homeworks-container teacher-page ${isMobile ? 'mobile' : 'desktop'}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: isMobile ? '16px' : '24px',\n        padding: isMobile ? '0 4px' : '0'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        level: isMobile ? 4 : 3,\n        style: {\n          marginBottom: '8px'\n        },\n        children: \"\\uD83D\\uDCDA \\u4ECA\\u65E5\\u4F5C\\u4E1A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 877,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        type: \"secondary\",\n        style: {\n          fontSize: isMobile ? '14px' : '16px'\n        },\n        children: \"\\u663E\\u793A\\u6240\\u6709\\u672A\\u7ED3\\u675F\\u4F5C\\u4E1A\\u4EFB\\u52A1\\u7684\\u4F5C\\u4E1A\\u63D0\\u4EA4\\uFF0C\\u5305\\u62EC\\u5F85\\u6279\\u6539\\u548C\\u6279\\u6539\\u4E2D\\u7684\\u4F5C\\u4E1A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 880,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 873,\n      columnNumber: 7\n    }, this), renderContent()]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 872,\n    columnNumber: 5\n  }, this);\n};\n_s(PendingReviewHomeworks, \"6QC3hhuWSoI0j/zZ11/95a/5HYU=\", false, function () {\n  return [useNavigate, useLocation, useSearchParams];\n});\n_c = PendingReviewHomeworks;\nexport default PendingReviewHomeworks;\nvar _c;\n$RefreshReg$(_c, \"PendingReviewHomeworks\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useRef", "useNavigate", "useSearchParams", "useLocation", "Table", "Tag", "<PERSON><PERSON>", "Input", "Select", "DatePicker", "Space", "message", "<PERSON><PERSON>", "Empty", "Spin", "Typography", "Popconfirm", "List", "Card", "Row", "Col", "Avatar", "SearchOutlined", "FileTextOutlined", "CheckCircleOutlined", "DeleteOutlined", "SyncOutlined", "ClockCircleOutlined", "CalendarOutlined", "UserOutlined", "getHomeworks", "deleteHomework", "getHomeworkAssignments", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Option", "RangePicker", "Title", "Text", "debounce", "fn", "delay", "timer", "args", "clearTimeout", "setTimeout", "apply", "PendingReviewHomeworks", "user", "_s", "navigate", "location", "searchParams", "loading", "setLoading", "homeworks", "setHomeworks", "assignments", "setAssignments", "pagination", "setPagination", "current", "pageSize", "total", "error", "setError", "assignmentsLoading", "setAssignmentsLoading", "lastSelectedAssignment", "setLastSelectedAssignment", "isMobile", "setIsMobile", "window", "innerWidth", "filters", "setFilters", "status", "search", "get", "date<PERSON><PERSON><PERSON>", "assignmentId", "handleResize", "addEventListener", "removeEventListener", "fetchAssignments", "console", "log", "response", "assignmentsList", "items", "Array", "isArray", "processedAssignments", "map", "assignment", "description", "statusMatch", "match", "activeAssignments", "filter", "length", "firstAssignmentId", "id", "prev", "fetchHomeworks", "assignment_id", "params", "apiParams", "page", "limit", "start_date", "format", "end_date", "data", "warn", "warning", "state", "refresh", "forceRefresh", "timestamp", "Date", "getTime", "cache", "_t", "pathname", "replace", "handleTableChange", "handleSearch", "handleViewDetail", "handleRefresh", "info", "handleAssignmentChange", "value", "handleDelete", "success", "getStatusTag", "color", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "children", "spin", "renderMobileCards", "style", "textAlign", "padding", "fontSize", "marginBottom", "dataSource", "renderItem", "homework", "<PERSON><PERSON>", "hoverable", "width", "borderRadius", "border", "boxShadow", "bodyStyle", "justify", "align", "flex", "fontWeight", "lineHeight", "title", "size", "wrap", "class_name", "gutter", "span", "marginRight", "is_teacher", "student_name", "student_id", "assignment_title", "created_at", "toLocaleDateString", "display", "gap", "justifyContent", "type", "onClick", "height", "touchAction", "startCorrection", "background", "borderColor", "onConfirm", "okText", "cancelText", "danger", "columns", "dataIndex", "key", "render", "text", "record", "href", "e", "preventDefault", "class_id", "date", "toLocaleString", "_", "renderContent", "tip", "image", "PRESENTED_IMAGE_SIMPLE", "className", "marginTop", "alignItems", "placeholder", "onChange", "target", "block", "showIcon", "action", "spinning", "disabled", "Math", "ceil", "<PERSON><PERSON><PERSON>", "level", "_c", "$RefreshReg$"], "sources": ["D:/pythonproject/checkingsys/frontend/src/components/PendingReviewHomeworks.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useRef } from 'react';\r\nimport { useNavigate, useSearchParams, useLocation } from 'react-router-dom';\r\nimport {\r\n  Table, Tag, Button, Input, Select, DatePicker, Space, message,\r\n  Alert, Empty, Spin, Typography, Popconfirm, List, Card, Row, Col, Avatar\r\n} from 'antd';\r\nimport {\r\n  SearchOutlined, FileTextOutlined, CheckCircleOutlined, DeleteOutlined,\r\n  SyncOutlined, ClockCircleOutlined, CalendarOutlined, UserOutlined\r\n} from '@ant-design/icons';\r\nimport { getHomeworks, deleteHomework, getHomeworkAssignments } from '../utils/api';\r\n\r\nconst { Option } = Select;\r\nconst { RangePicker } = DatePicker;\r\nconst { Title, Text } = Typography;\r\n\r\n// 简单的防抖函数\r\nconst debounce = (fn, delay) => {\r\n  let timer = null;\r\n  return function(...args) {\r\n    if (timer) clearTimeout(timer);\r\n    timer = setTimeout(() => {\r\n      fn.apply(this, args);\r\n    }, delay);\r\n  };\r\n};\r\n\r\nconst PendingReviewHomeworks = ({ user }) => {\r\n  const navigate = useNavigate();\r\n  const location = useLocation();\r\n  const [searchParams] = useSearchParams();\r\n  const [loading, setLoading] = useState(false);\r\n  const [homeworks, setHomeworks] = useState([]);\r\n  const [assignments, setAssignments] = useState([]);\r\n  const [pagination, setPagination] = useState({\r\n    current: 1,\r\n    pageSize: 10,\r\n    total: 0\r\n  });\r\n  const [error, setError] = useState(null);\r\n  const [assignmentsLoading, setAssignmentsLoading] = useState(false);\r\n  const [lastSelectedAssignment, setLastSelectedAssignment] = useState(null);\r\n  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);\r\n  \r\n  // 过滤条件\r\n  const [filters, setFilters] = useState({\r\n    status: '',  // 默认不过滤状态，显示所有进行中的作业\r\n    search: searchParams.get('search') || '',\r\n    dateRange: null,\r\n    assignmentId: null\r\n  });\r\n\r\n  // 监听窗口大小变化\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      setIsMobile(window.innerWidth <= 768);\r\n    };\r\n\r\n    window.addEventListener('resize', handleResize);\r\n    return () => window.removeEventListener('resize', handleResize);\r\n  }, []);\r\n  \r\n  // 获取作业任务列表\r\n  const fetchAssignments = async () => {\r\n    try {\r\n      setAssignmentsLoading(true);\r\n      console.log('开始获取作业任务列表');\r\n      const response = await getHomeworkAssignments();\r\n      console.log('获取作业任务列表响应:', response);\r\n      \r\n      let assignmentsList = [];\r\n      \r\n      if (response && response.items) {\r\n        assignmentsList = response.items;\r\n      } else if (Array.isArray(response)) {\r\n        assignmentsList = response;\r\n      }\r\n      \r\n      console.log('解析后的作业任务列表:', assignmentsList);\r\n\r\n      // 处理作业任务数据，确保每个作业任务都有正确的status字段\r\n      const processedAssignments = assignmentsList.map(assignment => {\r\n        let status = assignment.status;\r\n\r\n        // 如果没有status字段，从description中提取状态\r\n        if (!status && assignment.description) {\r\n          // 使用正则表达式提取状态\r\n          const statusMatch = assignment.description.match(/【状态】(.*?)】/);\r\n          if (statusMatch) {\r\n            status = statusMatch[1];\r\n          } else {\r\n            status = 'active'; // 默认状态为active\r\n          }\r\n        } else if (!status) {\r\n          status = 'active'; // 默认状态为active\r\n        }\r\n\r\n        return {\r\n          ...assignment,\r\n          status: status\r\n        };\r\n      });\r\n\r\n      console.log('处理后的作业任务列表:', processedAssignments);\r\n\r\n      // 过滤已结束的作业任务，只保留未结束的作业任务\r\n      const activeAssignments = processedAssignments.filter(assignment =>\r\n        assignment.status !== 'finished'\r\n      );\r\n      \r\n      console.log('未结束的作业任务:', activeAssignments);\r\n      setAssignments(activeAssignments);\r\n      \r\n      // 如果有作业任务，默认选择第一个\r\n      if (activeAssignments.length > 0 && !filters.assignmentId) {\r\n        console.log('选择第一个作业任务:', activeAssignments[0]);\r\n        const firstAssignmentId = activeAssignments[0].id;\r\n        \r\n        // 同时更新lastSelectedAssignment，避免重复刷新\r\n        setLastSelectedAssignment(firstAssignmentId);\r\n        \r\n        setFilters(prev => ({\r\n          ...prev,\r\n          assignmentId: firstAssignmentId\r\n        }));\r\n        \r\n        // 加载该作业任务的作业\r\n        fetchHomeworks({\r\n          assignment_id: firstAssignmentId\r\n        });\r\n      } else if (activeAssignments.length === 0) {\r\n        // 如果没有作业任务，清空作业列表\r\n        console.log('没有未结束的作业任务，清空作业列表');\r\n        setHomeworks([]);\r\n        setLoading(false);\r\n      }\r\n    } catch (error) {\r\n      console.error('获取作业任务列表失败:', error);\r\n      message.error('获取作业任务列表失败');\r\n      setHomeworks([]);\r\n      setLoading(false);\r\n    } finally {\r\n      setAssignmentsLoading(false);\r\n    }\r\n  };\r\n  \r\n  // 获取作业列表\r\n  const fetchHomeworks = async (params = {}) => {\r\n    // 如果没有选择作业任务，不进行请求\r\n    if (!params.assignment_id && !filters.assignmentId) {\r\n      console.log('没有选择作业任务，不请求作业列表');\r\n      setHomeworks([]);\r\n      setLoading(false);\r\n      return;\r\n    }\r\n    \r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n      \r\n      // 构建API参数\r\n      const apiParams = {\r\n        page: params.page || pagination.current,\r\n        limit: params.pageSize || pagination.pageSize,\r\n        status: params.status || filters.status,\r\n        search: params.search || filters.search,\r\n        assignment_id: params.assignment_id || filters.assignmentId\r\n      };\r\n      \r\n      // 添加日期范围\r\n      if (params.dateRange || filters.dateRange) {\r\n        const dateRange = params.dateRange || filters.dateRange;\r\n        if (dateRange && dateRange.length === 2) {\r\n          apiParams.start_date = dateRange[0].format('YYYY-MM-DD');\r\n          apiParams.end_date = dateRange[1].format('YYYY-MM-DD');\r\n        }\r\n      }\r\n      \r\n      console.log('请求待批改作业列表，参数:', apiParams);\r\n      \r\n      // 获取数据\r\n      const data = await getHomeworks(apiParams);\r\n      console.log('待批改作业列表响应:', data);\r\n      \r\n      // 检查响应数据\r\n      if (data && Array.isArray(data.items)) {\r\n        setHomeworks(data.items);\r\n        setPagination({\r\n          ...pagination,\r\n          current: params.page || pagination.current,\r\n          total: data.total || data.items.length\r\n        });\r\n      } else if (Array.isArray(data)) {\r\n        setHomeworks(data);\r\n        setPagination({\r\n          ...pagination,\r\n          current: params.page || pagination.current,\r\n          total: data.length\r\n        });\r\n      } else if (data && typeof data === 'object' && data.id) {\r\n        setHomeworks([data]);\r\n        setPagination({\r\n          ...pagination,\r\n          current: 1,\r\n          total: 1\r\n        });\r\n      } else {\r\n        console.warn('待批改作业列表响应格式不符合预期:', data);\r\n        setHomeworks([]);\r\n        setPagination({\r\n          ...pagination,\r\n          current: 1,\r\n          total: 0\r\n        });\r\n        setError('获取待批改作业列表格式异常，暂无数据');\r\n        message.warning('暂无待批改作业数据');\r\n      }\r\n    } catch (error) {\r\n      console.error('获取待批改作业列表失败:', error);\r\n      setError(`获取待批改作业列表失败: ${error.message || '未知错误'}`);\r\n      setHomeworks([]);\r\n      setPagination({\r\n        ...pagination,\r\n        current: 1,\r\n        total: 0\r\n      });\r\n      message.error('获取待批改作业列表失败，请稍后重试');\r\n    } finally {\r\n      // 确保无论如何都设置loading为false\r\n      setLoading(false);\r\n      console.log('待批改作业列表加载完成，loading状态已重置');\r\n    }\r\n  };\r\n  \r\n  // 首次加载时获取作业任务列表和作业列表\r\n  useEffect(() => {\r\n    fetchAssignments();\r\n  }, []);\r\n  \r\n  // 处理location.state中的refresh参数\r\n  useEffect(() => {\r\n    if (location.state && location.state.refresh) {\r\n      console.log('检测到需要刷新待批改作业列表');\r\n      \r\n      // 检查是否需要强制刷新\r\n      const forceRefresh = location.state.forceRefresh === true;\r\n      const timestamp = location.state.timestamp || new Date().getTime();\r\n      console.log(`强制刷新: ${forceRefresh}, 时间戳: ${timestamp}`);\r\n      \r\n      // 先获取作业任务列表\r\n      fetchAssignments();\r\n      \r\n      // 同时刷新当前选中的作业任务的作业列表\r\n      if (filters.assignmentId) {\r\n        console.log('同时刷新当前作业任务的作业列表:', filters.assignmentId);\r\n        \r\n        // 更新lastSelectedAssignment，避免刷新后重复请求\r\n        setLastSelectedAssignment(filters.assignmentId);\r\n        \r\n        fetchHomeworks({\r\n          assignment_id: filters.assignmentId,\r\n          page: 1,  // 重置到第一页\r\n          cache: false,  // 禁用缓存\r\n          _t: timestamp  // 添加时间戳参数，确保不使用缓存\r\n        });\r\n      }\r\n      \r\n      // 清除state，避免重复刷新\r\n      navigate(location.pathname, { replace: true, state: {} });\r\n    }\r\n  }, [location.state, navigate, location.pathname, filters.assignmentId]);\r\n  \r\n  // 处理表格变化\r\n  const handleTableChange = (pagination, filters) => {\r\n    fetchHomeworks({\r\n      page: pagination.current,\r\n      pageSize: pagination.pageSize,\r\n      ...filters\r\n    });\r\n  };\r\n  \r\n  // 处理搜索\r\n  const handleSearch = () => {\r\n    fetchHomeworks({ page: 1, ...filters });\r\n  };\r\n  \r\n  // 处理查看详情\r\n  const handleViewDetail = (id) => {\r\n    navigate(`/homework/${id}?from=today`);\r\n  };\r\n  \r\n  // 处理刷新\r\n  const handleRefresh = () => {\r\n    console.log('手动刷新待批改作业列表');\r\n    fetchAssignments();\r\n    \r\n    // 同时刷新当前选中的作业任务的作业列表\r\n    if (filters.assignmentId) {\r\n      console.log('同时刷新当前作业任务的作业列表:', filters.assignmentId);\r\n      \r\n      // 更新lastSelectedAssignment，避免刷新后重复请求\r\n      setLastSelectedAssignment(filters.assignmentId);\r\n      \r\n      fetchHomeworks({\r\n        assignment_id: filters.assignmentId,\r\n        page: 1,  // 重置到第一页\r\n        cache: false  // 禁用缓存\r\n      });\r\n    }\r\n    \r\n    message.info('正在刷新待批改作业列表...');\r\n  };\r\n  \r\n  // 处理作业任务选择变更\r\n  const handleAssignmentChange = (value) => {\r\n    // 如果选择的是同一个作业任务，不重复请求\r\n    if (value === lastSelectedAssignment) {\r\n      console.log('选择了相同的作业任务，跳过刷新');\r\n      return;\r\n    }\r\n    \r\n    // 更新上次选择的作业任务\r\n    setLastSelectedAssignment(value);\r\n    \r\n    setFilters(prev => ({\r\n      ...prev,\r\n      assignmentId: value\r\n    }));\r\n    \r\n    // 获取作业列表，只刷新一次\r\n    fetchHomeworks({\r\n      assignment_id: value,\r\n      page: 1\r\n    });\r\n  };\r\n  \r\n  // 处理删除作业\r\n  const handleDelete = async (id) => {\r\n    try {\r\n      await deleteHomework(id);\r\n      message.success('作业删除成功');\r\n      fetchHomeworks();\r\n    } catch (error) {\r\n      console.error('删除作业失败:', error);\r\n      message.error(`删除作业失败: ${error.message || '未知错误'}`);\r\n    }\r\n  };\r\n  \r\n  // 获取状态标签\r\n  const getStatusTag = (status) => {\r\n    switch (status) {\r\n      case 'submitted':\r\n        return <Tag color=\"blue\" icon={<ClockCircleOutlined />}>已提交</Tag>;\r\n      case 'grading':\r\n      case 'correcting':\r\n        return <Tag color=\"orange\" icon={<SyncOutlined spin />}>批改中</Tag>;\r\n      case 'graded':\r\n      case 'corrected':\r\n        return <Tag color=\"green\" icon={<CheckCircleOutlined />}>已批改</Tag>;\r\n      default:\r\n        return <Tag color=\"default\">未知状态</Tag>;\r\n    }\r\n  };\r\n  \r\n  // 移动端卡片式布局渲染\r\n  const renderMobileCards = () => {\r\n    if (homeworks.length === 0) {\r\n      return (\r\n        <div style={{ textAlign: 'center', padding: '40px 16px' }}>\r\n          <FileTextOutlined style={{ fontSize: '48px', color: '#d9d9d9', marginBottom: '16px' }} />\r\n          <div style={{ fontSize: '16px', color: '#666666', marginBottom: '8px' }}>\r\n            暂无作业\r\n          </div>\r\n          <div style={{ fontSize: '14px', color: '#999999' }}>\r\n            当前筛选条件下没有找到作业\r\n          </div>\r\n        </div>\r\n      );\r\n    }\r\n\r\n    return (\r\n      <List\r\n        dataSource={homeworks}\r\n        renderItem={(homework) => (\r\n          <List.Item style={{ padding: 0, marginBottom: '12px' }}>\r\n            <Card\r\n              hoverable\r\n              style={{\r\n                width: '100%',\r\n                borderRadius: '12px',\r\n                border: '1px solid #e8e8e8',\r\n                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)'\r\n              }}\r\n              bodyStyle={{ padding: '16px' }}\r\n            >\r\n              {/* 卡片头部 */}\r\n              <div style={{ marginBottom: '12px' }}>\r\n                <Row justify=\"space-between\" align=\"top\">\r\n                  <Col flex=\"1\">\r\n                    <div style={{\r\n                      fontSize: '16px',\r\n                      fontWeight: '600',\r\n                      color: '#262626',\r\n                      marginBottom: '4px',\r\n                      lineHeight: '1.4'\r\n                    }}>\r\n                      {homework.title}\r\n                    </div>\r\n                    <Space size=\"small\" wrap>\r\n                      {getStatusTag(homework.status)}\r\n                      {homework.class_name && (\r\n                        <Tag color=\"blue\">\r\n                          {homework.class_name}\r\n                        </Tag>\r\n                      )}\r\n                    </Space>\r\n                  </Col>\r\n                </Row>\r\n              </div>\r\n\r\n              {/* 卡片内容 */}\r\n              <Row gutter={[12, 8]} style={{ marginBottom: '12px' }}>\r\n                <Col span={12}>\r\n                  <div style={{ fontSize: '12px', color: '#8c8c8c' }}>学生</div>\r\n                  <div style={{ fontSize: '14px', color: '#595959', fontWeight: '500' }}>\r\n                    <UserOutlined style={{ marginRight: '4px' }} />\r\n                    {user.is_teacher ?\r\n                      (homework.student_name || `学生ID: ${homework.student_id}`) :\r\n                      (homework.assignment_title || homework.title || '-')\r\n                    }\r\n                  </div>\r\n                </Col>\r\n                <Col span={12}>\r\n                  <div style={{ fontSize: '12px', color: '#8c8c8c' }}>提交时间</div>\r\n                  <div style={{ fontSize: '14px', color: '#595959', fontWeight: '500' }}>\r\n                    <CalendarOutlined style={{ marginRight: '4px' }} />\r\n                    {homework.created_at ?\r\n                      new Date(homework.created_at).toLocaleDateString() :\r\n                      '-'\r\n                    }\r\n                  </div>\r\n                </Col>\r\n              </Row>\r\n\r\n              {/* 操作按钮 */}\r\n              <div style={{ display: 'flex', gap: '8px', justifyContent: 'flex-end' }}>\r\n                <Button\r\n                  type=\"primary\"\r\n                  size=\"small\"\r\n                  icon={<FileTextOutlined />}\r\n                  onClick={() => handleViewDetail(homework.id)}\r\n                  style={{\r\n                    borderRadius: '6px',\r\n                    height: '36px',\r\n                    fontSize: '14px',\r\n                    fontWeight: '500',\r\n                    touchAction: 'manipulation'\r\n                  }}\r\n                >\r\n                  查看\r\n                </Button>\r\n\r\n                {user.is_teacher && homework.status === 'submitted' && (\r\n                  <Button\r\n                    type=\"primary\"\r\n                    size=\"small\"\r\n                    icon={<CheckCircleOutlined />}\r\n                    onClick={() => navigate(`/homework/${homework.id}?from=today`, { state: { startCorrection: true } })}\r\n                    style={{\r\n                      borderRadius: '6px',\r\n                      height: '36px',\r\n                      fontSize: '14px',\r\n                      fontWeight: '500',\r\n                      background: '#52c41a',\r\n                      borderColor: '#52c41a',\r\n                      touchAction: 'manipulation'\r\n                    }}\r\n                  >\r\n                    批改\r\n                  </Button>\r\n                )}\r\n\r\n                {user.is_teacher && (\r\n                  <Popconfirm\r\n                    title=\"确定删除该作业吗？\"\r\n                    onConfirm={() => handleDelete(homework.id)}\r\n                    okText=\"确定\"\r\n                    cancelText=\"取消\"\r\n                  >\r\n                    <Button\r\n                      danger\r\n                      size=\"small\"\r\n                      icon={<DeleteOutlined />}\r\n                      style={{\r\n                        borderRadius: '6px',\r\n                        height: '36px',\r\n                        fontSize: '14px',\r\n                        touchAction: 'manipulation'\r\n                      }}\r\n                    >\r\n                      删除\r\n                    </Button>\r\n                  </Popconfirm>\r\n                )}\r\n              </div>\r\n            </Card>\r\n          </List.Item>\r\n        )}\r\n      />\r\n    );\r\n  };\r\n\r\n  // 表格列定义\r\n  const columns = [\r\n    {\r\n      title: '标题',\r\n      dataIndex: 'title',\r\n      key: 'title',\r\n      render: (text, record) => (\r\n        <a href={`/homework/${record.id}`} onClick={(e) => {\r\n          e.preventDefault();\r\n          handleViewDetail(record.id);\r\n        }}>{text}</a>\r\n      )\r\n    },\r\n    {\r\n      title: user.is_teacher ? '学生' : '作业来源',\r\n      dataIndex: user.is_teacher ? 'student_name' : 'assignment_title',\r\n      key: user.is_teacher ? 'student_name' : 'assignment_title',\r\n      render: (text, record) => {\r\n        if (user.is_teacher) {\r\n          return text || `学生ID: ${record.student_id}`;\r\n        } else {\r\n          return text || record.title || '-';\r\n        }\r\n      }\r\n    },\r\n    {\r\n      title: '班级',\r\n      dataIndex: 'class_name',\r\n      key: 'class_name',\r\n      render: (text, record) => {\r\n        if (text) {\r\n          return text;\r\n        } else if (record.class_id) {\r\n          return `班级ID: ${record.class_id}`;\r\n        } else {\r\n          return '-';\r\n        }\r\n      }\r\n    },\r\n    {\r\n      title: '状态',\r\n      dataIndex: 'status',\r\n      key: 'status',\r\n      render: (status) => getStatusTag(status)\r\n    },\r\n    {\r\n      title: '提交时间',\r\n      dataIndex: 'created_at',\r\n      key: 'created_at',\r\n      render: (date) => new Date(date).toLocaleString()\r\n    },\r\n    {\r\n      title: '操作',\r\n      key: 'action',\r\n      render: (_, record) => (\r\n        <Space>\r\n          <Button\r\n            type=\"primary\"\r\n            icon={<FileTextOutlined />}\r\n            onClick={() => handleViewDetail(record.id)}\r\n          >\r\n            查看\r\n          </Button>\r\n          {user.is_teacher && record.status === 'submitted' && (\r\n            <Button\r\n              type=\"primary\"\r\n              icon={<CheckCircleOutlined />}\r\n              onClick={() => navigate(`/homework/${record.id}`, { state: { startCorrection: true } })}\r\n            >\r\n              批改\r\n            </Button>\r\n          )}\r\n          {user.is_teacher && (\r\n            <Popconfirm\r\n              title=\"确定删除该作业吗？\"\r\n              onConfirm={() => handleDelete(record.id)}\r\n              okText=\"确定\"\r\n              cancelText=\"取消\"\r\n            >\r\n              <Button\r\n                type=\"primary\"\r\n                danger\r\n                icon={<DeleteOutlined />}\r\n              >\r\n                删除\r\n              </Button>\r\n            </Popconfirm>\r\n          )}\r\n        </Space>\r\n      )\r\n    }\r\n  ];\r\n\r\n  // 渲染内容\r\n  const renderContent = () => {\r\n    // 如果正在加载作业任务列表\r\n    if (assignmentsLoading) {\r\n      return (\r\n        <div style={{ textAlign: 'center', padding: '50px 0' }}>\r\n          <Spin tip=\"加载作业任务列表...\" />\r\n        </div>\r\n      );\r\n    }\r\n    \r\n    // 如果没有作业任务\r\n    if (assignments.length === 0) {\r\n      return (\r\n        <Empty \r\n          description=\"没有作业任务\" \r\n          image={Empty.PRESENTED_IMAGE_SIMPLE}\r\n        >\r\n          <Button type=\"primary\" onClick={() => navigate('/homework-assignment/create')}>\r\n            创建作业任务\r\n          </Button>\r\n        </Empty>\r\n      );\r\n    }\r\n    \r\n    // 如果有作业任务但没有作业\r\n    return (\r\n      <>\r\n        {/* 筛选工具栏 */}\r\n        <Card\r\n          className=\"teacher-page\"\r\n          style={{\r\n            marginBottom: isMobile ? '16px' : '24px',\r\n            marginTop: '16px',\r\n            borderRadius: '12px',\r\n            border: '1px solid #E8E8E8'\r\n          }}\r\n          bodyStyle={{ padding: isMobile ? '16px' : '20px' }}\r\n        >\r\n          {isMobile ? (\r\n            // 移动端筛选器布局\r\n            <div>\r\n              <div style={{\r\n                display: 'flex',\r\n                alignItems: 'center',\r\n                gap: '8px',\r\n                marginBottom: '16px'\r\n              }}>\r\n                <SearchOutlined style={{ color: '#1890ff' }} />\r\n                <span style={{ fontWeight: 500, color: '#262626' }}>筛选条件</span>\r\n              </div>\r\n\r\n              <Row gutter={[12, 12]}>\r\n                <Col span={24}>\r\n                  <div style={{ marginBottom: '4px' }}>\r\n                    <span style={{ fontSize: '14px', color: '#666' }}>作业任务</span>\r\n                  </div>\r\n                  <Select\r\n                    placeholder=\"选择作业任务\"\r\n                    style={{ width: '100%' }}\r\n                    value={filters.assignmentId}\r\n                    onChange={handleAssignmentChange}\r\n                    loading={loading}\r\n                    size=\"large\"\r\n                  >\r\n                    {assignments.map(assignment => (\r\n                      <Option key={assignment.id} value={assignment.id}>\r\n                        {assignment.title}\r\n                      </Option>\r\n                    ))}\r\n                  </Select>\r\n                </Col>\r\n\r\n                <Col span={12}>\r\n                  <div style={{ marginBottom: '4px' }}>\r\n                    <span style={{ fontSize: '14px', color: '#666' }}>搜索</span>\r\n                  </div>\r\n                  <Input\r\n                    placeholder=\"搜索作业标题或学生\"\r\n                    value={filters.search}\r\n                    onChange={(e) => setFilters({ ...filters, search: e.target.value })}\r\n                    style={{ width: '100%' }}\r\n                    size=\"large\"\r\n                  />\r\n                </Col>\r\n\r\n                <Col span={12}>\r\n                  <div style={{ marginBottom: '4px' }}>\r\n                    <span style={{ fontSize: '14px', color: '#666' }}>状态</span>\r\n                  </div>\r\n                  <Select\r\n                    placeholder=\"选择状态\"\r\n                    style={{ width: '100%' }}\r\n                    value={filters.status}\r\n                    onChange={(value) => setFilters({ ...filters, status: value })}\r\n                    size=\"large\"\r\n                  >\r\n                    <Option value=\"\">全部</Option>\r\n                    <Option value=\"submitted\">待批改</Option>\r\n                    <Option value=\"grading\">批改中</Option>\r\n                    <Option value=\"graded\">已批改</Option>\r\n                  </Select>\r\n                </Col>\r\n\r\n                <Col span={12}>\r\n                  <Button\r\n                    type=\"primary\"\r\n                    icon={<SearchOutlined />}\r\n                    onClick={handleSearch}\r\n                    block\r\n                    size=\"large\"\r\n                    style={{ height: '44px' }}\r\n                  >\r\n                    搜索\r\n                  </Button>\r\n                </Col>\r\n\r\n                <Col span={12}>\r\n                  <Button\r\n                    onClick={handleRefresh}\r\n                    block\r\n                    size=\"large\"\r\n                    style={{ height: '44px' }}\r\n                  >\r\n                    刷新\r\n                  </Button>\r\n                </Col>\r\n              </Row>\r\n            </div>\r\n          ) : (\r\n            // 桌面端筛选器布局\r\n            <Space style={{ marginBottom: 16 }}>\r\n              <Select\r\n                placeholder=\"选择作业任务\"\r\n                style={{ width: 200 }}\r\n                value={filters.assignmentId}\r\n                onChange={handleAssignmentChange}\r\n                loading={loading}\r\n              >\r\n                {assignments.map(assignment => (\r\n                  <Option key={assignment.id} value={assignment.id}>\r\n                    {assignment.title}\r\n                  </Option>\r\n                ))}\r\n              </Select>\r\n\r\n              <Input\r\n                placeholder=\"搜索作业标题或学生\"\r\n                value={filters.search}\r\n                onChange={(e) => setFilters({ ...filters, search: e.target.value })}\r\n                style={{ width: 200 }}\r\n              />\r\n\r\n              <Select\r\n                placeholder=\"选择状态\"\r\n                style={{ width: 120 }}\r\n                value={filters.status}\r\n                onChange={(value) => setFilters({ ...filters, status: value })}\r\n              >\r\n                <Option value=\"\">全部</Option>\r\n                <Option value=\"submitted\">待批改</Option>\r\n                <Option value=\"grading\">批改中</Option>\r\n                <Option value=\"graded\">已批改</Option>\r\n              </Select>\r\n\r\n              <Button\r\n                type=\"primary\"\r\n                icon={<SearchOutlined />}\r\n                onClick={handleSearch}\r\n              >\r\n                搜索\r\n              </Button>\r\n\r\n              <Button onClick={handleRefresh}>\r\n                刷新\r\n              </Button>\r\n            </Space>\r\n          )}\r\n        </Card>\r\n        \r\n        {/* 错误提示 */}\r\n        {error && (\r\n          <Alert\r\n            message=\"获取数据时出现错误\"\r\n            description={error}\r\n            type=\"error\"\r\n            showIcon\r\n            style={{ marginBottom: 16 }}\r\n            action={\r\n              <Button size=\"small\" onClick={handleRefresh}>\r\n                重试\r\n              </Button>\r\n            }\r\n          />\r\n        )}\r\n        \r\n        {/* 数据表格 */}\r\n        <div className=\"teacher-page\">\r\n          <Spin spinning={loading}>\r\n            {homeworks.length > 0 ? (\r\n              isMobile ? (\r\n                // 移动端卡片式布局\r\n                <div style={{ padding: '0 4px' }}>\r\n                  {renderMobileCards()}\r\n                  {/* 移动端分页 */}\r\n                  {pagination.total > pagination.pageSize && (\r\n                    <div style={{\r\n                      textAlign: 'center',\r\n                      marginTop: '20px',\r\n                      padding: '16px 0'\r\n                    }}>\r\n                      <Space>\r\n                        <Button\r\n                          disabled={pagination.current === 1}\r\n                          onClick={() => handleTableChange({\r\n                            current: pagination.current - 1,\r\n                            pageSize: pagination.pageSize\r\n                          })}\r\n                          style={{ height: '40px' }}\r\n                        >\r\n                          上一页\r\n                        </Button>\r\n                        <span style={{\r\n                          padding: '0 16px',\r\n                          fontSize: '14px',\r\n                          color: '#666'\r\n                        }}>\r\n                          {pagination.current} / {Math.ceil(pagination.total / pagination.pageSize)}\r\n                        </span>\r\n                        <Button\r\n                          disabled={pagination.current >= Math.ceil(pagination.total / pagination.pageSize)}\r\n                          onClick={() => handleTableChange({\r\n                            current: pagination.current + 1,\r\n                            pageSize: pagination.pageSize\r\n                          })}\r\n                          style={{ height: '40px' }}\r\n                        >\r\n                          下一页\r\n                        </Button>\r\n                      </Space>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              ) : (\r\n                // 桌面端表格布局\r\n                <Table\r\n                  columns={columns}\r\n                  dataSource={homeworks}\r\n                  rowKey=\"id\"\r\n                  pagination={pagination}\r\n                  onChange={handleTableChange}\r\n                  loading={false}\r\n                />\r\n              )\r\n            ) : (\r\n              <Empty description={\r\n                loading ? \"加载中...\" : \"暂无今日作业\"\r\n              } />\r\n            )}\r\n          </Spin>\r\n        </div>\r\n      </>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <div className={`pending-review-homeworks-container teacher-page ${isMobile ? 'mobile' : 'desktop'}`}>\r\n      <div style={{\r\n        marginBottom: isMobile ? '16px' : '24px',\r\n        padding: isMobile ? '0 4px' : '0'\r\n      }}>\r\n        <Title level={isMobile ? 4 : 3} style={{ marginBottom: '8px' }}>\r\n          📚 今日作业\r\n        </Title>\r\n        <Text type=\"secondary\" style={{ fontSize: isMobile ? '14px' : '16px' }}>\r\n          显示所有未结束作业任务的作业提交，包括待批改和批改中的作业\r\n        </Text>\r\n      </div>\r\n\r\n      {renderContent()}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PendingReviewHomeworks; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,MAAM,QAAQ,OAAO;AACvE,SAASC,WAAW,EAAEC,eAAe,EAAEC,WAAW,QAAQ,kBAAkB;AAC5E,SACEC,KAAK,EAAEC,GAAG,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,UAAU,EAAEC,KAAK,EAAEC,OAAO,EAC7DC,KAAK,EAAEC,KAAK,EAAEC,IAAI,EAAEC,UAAU,EAAEC,UAAU,EAAEC,IAAI,EAAEC,IAAI,EAAEC,GAAG,EAAEC,GAAG,EAAEC,MAAM,QACnE,MAAM;AACb,SACEC,cAAc,EAAEC,gBAAgB,EAAEC,mBAAmB,EAAEC,cAAc,EACrEC,YAAY,EAAEC,mBAAmB,EAAEC,gBAAgB,EAAEC,YAAY,QAC5D,mBAAmB;AAC1B,SAASC,YAAY,EAAEC,cAAc,EAAEC,sBAAsB,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEpF,MAAM;EAAEC;AAAO,CAAC,GAAG7B,MAAM;AACzB,MAAM;EAAE8B;AAAY,CAAC,GAAG7B,UAAU;AAClC,MAAM;EAAE8B,KAAK;EAAEC;AAAK,CAAC,GAAGzB,UAAU;;AAElC;AACA,MAAM0B,QAAQ,GAAGA,CAACC,EAAE,EAAEC,KAAK,KAAK;EAC9B,IAAIC,KAAK,GAAG,IAAI;EAChB,OAAO,UAAS,GAAGC,IAAI,EAAE;IACvB,IAAID,KAAK,EAAEE,YAAY,CAACF,KAAK,CAAC;IAC9BA,KAAK,GAAGG,UAAU,CAAC,MAAM;MACvBL,EAAE,CAACM,KAAK,CAAC,IAAI,EAAEH,IAAI,CAAC;IACtB,CAAC,EAAEF,KAAK,CAAC;EACX,CAAC;AACH,CAAC;AAED,MAAMM,sBAAsB,GAAGA,CAAC;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EAC3C,MAAMC,QAAQ,GAAGnD,WAAW,CAAC,CAAC;EAC9B,MAAMoD,QAAQ,GAAGlD,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACmD,YAAY,CAAC,GAAGpD,eAAe,CAAC,CAAC;EACxC,MAAM,CAACqD,OAAO,EAAEC,UAAU,CAAC,GAAG3D,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC4D,SAAS,EAAEC,YAAY,CAAC,GAAG7D,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC8D,WAAW,EAAEC,cAAc,CAAC,GAAG/D,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACgE,UAAU,EAAEC,aAAa,CAAC,GAAGjE,QAAQ,CAAC;IAC3CkE,OAAO,EAAE,CAAC;IACVC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGtE,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACuE,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGxE,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACyE,sBAAsB,EAAEC,yBAAyB,CAAC,GAAG1E,QAAQ,CAAC,IAAI,CAAC;EAC1E,MAAM,CAAC2E,QAAQ,EAAEC,WAAW,CAAC,GAAG5E,QAAQ,CAAC6E,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC;;EAElE;EACA,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGhF,QAAQ,CAAC;IACrCiF,MAAM,EAAE,EAAE;IAAG;IACbC,MAAM,EAAEzB,YAAY,CAAC0B,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE;IACxCC,SAAS,EAAE,IAAI;IACfC,YAAY,EAAE;EAChB,CAAC,CAAC;;EAEF;EACApF,SAAS,CAAC,MAAM;IACd,MAAMqF,YAAY,GAAGA,CAAA,KAAM;MACzBV,WAAW,CAACC,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC;IACvC,CAAC;IAEDD,MAAM,CAACU,gBAAgB,CAAC,QAAQ,EAAED,YAAY,CAAC;IAC/C,OAAO,MAAMT,MAAM,CAACW,mBAAmB,CAAC,QAAQ,EAAEF,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMG,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFjB,qBAAqB,CAAC,IAAI,CAAC;MAC3BkB,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC;MACzB,MAAMC,QAAQ,GAAG,MAAMzD,sBAAsB,CAAC,CAAC;MAC/CuD,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEC,QAAQ,CAAC;MAEpC,IAAIC,eAAe,GAAG,EAAE;MAExB,IAAID,QAAQ,IAAIA,QAAQ,CAACE,KAAK,EAAE;QAC9BD,eAAe,GAAGD,QAAQ,CAACE,KAAK;MAClC,CAAC,MAAM,IAAIC,KAAK,CAACC,OAAO,CAACJ,QAAQ,CAAC,EAAE;QAClCC,eAAe,GAAGD,QAAQ;MAC5B;MAEAF,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEE,eAAe,CAAC;;MAE3C;MACA,MAAMI,oBAAoB,GAAGJ,eAAe,CAACK,GAAG,CAACC,UAAU,IAAI;QAC7D,IAAIlB,MAAM,GAAGkB,UAAU,CAAClB,MAAM;;QAE9B;QACA,IAAI,CAACA,MAAM,IAAIkB,UAAU,CAACC,WAAW,EAAE;UACrC;UACA,MAAMC,WAAW,GAAGF,UAAU,CAACC,WAAW,CAACE,KAAK,CAAC,YAAY,CAAC;UAC9D,IAAID,WAAW,EAAE;YACfpB,MAAM,GAAGoB,WAAW,CAAC,CAAC,CAAC;UACzB,CAAC,MAAM;YACLpB,MAAM,GAAG,QAAQ,CAAC,CAAC;UACrB;QACF,CAAC,MAAM,IAAI,CAACA,MAAM,EAAE;UAClBA,MAAM,GAAG,QAAQ,CAAC,CAAC;QACrB;QAEA,OAAO;UACL,GAAGkB,UAAU;UACblB,MAAM,EAAEA;QACV,CAAC;MACH,CAAC,CAAC;MAEFS,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEM,oBAAoB,CAAC;;MAEhD;MACA,MAAMM,iBAAiB,GAAGN,oBAAoB,CAACO,MAAM,CAACL,UAAU,IAC9DA,UAAU,CAAClB,MAAM,KAAK,UACxB,CAAC;MAEDS,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEY,iBAAiB,CAAC;MAC3CxC,cAAc,CAACwC,iBAAiB,CAAC;;MAEjC;MACA,IAAIA,iBAAiB,CAACE,MAAM,GAAG,CAAC,IAAI,CAAC1B,OAAO,CAACM,YAAY,EAAE;QACzDK,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEY,iBAAiB,CAAC,CAAC,CAAC,CAAC;QAC/C,MAAMG,iBAAiB,GAAGH,iBAAiB,CAAC,CAAC,CAAC,CAACI,EAAE;;QAEjD;QACAjC,yBAAyB,CAACgC,iBAAiB,CAAC;QAE5C1B,UAAU,CAAC4B,IAAI,KAAK;UAClB,GAAGA,IAAI;UACPvB,YAAY,EAAEqB;QAChB,CAAC,CAAC,CAAC;;QAEH;QACAG,cAAc,CAAC;UACbC,aAAa,EAAEJ;QACjB,CAAC,CAAC;MACJ,CAAC,MAAM,IAAIH,iBAAiB,CAACE,MAAM,KAAK,CAAC,EAAE;QACzC;QACAf,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;QAChC9B,YAAY,CAAC,EAAE,CAAC;QAChBF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC,CAAC,OAAOU,KAAK,EAAE;MACdqB,OAAO,CAACrB,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACnCvD,OAAO,CAACuD,KAAK,CAAC,YAAY,CAAC;MAC3BR,YAAY,CAAC,EAAE,CAAC;MAChBF,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,SAAS;MACRa,qBAAqB,CAAC,KAAK,CAAC;IAC9B;EACF,CAAC;;EAED;EACA,MAAMqC,cAAc,GAAG,MAAAA,CAAOE,MAAM,GAAG,CAAC,CAAC,KAAK;IAC5C;IACA,IAAI,CAACA,MAAM,CAACD,aAAa,IAAI,CAAC/B,OAAO,CAACM,YAAY,EAAE;MAClDK,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;MAC/B9B,YAAY,CAAC,EAAE,CAAC;MAChBF,UAAU,CAAC,KAAK,CAAC;MACjB;IACF;IAEA,IAAI;MACFA,UAAU,CAAC,IAAI,CAAC;MAChBW,QAAQ,CAAC,IAAI,CAAC;;MAEd;MACA,MAAM0C,SAAS,GAAG;QAChBC,IAAI,EAAEF,MAAM,CAACE,IAAI,IAAIjD,UAAU,CAACE,OAAO;QACvCgD,KAAK,EAAEH,MAAM,CAAC5C,QAAQ,IAAIH,UAAU,CAACG,QAAQ;QAC7Cc,MAAM,EAAE8B,MAAM,CAAC9B,MAAM,IAAIF,OAAO,CAACE,MAAM;QACvCC,MAAM,EAAE6B,MAAM,CAAC7B,MAAM,IAAIH,OAAO,CAACG,MAAM;QACvC4B,aAAa,EAAEC,MAAM,CAACD,aAAa,IAAI/B,OAAO,CAACM;MACjD,CAAC;;MAED;MACA,IAAI0B,MAAM,CAAC3B,SAAS,IAAIL,OAAO,CAACK,SAAS,EAAE;QACzC,MAAMA,SAAS,GAAG2B,MAAM,CAAC3B,SAAS,IAAIL,OAAO,CAACK,SAAS;QACvD,IAAIA,SAAS,IAAIA,SAAS,CAACqB,MAAM,KAAK,CAAC,EAAE;UACvCO,SAAS,CAACG,UAAU,GAAG/B,SAAS,CAAC,CAAC,CAAC,CAACgC,MAAM,CAAC,YAAY,CAAC;UACxDJ,SAAS,CAACK,QAAQ,GAAGjC,SAAS,CAAC,CAAC,CAAC,CAACgC,MAAM,CAAC,YAAY,CAAC;QACxD;MACF;MAEA1B,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEqB,SAAS,CAAC;;MAEvC;MACA,MAAMM,IAAI,GAAG,MAAMrF,YAAY,CAAC+E,SAAS,CAAC;MAC1CtB,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE2B,IAAI,CAAC;;MAE/B;MACA,IAAIA,IAAI,IAAIvB,KAAK,CAACC,OAAO,CAACsB,IAAI,CAACxB,KAAK,CAAC,EAAE;QACrCjC,YAAY,CAACyD,IAAI,CAACxB,KAAK,CAAC;QACxB7B,aAAa,CAAC;UACZ,GAAGD,UAAU;UACbE,OAAO,EAAE6C,MAAM,CAACE,IAAI,IAAIjD,UAAU,CAACE,OAAO;UAC1CE,KAAK,EAAEkD,IAAI,CAAClD,KAAK,IAAIkD,IAAI,CAACxB,KAAK,CAACW;QAClC,CAAC,CAAC;MACJ,CAAC,MAAM,IAAIV,KAAK,CAACC,OAAO,CAACsB,IAAI,CAAC,EAAE;QAC9BzD,YAAY,CAACyD,IAAI,CAAC;QAClBrD,aAAa,CAAC;UACZ,GAAGD,UAAU;UACbE,OAAO,EAAE6C,MAAM,CAACE,IAAI,IAAIjD,UAAU,CAACE,OAAO;UAC1CE,KAAK,EAAEkD,IAAI,CAACb;QACd,CAAC,CAAC;MACJ,CAAC,MAAM,IAAIa,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAIA,IAAI,CAACX,EAAE,EAAE;QACtD9C,YAAY,CAAC,CAACyD,IAAI,CAAC,CAAC;QACpBrD,aAAa,CAAC;UACZ,GAAGD,UAAU;UACbE,OAAO,EAAE,CAAC;UACVE,KAAK,EAAE;QACT,CAAC,CAAC;MACJ,CAAC,MAAM;QACLsB,OAAO,CAAC6B,IAAI,CAAC,mBAAmB,EAAED,IAAI,CAAC;QACvCzD,YAAY,CAAC,EAAE,CAAC;QAChBI,aAAa,CAAC;UACZ,GAAGD,UAAU;UACbE,OAAO,EAAE,CAAC;UACVE,KAAK,EAAE;QACT,CAAC,CAAC;QACFE,QAAQ,CAAC,oBAAoB,CAAC;QAC9BxD,OAAO,CAAC0G,OAAO,CAAC,WAAW,CAAC;MAC9B;IACF,CAAC,CAAC,OAAOnD,KAAK,EAAE;MACdqB,OAAO,CAACrB,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpCC,QAAQ,CAAC,gBAAgBD,KAAK,CAACvD,OAAO,IAAI,MAAM,EAAE,CAAC;MACnD+C,YAAY,CAAC,EAAE,CAAC;MAChBI,aAAa,CAAC;QACZ,GAAGD,UAAU;QACbE,OAAO,EAAE,CAAC;QACVE,KAAK,EAAE;MACT,CAAC,CAAC;MACFtD,OAAO,CAACuD,KAAK,CAAC,mBAAmB,CAAC;IACpC,CAAC,SAAS;MACR;MACAV,UAAU,CAAC,KAAK,CAAC;MACjB+B,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;IACzC;EACF,CAAC;;EAED;EACA1F,SAAS,CAAC,MAAM;IACdwF,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAxF,SAAS,CAAC,MAAM;IACd,IAAIuD,QAAQ,CAACiE,KAAK,IAAIjE,QAAQ,CAACiE,KAAK,CAACC,OAAO,EAAE;MAC5ChC,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;;MAE7B;MACA,MAAMgC,YAAY,GAAGnE,QAAQ,CAACiE,KAAK,CAACE,YAAY,KAAK,IAAI;MACzD,MAAMC,SAAS,GAAGpE,QAAQ,CAACiE,KAAK,CAACG,SAAS,IAAI,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;MAClEpC,OAAO,CAACC,GAAG,CAAC,SAASgC,YAAY,UAAUC,SAAS,EAAE,CAAC;;MAEvD;MACAnC,gBAAgB,CAAC,CAAC;;MAElB;MACA,IAAIV,OAAO,CAACM,YAAY,EAAE;QACxBK,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEZ,OAAO,CAACM,YAAY,CAAC;;QAErD;QACAX,yBAAyB,CAACK,OAAO,CAACM,YAAY,CAAC;QAE/CwB,cAAc,CAAC;UACbC,aAAa,EAAE/B,OAAO,CAACM,YAAY;UACnC4B,IAAI,EAAE,CAAC;UAAG;UACVc,KAAK,EAAE,KAAK;UAAG;UACfC,EAAE,EAAEJ,SAAS,CAAE;QACjB,CAAC,CAAC;MACJ;;MAEA;MACArE,QAAQ,CAACC,QAAQ,CAACyE,QAAQ,EAAE;QAAEC,OAAO,EAAE,IAAI;QAAET,KAAK,EAAE,CAAC;MAAE,CAAC,CAAC;IAC3D;EACF,CAAC,EAAE,CAACjE,QAAQ,CAACiE,KAAK,EAAElE,QAAQ,EAAEC,QAAQ,CAACyE,QAAQ,EAAElD,OAAO,CAACM,YAAY,CAAC,CAAC;;EAEvE;EACA,MAAM8C,iBAAiB,GAAGA,CAACnE,UAAU,EAAEe,OAAO,KAAK;IACjD8B,cAAc,CAAC;MACbI,IAAI,EAAEjD,UAAU,CAACE,OAAO;MACxBC,QAAQ,EAAEH,UAAU,CAACG,QAAQ;MAC7B,GAAGY;IACL,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMqD,YAAY,GAAGA,CAAA,KAAM;IACzBvB,cAAc,CAAC;MAAEI,IAAI,EAAE,CAAC;MAAE,GAAGlC;IAAQ,CAAC,CAAC;EACzC,CAAC;;EAED;EACA,MAAMsD,gBAAgB,GAAI1B,EAAE,IAAK;IAC/BpD,QAAQ,CAAC,aAAaoD,EAAE,aAAa,CAAC;EACxC,CAAC;;EAED;EACA,MAAM2B,aAAa,GAAGA,CAAA,KAAM;IAC1B5C,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;IAC1BF,gBAAgB,CAAC,CAAC;;IAElB;IACA,IAAIV,OAAO,CAACM,YAAY,EAAE;MACxBK,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEZ,OAAO,CAACM,YAAY,CAAC;;MAErD;MACAX,yBAAyB,CAACK,OAAO,CAACM,YAAY,CAAC;MAE/CwB,cAAc,CAAC;QACbC,aAAa,EAAE/B,OAAO,CAACM,YAAY;QACnC4B,IAAI,EAAE,CAAC;QAAG;QACVc,KAAK,EAAE,KAAK,CAAE;MAChB,CAAC,CAAC;IACJ;IAEAjH,OAAO,CAACyH,IAAI,CAAC,gBAAgB,CAAC;EAChC,CAAC;;EAED;EACA,MAAMC,sBAAsB,GAAIC,KAAK,IAAK;IACxC;IACA,IAAIA,KAAK,KAAKhE,sBAAsB,EAAE;MACpCiB,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;MAC9B;IACF;;IAEA;IACAjB,yBAAyB,CAAC+D,KAAK,CAAC;IAEhCzD,UAAU,CAAC4B,IAAI,KAAK;MAClB,GAAGA,IAAI;MACPvB,YAAY,EAAEoD;IAChB,CAAC,CAAC,CAAC;;IAEH;IACA5B,cAAc,CAAC;MACbC,aAAa,EAAE2B,KAAK;MACpBxB,IAAI,EAAE;IACR,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMyB,YAAY,GAAG,MAAO/B,EAAE,IAAK;IACjC,IAAI;MACF,MAAMzE,cAAc,CAACyE,EAAE,CAAC;MACxB7F,OAAO,CAAC6H,OAAO,CAAC,QAAQ,CAAC;MACzB9B,cAAc,CAAC,CAAC;IAClB,CAAC,CAAC,OAAOxC,KAAK,EAAE;MACdqB,OAAO,CAACrB,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BvD,OAAO,CAACuD,KAAK,CAAC,WAAWA,KAAK,CAACvD,OAAO,IAAI,MAAM,EAAE,CAAC;IACrD;EACF,CAAC;;EAED;EACA,MAAM8H,YAAY,GAAI3D,MAAM,IAAK;IAC/B,QAAQA,MAAM;MACZ,KAAK,WAAW;QACd,oBAAO5C,OAAA,CAAC7B,GAAG;UAACqI,KAAK,EAAC,MAAM;UAACC,IAAI,eAAEzG,OAAA,CAACP,mBAAmB;YAAAiH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAC,QAAA,EAAC;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MACnE,KAAK,SAAS;MACd,KAAK,YAAY;QACf,oBAAO7G,OAAA,CAAC7B,GAAG;UAACqI,KAAK,EAAC,QAAQ;UAACC,IAAI,eAAEzG,OAAA,CAACR,YAAY;YAACuH,IAAI;UAAA;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAC,QAAA,EAAC;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MACnE,KAAK,QAAQ;MACb,KAAK,WAAW;QACd,oBAAO7G,OAAA,CAAC7B,GAAG;UAACqI,KAAK,EAAC,OAAO;UAACC,IAAI,eAAEzG,OAAA,CAACV,mBAAmB;YAAAoH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAC,QAAA,EAAC;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MACpE;QACE,oBAAO7G,OAAA,CAAC7B,GAAG;UAACqI,KAAK,EAAC,SAAS;UAAAM,QAAA,EAAC;QAAI;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;IAC1C;EACF,CAAC;;EAED;EACA,MAAMG,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAIzF,SAAS,CAAC6C,MAAM,KAAK,CAAC,EAAE;MAC1B,oBACEpE,OAAA;QAAKiH,KAAK,EAAE;UAAEC,SAAS,EAAE,QAAQ;UAAEC,OAAO,EAAE;QAAY,CAAE;QAAAL,QAAA,gBACxD9G,OAAA,CAACX,gBAAgB;UAAC4H,KAAK,EAAE;YAAEG,QAAQ,EAAE,MAAM;YAAEZ,KAAK,EAAE,SAAS;YAAEa,YAAY,EAAE;UAAO;QAAE;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzF7G,OAAA;UAAKiH,KAAK,EAAE;YAAEG,QAAQ,EAAE,MAAM;YAAEZ,KAAK,EAAE,SAAS;YAAEa,YAAY,EAAE;UAAM,CAAE;UAAAP,QAAA,EAAC;QAEzE;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACN7G,OAAA;UAAKiH,KAAK,EAAE;YAAEG,QAAQ,EAAE,MAAM;YAAEZ,KAAK,EAAE;UAAU,CAAE;UAAAM,QAAA,EAAC;QAEpD;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAEV;IAEA,oBACE7G,OAAA,CAACjB,IAAI;MACHuI,UAAU,EAAE/F,SAAU;MACtBgG,UAAU,EAAGC,QAAQ,iBACnBxH,OAAA,CAACjB,IAAI,CAAC0I,IAAI;QAACR,KAAK,EAAE;UAAEE,OAAO,EAAE,CAAC;UAAEE,YAAY,EAAE;QAAO,CAAE;QAAAP,QAAA,eACrD9G,OAAA,CAAChB,IAAI;UACH0I,SAAS;UACTT,KAAK,EAAE;YACLU,KAAK,EAAE,MAAM;YACbC,YAAY,EAAE,MAAM;YACpBC,MAAM,EAAE,mBAAmB;YAC3BC,SAAS,EAAE;UACb,CAAE;UACFC,SAAS,EAAE;YAAEZ,OAAO,EAAE;UAAO,CAAE;UAAAL,QAAA,gBAG/B9G,OAAA;YAAKiH,KAAK,EAAE;cAAEI,YAAY,EAAE;YAAO,CAAE;YAAAP,QAAA,eACnC9G,OAAA,CAACf,GAAG;cAAC+I,OAAO,EAAC,eAAe;cAACC,KAAK,EAAC,KAAK;cAAAnB,QAAA,eACtC9G,OAAA,CAACd,GAAG;gBAACgJ,IAAI,EAAC,GAAG;gBAAApB,QAAA,gBACX9G,OAAA;kBAAKiH,KAAK,EAAE;oBACVG,QAAQ,EAAE,MAAM;oBAChBe,UAAU,EAAE,KAAK;oBACjB3B,KAAK,EAAE,SAAS;oBAChBa,YAAY,EAAE,KAAK;oBACnBe,UAAU,EAAE;kBACd,CAAE;kBAAAtB,QAAA,EACCU,QAAQ,CAACa;gBAAK;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC,eACN7G,OAAA,CAACxB,KAAK;kBAAC8J,IAAI,EAAC,OAAO;kBAACC,IAAI;kBAAAzB,QAAA,GACrBP,YAAY,CAACiB,QAAQ,CAAC5E,MAAM,CAAC,EAC7B4E,QAAQ,CAACgB,UAAU,iBAClBxI,OAAA,CAAC7B,GAAG;oBAACqI,KAAK,EAAC,MAAM;oBAAAM,QAAA,EACdU,QAAQ,CAACgB;kBAAU;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN7G,OAAA,CAACf,GAAG;YAACwJ,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC,CAAE;YAACxB,KAAK,EAAE;cAAEI,YAAY,EAAE;YAAO,CAAE;YAAAP,QAAA,gBACpD9G,OAAA,CAACd,GAAG;cAACwJ,IAAI,EAAE,EAAG;cAAA5B,QAAA,gBACZ9G,OAAA;gBAAKiH,KAAK,EAAE;kBAAEG,QAAQ,EAAE,MAAM;kBAAEZ,KAAK,EAAE;gBAAU,CAAE;gBAAAM,QAAA,EAAC;cAAE;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC5D7G,OAAA;gBAAKiH,KAAK,EAAE;kBAAEG,QAAQ,EAAE,MAAM;kBAAEZ,KAAK,EAAE,SAAS;kBAAE2B,UAAU,EAAE;gBAAM,CAAE;gBAAArB,QAAA,gBACpE9G,OAAA,CAACL,YAAY;kBAACsH,KAAK,EAAE;oBAAE0B,WAAW,EAAE;kBAAM;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EAC9C7F,IAAI,CAAC4H,UAAU,GACbpB,QAAQ,CAACqB,YAAY,IAAI,SAASrB,QAAQ,CAACsB,UAAU,EAAE,GACvDtB,QAAQ,CAACuB,gBAAgB,IAAIvB,QAAQ,CAACa,KAAK,IAAI,GAAI;cAAA;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEnD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN7G,OAAA,CAACd,GAAG;cAACwJ,IAAI,EAAE,EAAG;cAAA5B,QAAA,gBACZ9G,OAAA;gBAAKiH,KAAK,EAAE;kBAAEG,QAAQ,EAAE,MAAM;kBAAEZ,KAAK,EAAE;gBAAU,CAAE;gBAAAM,QAAA,EAAC;cAAI;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC9D7G,OAAA;gBAAKiH,KAAK,EAAE;kBAAEG,QAAQ,EAAE,MAAM;kBAAEZ,KAAK,EAAE,SAAS;kBAAE2B,UAAU,EAAE;gBAAM,CAAE;gBAAArB,QAAA,gBACpE9G,OAAA,CAACN,gBAAgB;kBAACuH,KAAK,EAAE;oBAAE0B,WAAW,EAAE;kBAAM;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EAClDW,QAAQ,CAACwB,UAAU,GAClB,IAAIxD,IAAI,CAACgC,QAAQ,CAACwB,UAAU,CAAC,CAACC,kBAAkB,CAAC,CAAC,GAClD,GAAG;cAAA;gBAAAvC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN7G,OAAA;YAAKiH,KAAK,EAAE;cAAEiC,OAAO,EAAE,MAAM;cAAEC,GAAG,EAAE,KAAK;cAAEC,cAAc,EAAE;YAAW,CAAE;YAAAtC,QAAA,gBACtE9G,OAAA,CAAC5B,MAAM;cACLiL,IAAI,EAAC,SAAS;cACdf,IAAI,EAAC,OAAO;cACZ7B,IAAI,eAAEzG,OAAA,CAACX,gBAAgB;gBAAAqH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC3ByC,OAAO,EAAEA,CAAA,KAAMtD,gBAAgB,CAACwB,QAAQ,CAAClD,EAAE,CAAE;cAC7C2C,KAAK,EAAE;gBACLW,YAAY,EAAE,KAAK;gBACnB2B,MAAM,EAAE,MAAM;gBACdnC,QAAQ,EAAE,MAAM;gBAChBe,UAAU,EAAE,KAAK;gBACjBqB,WAAW,EAAE;cACf,CAAE;cAAA1C,QAAA,EACH;YAED;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAER7F,IAAI,CAAC4H,UAAU,IAAIpB,QAAQ,CAAC5E,MAAM,KAAK,WAAW,iBACjD5C,OAAA,CAAC5B,MAAM;cACLiL,IAAI,EAAC,SAAS;cACdf,IAAI,EAAC,OAAO;cACZ7B,IAAI,eAAEzG,OAAA,CAACV,mBAAmB;gBAAAoH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC9ByC,OAAO,EAAEA,CAAA,KAAMpI,QAAQ,CAAC,aAAasG,QAAQ,CAAClD,EAAE,aAAa,EAAE;gBAAEc,KAAK,EAAE;kBAAEqE,eAAe,EAAE;gBAAK;cAAE,CAAC,CAAE;cACrGxC,KAAK,EAAE;gBACLW,YAAY,EAAE,KAAK;gBACnB2B,MAAM,EAAE,MAAM;gBACdnC,QAAQ,EAAE,MAAM;gBAChBe,UAAU,EAAE,KAAK;gBACjBuB,UAAU,EAAE,SAAS;gBACrBC,WAAW,EAAE,SAAS;gBACtBH,WAAW,EAAE;cACf,CAAE;cAAA1C,QAAA,EACH;YAED;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT,EAEA7F,IAAI,CAAC4H,UAAU,iBACd5I,OAAA,CAAClB,UAAU;cACTuJ,KAAK,EAAC,wDAAW;cACjBuB,SAAS,EAAEA,CAAA,KAAMvD,YAAY,CAACmB,QAAQ,CAAClD,EAAE,CAAE;cAC3CuF,MAAM,EAAC,cAAI;cACXC,UAAU,EAAC,cAAI;cAAAhD,QAAA,eAEf9G,OAAA,CAAC5B,MAAM;gBACL2L,MAAM;gBACNzB,IAAI,EAAC,OAAO;gBACZ7B,IAAI,eAAEzG,OAAA,CAACT,cAAc;kBAAAmH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACzBI,KAAK,EAAE;kBACLW,YAAY,EAAE,KAAK;kBACnB2B,MAAM,EAAE,MAAM;kBACdnC,QAAQ,EAAE,MAAM;kBAChBoC,WAAW,EAAE;gBACf,CAAE;gBAAA1C,QAAA,EACH;cAED;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IACX;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEN,CAAC;;EAED;EACA,MAAMmD,OAAO,GAAG,CACd;IACE3B,KAAK,EAAE,IAAI;IACX4B,SAAS,EAAE,OAAO;IAClBC,GAAG,EAAE,OAAO;IACZC,MAAM,EAAEA,CAACC,IAAI,EAAEC,MAAM,kBACnBrK,OAAA;MAAGsK,IAAI,EAAE,aAAaD,MAAM,CAAC/F,EAAE,EAAG;MAACgF,OAAO,EAAGiB,CAAC,IAAK;QACjDA,CAAC,CAACC,cAAc,CAAC,CAAC;QAClBxE,gBAAgB,CAACqE,MAAM,CAAC/F,EAAE,CAAC;MAC7B,CAAE;MAAAwC,QAAA,EAAEsD;IAAI;MAAA1D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI;EAEhB,CAAC,EACD;IACEwB,KAAK,EAAErH,IAAI,CAAC4H,UAAU,GAAG,IAAI,GAAG,MAAM;IACtCqB,SAAS,EAAEjJ,IAAI,CAAC4H,UAAU,GAAG,cAAc,GAAG,kBAAkB;IAChEsB,GAAG,EAAElJ,IAAI,CAAC4H,UAAU,GAAG,cAAc,GAAG,kBAAkB;IAC1DuB,MAAM,EAAEA,CAACC,IAAI,EAAEC,MAAM,KAAK;MACxB,IAAIrJ,IAAI,CAAC4H,UAAU,EAAE;QACnB,OAAOwB,IAAI,IAAI,SAASC,MAAM,CAACvB,UAAU,EAAE;MAC7C,CAAC,MAAM;QACL,OAAOsB,IAAI,IAAIC,MAAM,CAAChC,KAAK,IAAI,GAAG;MACpC;IACF;EACF,CAAC,EACD;IACEA,KAAK,EAAE,IAAI;IACX4B,SAAS,EAAE,YAAY;IACvBC,GAAG,EAAE,YAAY;IACjBC,MAAM,EAAEA,CAACC,IAAI,EAAEC,MAAM,KAAK;MACxB,IAAID,IAAI,EAAE;QACR,OAAOA,IAAI;MACb,CAAC,MAAM,IAAIC,MAAM,CAACI,QAAQ,EAAE;QAC1B,OAAO,SAASJ,MAAM,CAACI,QAAQ,EAAE;MACnC,CAAC,MAAM;QACL,OAAO,GAAG;MACZ;IACF;EACF,CAAC,EACD;IACEpC,KAAK,EAAE,IAAI;IACX4B,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAGvH,MAAM,IAAK2D,YAAY,CAAC3D,MAAM;EACzC,CAAC,EACD;IACEyF,KAAK,EAAE,MAAM;IACb4B,SAAS,EAAE,YAAY;IACvBC,GAAG,EAAE,YAAY;IACjBC,MAAM,EAAGO,IAAI,IAAK,IAAIlF,IAAI,CAACkF,IAAI,CAAC,CAACC,cAAc,CAAC;EAClD,CAAC,EACD;IACEtC,KAAK,EAAE,IAAI;IACX6B,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAEA,CAACS,CAAC,EAAEP,MAAM,kBAChBrK,OAAA,CAACxB,KAAK;MAAAsI,QAAA,gBACJ9G,OAAA,CAAC5B,MAAM;QACLiL,IAAI,EAAC,SAAS;QACd5C,IAAI,eAAEzG,OAAA,CAACX,gBAAgB;UAAAqH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC3ByC,OAAO,EAAEA,CAAA,KAAMtD,gBAAgB,CAACqE,MAAM,CAAC/F,EAAE,CAAE;QAAAwC,QAAA,EAC5C;MAED;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EACR7F,IAAI,CAAC4H,UAAU,IAAIyB,MAAM,CAACzH,MAAM,KAAK,WAAW,iBAC/C5C,OAAA,CAAC5B,MAAM;QACLiL,IAAI,EAAC,SAAS;QACd5C,IAAI,eAAEzG,OAAA,CAACV,mBAAmB;UAAAoH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC9ByC,OAAO,EAAEA,CAAA,KAAMpI,QAAQ,CAAC,aAAamJ,MAAM,CAAC/F,EAAE,EAAE,EAAE;UAAEc,KAAK,EAAE;YAAEqE,eAAe,EAAE;UAAK;QAAE,CAAC,CAAE;QAAA3C,QAAA,EACzF;MAED;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT,EACA7F,IAAI,CAAC4H,UAAU,iBACd5I,OAAA,CAAClB,UAAU;QACTuJ,KAAK,EAAC,wDAAW;QACjBuB,SAAS,EAAEA,CAAA,KAAMvD,YAAY,CAACgE,MAAM,CAAC/F,EAAE,CAAE;QACzCuF,MAAM,EAAC,cAAI;QACXC,UAAU,EAAC,cAAI;QAAAhD,QAAA,eAEf9G,OAAA,CAAC5B,MAAM;UACLiL,IAAI,EAAC,SAAS;UACdU,MAAM;UACNtD,IAAI,eAAEzG,OAAA,CAACT,cAAc;YAAAmH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAC,QAAA,EAC1B;QAED;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACb;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EAEX,CAAC,CACF;;EAED;EACA,MAAMgE,aAAa,GAAGA,CAAA,KAAM;IAC1B;IACA,IAAI3I,kBAAkB,EAAE;MACtB,oBACElC,OAAA;QAAKiH,KAAK,EAAE;UAAEC,SAAS,EAAE,QAAQ;UAAEC,OAAO,EAAE;QAAS,CAAE;QAAAL,QAAA,eACrD9G,OAAA,CAACpB,IAAI;UAACkM,GAAG,EAAC;QAAa;UAAApE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvB,CAAC;IAEV;;IAEA;IACA,IAAIpF,WAAW,CAAC2C,MAAM,KAAK,CAAC,EAAE;MAC5B,oBACEpE,OAAA,CAACrB,KAAK;QACJoF,WAAW,EAAC,sCAAQ;QACpBgH,KAAK,EAAEpM,KAAK,CAACqM,sBAAuB;QAAAlE,QAAA,eAEpC9G,OAAA,CAAC5B,MAAM;UAACiL,IAAI,EAAC,SAAS;UAACC,OAAO,EAAEA,CAAA,KAAMpI,QAAQ,CAAC,6BAA6B,CAAE;UAAA4F,QAAA,EAAC;QAE/E;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAEZ;;IAEA;IACA,oBACE7G,OAAA,CAAAE,SAAA;MAAA4G,QAAA,gBAEE9G,OAAA,CAAChB,IAAI;QACHiM,SAAS,EAAC,cAAc;QACxBhE,KAAK,EAAE;UACLI,YAAY,EAAE/E,QAAQ,GAAG,MAAM,GAAG,MAAM;UACxC4I,SAAS,EAAE,MAAM;UACjBtD,YAAY,EAAE,MAAM;UACpBC,MAAM,EAAE;QACV,CAAE;QACFE,SAAS,EAAE;UAAEZ,OAAO,EAAE7E,QAAQ,GAAG,MAAM,GAAG;QAAO,CAAE;QAAAwE,QAAA,EAElDxE,QAAQ;QAAA;QACP;QACAtC,OAAA;UAAA8G,QAAA,gBACE9G,OAAA;YAAKiH,KAAK,EAAE;cACViC,OAAO,EAAE,MAAM;cACfiC,UAAU,EAAE,QAAQ;cACpBhC,GAAG,EAAE,KAAK;cACV9B,YAAY,EAAE;YAChB,CAAE;YAAAP,QAAA,gBACA9G,OAAA,CAACZ,cAAc;cAAC6H,KAAK,EAAE;gBAAET,KAAK,EAAE;cAAU;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/C7G,OAAA;cAAMiH,KAAK,EAAE;gBAAEkB,UAAU,EAAE,GAAG;gBAAE3B,KAAK,EAAE;cAAU,CAAE;cAAAM,QAAA,EAAC;YAAI;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC,eAEN7G,OAAA,CAACf,GAAG;YAACwJ,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;YAAA3B,QAAA,gBACpB9G,OAAA,CAACd,GAAG;cAACwJ,IAAI,EAAE,EAAG;cAAA5B,QAAA,gBACZ9G,OAAA;gBAAKiH,KAAK,EAAE;kBAAEI,YAAY,EAAE;gBAAM,CAAE;gBAAAP,QAAA,eAClC9G,OAAA;kBAAMiH,KAAK,EAAE;oBAAEG,QAAQ,EAAE,MAAM;oBAAEZ,KAAK,EAAE;kBAAO,CAAE;kBAAAM,QAAA,EAAC;gBAAI;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC,eACN7G,OAAA,CAAC1B,MAAM;gBACL8M,WAAW,EAAC,sCAAQ;gBACpBnE,KAAK,EAAE;kBAAEU,KAAK,EAAE;gBAAO,CAAE;gBACzBvB,KAAK,EAAE1D,OAAO,CAACM,YAAa;gBAC5BqI,QAAQ,EAAElF,sBAAuB;gBACjC9E,OAAO,EAAEA,OAAQ;gBACjBiH,IAAI,EAAC,OAAO;gBAAAxB,QAAA,EAEXrF,WAAW,CAACoC,GAAG,CAACC,UAAU,iBACzB9D,OAAA,CAACG,MAAM;kBAAqBiG,KAAK,EAAEtC,UAAU,CAACQ,EAAG;kBAAAwC,QAAA,EAC9ChD,UAAU,CAACuE;gBAAK,GADNvE,UAAU,CAACQ,EAAE;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAElB,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAEN7G,OAAA,CAACd,GAAG;cAACwJ,IAAI,EAAE,EAAG;cAAA5B,QAAA,gBACZ9G,OAAA;gBAAKiH,KAAK,EAAE;kBAAEI,YAAY,EAAE;gBAAM,CAAE;gBAAAP,QAAA,eAClC9G,OAAA;kBAAMiH,KAAK,EAAE;oBAAEG,QAAQ,EAAE,MAAM;oBAAEZ,KAAK,EAAE;kBAAO,CAAE;kBAAAM,QAAA,EAAC;gBAAE;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC,eACN7G,OAAA,CAAC3B,KAAK;gBACJ+M,WAAW,EAAC,wDAAW;gBACvBhF,KAAK,EAAE1D,OAAO,CAACG,MAAO;gBACtBwI,QAAQ,EAAGd,CAAC,IAAK5H,UAAU,CAAC;kBAAE,GAAGD,OAAO;kBAAEG,MAAM,EAAE0H,CAAC,CAACe,MAAM,CAAClF;gBAAM,CAAC,CAAE;gBACpEa,KAAK,EAAE;kBAAEU,KAAK,EAAE;gBAAO,CAAE;gBACzBW,IAAI,EAAC;cAAO;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN7G,OAAA,CAACd,GAAG;cAACwJ,IAAI,EAAE,EAAG;cAAA5B,QAAA,gBACZ9G,OAAA;gBAAKiH,KAAK,EAAE;kBAAEI,YAAY,EAAE;gBAAM,CAAE;gBAAAP,QAAA,eAClC9G,OAAA;kBAAMiH,KAAK,EAAE;oBAAEG,QAAQ,EAAE,MAAM;oBAAEZ,KAAK,EAAE;kBAAO,CAAE;kBAAAM,QAAA,EAAC;gBAAE;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC,eACN7G,OAAA,CAAC1B,MAAM;gBACL8M,WAAW,EAAC,0BAAM;gBAClBnE,KAAK,EAAE;kBAAEU,KAAK,EAAE;gBAAO,CAAE;gBACzBvB,KAAK,EAAE1D,OAAO,CAACE,MAAO;gBACtByI,QAAQ,EAAGjF,KAAK,IAAKzD,UAAU,CAAC;kBAAE,GAAGD,OAAO;kBAAEE,MAAM,EAAEwD;gBAAM,CAAC,CAAE;gBAC/DkC,IAAI,EAAC,OAAO;gBAAAxB,QAAA,gBAEZ9G,OAAA,CAACG,MAAM;kBAACiG,KAAK,EAAC,EAAE;kBAAAU,QAAA,EAAC;gBAAE;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5B7G,OAAA,CAACG,MAAM;kBAACiG,KAAK,EAAC,WAAW;kBAAAU,QAAA,EAAC;gBAAG;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtC7G,OAAA,CAACG,MAAM;kBAACiG,KAAK,EAAC,SAAS;kBAAAU,QAAA,EAAC;gBAAG;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpC7G,OAAA,CAACG,MAAM;kBAACiG,KAAK,EAAC,QAAQ;kBAAAU,QAAA,EAAC;gBAAG;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAEN7G,OAAA,CAACd,GAAG;cAACwJ,IAAI,EAAE,EAAG;cAAA5B,QAAA,eACZ9G,OAAA,CAAC5B,MAAM;gBACLiL,IAAI,EAAC,SAAS;gBACd5C,IAAI,eAAEzG,OAAA,CAACZ,cAAc;kBAAAsH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACzByC,OAAO,EAAEvD,YAAa;gBACtBwF,KAAK;gBACLjD,IAAI,EAAC,OAAO;gBACZrB,KAAK,EAAE;kBAAEsC,MAAM,EAAE;gBAAO,CAAE;gBAAAzC,QAAA,EAC3B;cAED;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAEN7G,OAAA,CAACd,GAAG;cAACwJ,IAAI,EAAE,EAAG;cAAA5B,QAAA,eACZ9G,OAAA,CAAC5B,MAAM;gBACLkL,OAAO,EAAErD,aAAc;gBACvBsF,KAAK;gBACLjD,IAAI,EAAC,OAAO;gBACZrB,KAAK,EAAE;kBAAEsC,MAAM,EAAE;gBAAO,CAAE;gBAAAzC,QAAA,EAC3B;cAED;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;QAAA;QAEN;QACA7G,OAAA,CAACxB,KAAK;UAACyI,KAAK,EAAE;YAAEI,YAAY,EAAE;UAAG,CAAE;UAAAP,QAAA,gBACjC9G,OAAA,CAAC1B,MAAM;YACL8M,WAAW,EAAC,sCAAQ;YACpBnE,KAAK,EAAE;cAAEU,KAAK,EAAE;YAAI,CAAE;YACtBvB,KAAK,EAAE1D,OAAO,CAACM,YAAa;YAC5BqI,QAAQ,EAAElF,sBAAuB;YACjC9E,OAAO,EAAEA,OAAQ;YAAAyF,QAAA,EAEhBrF,WAAW,CAACoC,GAAG,CAACC,UAAU,iBACzB9D,OAAA,CAACG,MAAM;cAAqBiG,KAAK,EAAEtC,UAAU,CAACQ,EAAG;cAAAwC,QAAA,EAC9ChD,UAAU,CAACuE;YAAK,GADNvE,UAAU,CAACQ,EAAE;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAElB,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAET7G,OAAA,CAAC3B,KAAK;YACJ+M,WAAW,EAAC,wDAAW;YACvBhF,KAAK,EAAE1D,OAAO,CAACG,MAAO;YACtBwI,QAAQ,EAAGd,CAAC,IAAK5H,UAAU,CAAC;cAAE,GAAGD,OAAO;cAAEG,MAAM,EAAE0H,CAAC,CAACe,MAAM,CAAClF;YAAM,CAAC,CAAE;YACpEa,KAAK,EAAE;cAAEU,KAAK,EAAE;YAAI;UAAE;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC,eAEF7G,OAAA,CAAC1B,MAAM;YACL8M,WAAW,EAAC,0BAAM;YAClBnE,KAAK,EAAE;cAAEU,KAAK,EAAE;YAAI,CAAE;YACtBvB,KAAK,EAAE1D,OAAO,CAACE,MAAO;YACtByI,QAAQ,EAAGjF,KAAK,IAAKzD,UAAU,CAAC;cAAE,GAAGD,OAAO;cAAEE,MAAM,EAAEwD;YAAM,CAAC,CAAE;YAAAU,QAAA,gBAE/D9G,OAAA,CAACG,MAAM;cAACiG,KAAK,EAAC,EAAE;cAAAU,QAAA,EAAC;YAAE;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC5B7G,OAAA,CAACG,MAAM;cAACiG,KAAK,EAAC,WAAW;cAAAU,QAAA,EAAC;YAAG;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtC7G,OAAA,CAACG,MAAM;cAACiG,KAAK,EAAC,SAAS;cAAAU,QAAA,EAAC;YAAG;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACpC7G,OAAA,CAACG,MAAM;cAACiG,KAAK,EAAC,QAAQ;cAAAU,QAAA,EAAC;YAAG;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eAET7G,OAAA,CAAC5B,MAAM;YACLiL,IAAI,EAAC,SAAS;YACd5C,IAAI,eAAEzG,OAAA,CAACZ,cAAc;cAAAsH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzByC,OAAO,EAAEvD,YAAa;YAAAe,QAAA,EACvB;UAED;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAET7G,OAAA,CAAC5B,MAAM;YAACkL,OAAO,EAAErD,aAAc;YAAAa,QAAA,EAAC;UAEhC;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MACR;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,EAGN7E,KAAK,iBACJhC,OAAA,CAACtB,KAAK;QACJD,OAAO,EAAC,wDAAW;QACnBsF,WAAW,EAAE/B,KAAM;QACnBqH,IAAI,EAAC,OAAO;QACZmC,QAAQ;QACRvE,KAAK,EAAE;UAAEI,YAAY,EAAE;QAAG,CAAE;QAC5BoE,MAAM,eACJzL,OAAA,CAAC5B,MAAM;UAACkK,IAAI,EAAC,OAAO;UAACgB,OAAO,EAAErD,aAAc;UAAAa,QAAA,EAAC;QAE7C;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MACT;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CACF,eAGD7G,OAAA;QAAKiL,SAAS,EAAC,cAAc;QAAAnE,QAAA,eAC3B9G,OAAA,CAACpB,IAAI;UAAC8M,QAAQ,EAAErK,OAAQ;UAAAyF,QAAA,EACrBvF,SAAS,CAAC6C,MAAM,GAAG,CAAC,GACnB9B,QAAQ;UAAA;UACN;UACAtC,OAAA;YAAKiH,KAAK,EAAE;cAAEE,OAAO,EAAE;YAAQ,CAAE;YAAAL,QAAA,GAC9BE,iBAAiB,CAAC,CAAC,EAEnBrF,UAAU,CAACI,KAAK,GAAGJ,UAAU,CAACG,QAAQ,iBACrC9B,OAAA;cAAKiH,KAAK,EAAE;gBACVC,SAAS,EAAE,QAAQ;gBACnBgE,SAAS,EAAE,MAAM;gBACjB/D,OAAO,EAAE;cACX,CAAE;cAAAL,QAAA,eACA9G,OAAA,CAACxB,KAAK;gBAAAsI,QAAA,gBACJ9G,OAAA,CAAC5B,MAAM;kBACLuN,QAAQ,EAAEhK,UAAU,CAACE,OAAO,KAAK,CAAE;kBACnCyH,OAAO,EAAEA,CAAA,KAAMxD,iBAAiB,CAAC;oBAC/BjE,OAAO,EAAEF,UAAU,CAACE,OAAO,GAAG,CAAC;oBAC/BC,QAAQ,EAAEH,UAAU,CAACG;kBACvB,CAAC,CAAE;kBACHmF,KAAK,EAAE;oBAAEsC,MAAM,EAAE;kBAAO,CAAE;kBAAAzC,QAAA,EAC3B;gBAED;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT7G,OAAA;kBAAMiH,KAAK,EAAE;oBACXE,OAAO,EAAE,QAAQ;oBACjBC,QAAQ,EAAE,MAAM;oBAChBZ,KAAK,EAAE;kBACT,CAAE;kBAAAM,QAAA,GACCnF,UAAU,CAACE,OAAO,EAAC,KAAG,EAAC+J,IAAI,CAACC,IAAI,CAAClK,UAAU,CAACI,KAAK,GAAGJ,UAAU,CAACG,QAAQ,CAAC;gBAAA;kBAAA4E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrE,CAAC,eACP7G,OAAA,CAAC5B,MAAM;kBACLuN,QAAQ,EAAEhK,UAAU,CAACE,OAAO,IAAI+J,IAAI,CAACC,IAAI,CAAClK,UAAU,CAACI,KAAK,GAAGJ,UAAU,CAACG,QAAQ,CAAE;kBAClFwH,OAAO,EAAEA,CAAA,KAAMxD,iBAAiB,CAAC;oBAC/BjE,OAAO,EAAEF,UAAU,CAACE,OAAO,GAAG,CAAC;oBAC/BC,QAAQ,EAAEH,UAAU,CAACG;kBACvB,CAAC,CAAE;kBACHmF,KAAK,EAAE;oBAAEsC,MAAM,EAAE;kBAAO,CAAE;kBAAAzC,QAAA,EAC3B;gBAED;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;UAAA;UAEN;UACA7G,OAAA,CAAC9B,KAAK;YACJ8L,OAAO,EAAEA,OAAQ;YACjB1C,UAAU,EAAE/F,SAAU;YACtBuK,MAAM,EAAC,IAAI;YACXnK,UAAU,EAAEA,UAAW;YACvB0J,QAAQ,EAAEvF,iBAAkB;YAC5BzE,OAAO,EAAE;UAAM;YAAAqF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CACF,gBAED7G,OAAA,CAACrB,KAAK;YAACoF,WAAW,EAChB1C,OAAO,GAAG,QAAQ,GAAG;UACtB;YAAAqF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QACJ;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA,eACN,CAAC;EAEP,CAAC;EAED,oBACE7G,OAAA;IAAKiL,SAAS,EAAE,mDAAmD3I,QAAQ,GAAG,QAAQ,GAAG,SAAS,EAAG;IAAAwE,QAAA,gBACnG9G,OAAA;MAAKiH,KAAK,EAAE;QACVI,YAAY,EAAE/E,QAAQ,GAAG,MAAM,GAAG,MAAM;QACxC6E,OAAO,EAAE7E,QAAQ,GAAG,OAAO,GAAG;MAChC,CAAE;MAAAwE,QAAA,gBACA9G,OAAA,CAACK,KAAK;QAAC0L,KAAK,EAAEzJ,QAAQ,GAAG,CAAC,GAAG,CAAE;QAAC2E,KAAK,EAAE;UAAEI,YAAY,EAAE;QAAM,CAAE;QAAAP,QAAA,EAAC;MAEhE;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACR7G,OAAA,CAACM,IAAI;QAAC+I,IAAI,EAAC,WAAW;QAACpC,KAAK,EAAE;UAAEG,QAAQ,EAAE9E,QAAQ,GAAG,MAAM,GAAG;QAAO,CAAE;QAAAwE,QAAA,EAAC;MAExE;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,EAELgE,aAAa,CAAC,CAAC;EAAA;IAAAnE,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACb,CAAC;AAEV,CAAC;AAAC5F,EAAA,CA51BIF,sBAAsB;EAAA,QACThD,WAAW,EACXE,WAAW,EACLD,eAAe;AAAA;AAAAgO,EAAA,GAHlCjL,sBAAsB;AA81B5B,eAAeA,sBAAsB;AAAC,IAAAiL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}