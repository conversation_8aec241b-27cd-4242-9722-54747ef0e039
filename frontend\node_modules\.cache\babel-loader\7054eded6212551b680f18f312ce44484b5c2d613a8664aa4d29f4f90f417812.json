{"ast": null, "code": "var _jsxFileName = \"D:\\\\pythonproject\\\\checkingsys\\\\frontend\\\\src\\\\components\\\\FinishedHomeworks.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { Table, Tag, Button, Input, Select, DatePicker, Space, message, Alert, Empty, Spin, Typography, Popconfirm, Badge } from 'antd';\nimport { SearchOutlined, FileTextOutlined, CheckCircleOutlined, DeleteOutlined, SyncOutlined, ClockCircleOutlined, HistoryOutlined } from '@ant-design/icons';\nimport { getHomeworks, deleteHomework, getHomeworkAssignments } from '../utils/api';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Option\n} = Select;\nconst {\n  RangePicker\n} = DatePicker;\nconst {\n  Title,\n  Text\n} = Typography;\n\n// 简单的防抖函数\nconst debounce = (fn, delay) => {\n  let timer = null;\n  return function (...args) {\n    if (timer) clearTimeout(timer);\n    timer = setTimeout(() => {\n      fn.apply(this, args);\n    }, delay);\n  };\n};\nconst FinishedHomeworks = ({\n  user\n}) => {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [loading, setLoading] = useState(false);\n  const [homeworks, setHomeworks] = useState([]);\n  const [assignments, setAssignments] = useState([]);\n  const [pagination, setPagination] = useState({\n    current: 1,\n    pageSize: 10,\n    total: 0\n  });\n  const [error, setError] = useState(null);\n  const [assignmentsLoading, setAssignmentsLoading] = useState(false);\n  const [lastSelectedAssignment, setLastSelectedAssignment] = useState(null);\n\n  // 过滤条件\n  const [filters, setFilters] = useState({\n    status: '',\n    // 默认不过滤状态，显示所有已结束的作业\n    search: '',\n    dateRange: null,\n    assignmentId: null\n  });\n\n  // 获取作业任务列表\n  const fetchAssignments = async () => {\n    try {\n      setAssignmentsLoading(true);\n      console.log('开始获取已结束作业任务列表');\n      const response = await getHomeworkAssignments();\n      console.log('获取作业任务列表响应:', response);\n      let assignmentsList = [];\n      if (response && response.items) {\n        assignmentsList = response.items;\n      } else if (Array.isArray(response)) {\n        assignmentsList = response;\n      }\n      console.log('解析后的作业任务列表:', assignmentsList);\n\n      // 处理作业任务数据，确保每个作业任务都有正确的status字段\n      const processedAssignments = assignmentsList.map(assignment => {\n        let status = assignment.status;\n\n        // 如果没有status字段，从description中提取状态\n        if (!status && assignment.description) {\n          // 使用正则表达式提取状态\n          const statusMatch = assignment.description.match(/【状态】(.*?)】/);\n          if (statusMatch) {\n            status = statusMatch[1];\n          } else {\n            status = 'active'; // 默认状态为active\n          }\n        } else if (!status) {\n          status = 'active'; // 默认状态为active\n        }\n        return {\n          ...assignment,\n          status: status\n        };\n      });\n      console.log('处理后的作业任务列表:', processedAssignments);\n\n      // 过滤未结束的作业任务，只保留已结束的作业任务\n      const finishedAssignments = processedAssignments.filter(assignment => assignment.status === 'finished');\n      console.log('已结束的作业任务:', finishedAssignments);\n      setAssignments(finishedAssignments);\n\n      // 如果有作业任务，默认选择第一个\n      if (finishedAssignments.length > 0 && !filters.assignmentId) {\n        console.log('选择第一个作业任务:', finishedAssignments[0]);\n        const firstAssignmentId = finishedAssignments[0].id;\n\n        // 同时更新lastSelectedAssignment，避免重复刷新\n        setLastSelectedAssignment(firstAssignmentId);\n        setFilters(prev => ({\n          ...prev,\n          assignmentId: firstAssignmentId\n        }));\n\n        // 加载该作业任务的作业\n        fetchHomeworks({\n          assignment_id: firstAssignmentId\n        });\n      } else if (finishedAssignments.length === 0) {\n        // 如果没有作业任务，清空作业列表\n        console.log('没有已结束的作业任务，清空作业列表');\n        setHomeworks([]);\n        setLoading(false);\n      }\n    } catch (error) {\n      console.error('获取已结束作业任务列表失败:', error);\n      message.error('获取已结束作业任务列表失败');\n      setHomeworks([]);\n      setLoading(false);\n    } finally {\n      setAssignmentsLoading(false);\n    }\n  };\n\n  // 获取作业列表\n  const fetchHomeworks = async (params = {}) => {\n    // 如果没有选择作业任务，不进行请求\n    if (!params.assignment_id && !filters.assignmentId) {\n      console.log('没有选择作业任务，不请求作业列表');\n      setHomeworks([]);\n      setLoading(false);\n      return;\n    }\n    try {\n      setLoading(true);\n      setError(null);\n\n      // 构建API参数\n      const apiParams = {\n        page: params.page || pagination.current,\n        limit: params.pageSize || pagination.pageSize,\n        status: params.status || filters.status,\n        search: params.search || filters.search,\n        assignment_id: params.assignment_id || filters.assignmentId\n      };\n\n      // 添加日期范围\n      if (params.dateRange || filters.dateRange) {\n        const dateRange = params.dateRange || filters.dateRange;\n        if (dateRange && dateRange.length === 2) {\n          apiParams.start_date = dateRange[0].format('YYYY-MM-DD');\n          apiParams.end_date = dateRange[1].format('YYYY-MM-DD');\n        }\n      }\n      console.log('请求已结束作业列表，参数:', apiParams);\n\n      // 获取数据\n      const data = await getHomeworks(apiParams);\n      console.log('已结束作业列表响应:', data);\n\n      // 检查响应数据\n      if (data && Array.isArray(data.items)) {\n        setHomeworks(data.items);\n        setPagination({\n          ...pagination,\n          current: params.page || pagination.current,\n          total: data.total || data.items.length\n        });\n      } else if (Array.isArray(data)) {\n        setHomeworks(data);\n        setPagination({\n          ...pagination,\n          current: params.page || pagination.current,\n          total: data.length\n        });\n      } else if (data && typeof data === 'object' && data.id) {\n        setHomeworks([data]);\n        setPagination({\n          ...pagination,\n          current: 1,\n          total: 1\n        });\n      } else {\n        console.warn('已结束作业列表响应格式不符合预期:', data);\n        setHomeworks([]);\n        setPagination({\n          ...pagination,\n          current: 1,\n          total: 0\n        });\n        setError('获取已结束作业列表格式异常，暂无数据');\n        message.warning('暂无已结束作业数据');\n      }\n    } catch (error) {\n      console.error('获取已结束作业列表失败:', error);\n      setError(`获取已结束作业列表失败: ${error.message || '未知错误'}`);\n      setHomeworks([]);\n      setPagination({\n        ...pagination,\n        current: 1,\n        total: 0\n      });\n      message.error('获取已结束作业列表失败，请稍后重试');\n    } finally {\n      // 确保无论如何都设置loading为false\n      setLoading(false);\n      console.log('已结束作业列表加载完成，loading状态已重置');\n    }\n  };\n\n  // 首次加载时获取作业任务列表和作业列表\n  useEffect(() => {\n    fetchAssignments();\n  }, []);\n\n  // 处理location.state中的refresh参数\n  useEffect(() => {\n    if (location.state && location.state.refresh) {\n      console.log('检测到需要刷新已结束作业列表');\n\n      // 检查是否需要强制刷新\n      const forceRefresh = location.state.forceRefresh === true;\n      const timestamp = location.state.timestamp || new Date().getTime();\n      console.log(`强制刷新: ${forceRefresh}, 时间戳: ${timestamp}`);\n\n      // 先获取作业任务列表\n      fetchAssignments();\n\n      // 同时刷新当前选中的作业任务的作业列表\n      if (filters.assignmentId) {\n        console.log('同时刷新当前作业任务的作业列表:', filters.assignmentId);\n\n        // 更新lastSelectedAssignment，避免刷新后重复请求\n        setLastSelectedAssignment(filters.assignmentId);\n        fetchHomeworks({\n          assignment_id: filters.assignmentId,\n          page: 1,\n          // 重置到第一页\n          cache: false,\n          // 禁用缓存\n          _t: timestamp // 添加时间戳参数，确保不使用缓存\n        });\n      }\n\n      // 清除state，避免重复刷新\n      navigate(location.pathname, {\n        replace: true,\n        state: {}\n      });\n    }\n  }, [location.state, navigate, location.pathname, filters.assignmentId]);\n\n  // 处理表格变化\n  const handleTableChange = (pagination, filters) => {\n    fetchHomeworks({\n      page: pagination.current,\n      pageSize: pagination.pageSize,\n      ...filters\n    });\n  };\n\n  // 处理搜索\n  const handleSearch = () => {\n    fetchHomeworks({\n      page: 1,\n      ...filters\n    });\n  };\n\n  // 处理查看详情\n  const handleViewDetail = id => {\n    navigate(`/homework/${id}?from=finished`);\n  };\n\n  // 处理刷新\n  const handleRefresh = () => {\n    console.log('手动刷新已结束作业列表');\n    fetchAssignments();\n\n    // 同时刷新当前选中的作业任务的作业列表\n    if (filters.assignmentId) {\n      console.log('同时刷新当前作业任务的作业列表:', filters.assignmentId);\n\n      // 更新lastSelectedAssignment，避免刷新后重复请求\n      setLastSelectedAssignment(filters.assignmentId);\n      fetchHomeworks({\n        assignment_id: filters.assignmentId,\n        page: 1,\n        // 重置到第一页\n        cache: false // 禁用缓存\n      });\n    }\n    message.info('正在刷新已结束作业列表...');\n  };\n\n  // 处理作业任务选择变更\n  const handleAssignmentChange = value => {\n    // 如果选择的是同一个作业任务，不重复请求\n    if (value === lastSelectedAssignment) {\n      console.log('选择了相同的作业任务，跳过刷新');\n      return;\n    }\n\n    // 更新上次选择的作业任务\n    setLastSelectedAssignment(value);\n    setFilters(prev => ({\n      ...prev,\n      assignmentId: value\n    }));\n\n    // 获取作业列表，只刷新一次\n    fetchHomeworks({\n      assignment_id: value,\n      page: 1\n    });\n  };\n\n  // 处理删除作业\n  const handleDelete = async id => {\n    try {\n      await deleteHomework(id);\n      message.success('作业删除成功');\n      fetchHomeworks();\n    } catch (error) {\n      console.error('删除作业失败:', error);\n      message.error(`删除作业失败: ${error.message || '未知错误'}`);\n    }\n  };\n\n  // 获取状态标签\n  const getStatusTag = status => {\n    switch (status) {\n      case 'submitted':\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"blue\",\n          icon: /*#__PURE__*/_jsxDEV(ClockCircleOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 341,\n            columnNumber: 40\n          }, this),\n          children: \"\\u5DF2\\u63D0\\u4EA4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 341,\n          columnNumber: 16\n        }, this);\n      case 'grading':\n      case 'correcting':\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"orange\",\n          icon: /*#__PURE__*/_jsxDEV(SyncOutlined, {\n            spin: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 42\n          }, this),\n          children: \"\\u6279\\u6539\\u4E2D\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 16\n        }, this);\n      case 'graded':\n      case 'corrected':\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"green\",\n          icon: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 41\n          }, this),\n          children: \"\\u5DF2\\u6279\\u6539\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 347,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"default\",\n          children: \"\\u672A\\u77E5\\u72B6\\u6001\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 349,\n          columnNumber: 16\n        }, this);\n    }\n  };\n\n  // 表格列定义\n  const columns = [{\n    title: '标题',\n    dataIndex: 'title',\n    key: 'title',\n    render: (text, record) => /*#__PURE__*/_jsxDEV(\"a\", {\n      href: `/homework/${record.id}`,\n      onClick: e => {\n        e.preventDefault();\n        handleViewDetail(record.id);\n      },\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 360,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: user.is_teacher ? '学生' : '作业来源',\n    dataIndex: user.is_teacher ? 'student_name' : 'assignment_title',\n    key: user.is_teacher ? 'student_name' : 'assignment_title',\n    render: (text, record) => {\n      if (user.is_teacher) {\n        return text || `学生ID: ${record.student_id}`;\n      } else {\n        return text || record.title || '-';\n      }\n    }\n  }, {\n    title: '班级',\n    dataIndex: 'class_name',\n    key: 'class_name',\n    render: (text, record) => {\n      if (text) {\n        return text;\n      } else if (record.class_id) {\n        return `班级ID: ${record.class_id}`;\n      } else {\n        return '-';\n      }\n    }\n  }, {\n    title: '提交次数',\n    dataIndex: 'version_count',\n    key: 'version_count',\n    render: (count, record) => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(Badge, {\n        count: count || 1,\n        style: {\n          backgroundColor: count > 1 ? '#52c41a' : '#1890ff',\n          fontSize: '12px',\n          padding: '0 6px'\n        },\n        overflowCount: 99\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 398,\n        columnNumber: 11\n      }, this), count > 1 && /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        icon: /*#__PURE__*/_jsxDEV(ClockCircleOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 410,\n          columnNumber: 21\n        }, this),\n        size: \"small\",\n        onClick: e => {\n          e.stopPropagation();\n          navigate(`/homework/history?student_id=${record.student_id}&assignment_id=${record.assignment_id}`);\n        },\n        title: \"\\u67E5\\u770B\\u63D0\\u4EA4\\u5386\\u53F2\",\n        style: {\n          padding: '0 4px'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 408,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 397,\n      columnNumber: 9\n    }, this),\n    sorter: (a, b) => (a.version_count || 1) - (b.version_count || 1)\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    render: status => getStatusTag(status)\n  }, {\n    title: '提交时间',\n    dataIndex: 'created_at',\n    key: 'created_at',\n    render: date => new Date(date).toLocaleString()\n  }, {\n    title: '操作',\n    key: 'action',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        icon: /*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 443,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleViewDetail(record.id),\n        children: \"\\u67E5\\u770B\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 441,\n        columnNumber: 11\n      }, this), user.is_teacher && record.status === 'submitted' && /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        icon: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 451,\n          columnNumber: 21\n        }, this),\n        onClick: () => navigate(`/homework/${record.id}?from=finished`, {\n          state: {\n            startCorrection: true\n          }\n        }),\n        children: \"\\u6279\\u6539\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 449,\n        columnNumber: 13\n      }, this), user.is_teacher && /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: \"\\u786E\\u5B9A\\u5220\\u9664\\u8BE5\\u4F5C\\u4E1A\\u5417\\uFF1F\",\n        onConfirm: () => handleDelete(record.id),\n        okText: \"\\u786E\\u5B9A\",\n        cancelText: \"\\u53D6\\u6D88\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          danger: true,\n          icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 467,\n            columnNumber: 23\n          }, this),\n          children: \"\\u5220\\u9664\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 464,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 458,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 440,\n      columnNumber: 9\n    }, this)\n  }];\n\n  // 渲染内容\n  const renderContent = () => {\n    // 如果正在加载作业任务列表\n    if (assignmentsLoading) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '50px 0'\n        },\n        children: /*#__PURE__*/_jsxDEV(Spin, {\n          tip: \"\\u52A0\\u8F7D\\u4F5C\\u4E1A\\u4EFB\\u52A1\\u5217\\u8868...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 484,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 483,\n        columnNumber: 9\n      }, this);\n    }\n\n    // 如果没有作业任务\n    if (assignments.length === 0) {\n      return /*#__PURE__*/_jsxDEV(Empty, {\n        description: \"\\u6CA1\\u6709\\u5DF2\\u7ED3\\u675F\\u7684\\u4F5C\\u4E1A\\u4EFB\\u52A1\",\n        image: Empty.PRESENTED_IMAGE_SIMPLE\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 492,\n        columnNumber: 9\n      }, this);\n    }\n\n    // 如果有作业任务但没有作业\n    return /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"filter-toolbar\",\n        style: {\n          marginBottom: 16,\n          marginTop: 16\n        },\n        children: /*#__PURE__*/_jsxDEV(Space, {\n          style: {\n            marginBottom: 16\n          },\n          children: [/*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u9009\\u62E9\\u4F5C\\u4E1A\\u4EFB\\u52A1\",\n            style: {\n              width: 200\n            },\n            value: filters.assignmentId,\n            onChange: handleAssignmentChange,\n            loading: loading,\n            children: assignments.map(assignment => /*#__PURE__*/_jsxDEV(Option, {\n              value: assignment.id,\n              children: assignment.title\n            }, assignment.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 513,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 505,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u641C\\u7D22\\u4F5C\\u4E1A\\u6807\\u9898\\u6216\\u5B66\\u751F\",\n            value: filters.search,\n            onChange: e => setFilters({\n              ...filters,\n              search: e.target.value\n            }),\n            style: {\n              width: 200\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 519,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u9009\\u62E9\\u72B6\\u6001\",\n            style: {\n              width: 120\n            },\n            value: filters.status,\n            onChange: value => setFilters({\n              ...filters,\n              status: value\n            }),\n            children: [/*#__PURE__*/_jsxDEV(Option, {\n              value: \"\",\n              children: \"\\u5168\\u90E8\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 532,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"submitted\",\n              children: \"\\u5F85\\u6279\\u6539\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 533,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"grading\",\n              children: \"\\u6279\\u6539\\u4E2D\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 534,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"graded\",\n              children: \"\\u5DF2\\u6279\\u6539\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 535,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 526,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            icon: /*#__PURE__*/_jsxDEV(SearchOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 540,\n              columnNumber: 21\n            }, this),\n            onClick: handleSearch,\n            children: \"\\u641C\\u7D22\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 538,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleRefresh,\n            children: \"\\u5237\\u65B0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 546,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 504,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 503,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n        message: \"\\u83B7\\u53D6\\u6570\\u636E\\u65F6\\u51FA\\u73B0\\u9519\\u8BEF\",\n        description: error,\n        type: \"error\",\n        showIcon: true,\n        style: {\n          marginBottom: 16\n        },\n        action: /*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          onClick: handleRefresh,\n          children: \"\\u91CD\\u8BD5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 561,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 554,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Spin, {\n        spinning: loading,\n        children: homeworks.length > 0 ? /*#__PURE__*/_jsxDEV(Table, {\n          columns: columns,\n          dataSource: homeworks,\n          rowKey: \"id\",\n          pagination: pagination,\n          onChange: handleTableChange,\n          loading: loading\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 571,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Empty, {\n          description: loading ? \"加载中...\" : \"暂无已结束作业\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 580,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 569,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"finished-homeworks-container\",\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 3,\n      children: \"\\u5F80\\u65E5\\u4F5C\\u4E1A\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 591,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Text, {\n      type: \"secondary\",\n      children: \"\\u663E\\u793A\\u6240\\u6709\\u5DF2\\u7ED3\\u675F\\u4F5C\\u4E1A\\u4EFB\\u52A1\\u7684\\u4F5C\\u4E1A\\u63D0\\u4EA4\\u60C5\\u51B5\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 592,\n      columnNumber: 7\n    }, this), renderContent()]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 590,\n    columnNumber: 5\n  }, this);\n};\n_s(FinishedHomeworks, \"RCnWqPM0JiyJH8r8BwDJtlfBYoc=\", false, function () {\n  return [useNavigate, useLocation];\n});\n_c = FinishedHomeworks;\nexport default FinishedHomeworks;\nvar _c;\n$RefreshReg$(_c, \"FinishedHomeworks\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useNavigate", "useLocation", "Table", "Tag", "<PERSON><PERSON>", "Input", "Select", "DatePicker", "Space", "message", "<PERSON><PERSON>", "Empty", "Spin", "Typography", "Popconfirm", "Badge", "SearchOutlined", "FileTextOutlined", "CheckCircleOutlined", "DeleteOutlined", "SyncOutlined", "ClockCircleOutlined", "HistoryOutlined", "getHomeworks", "deleteHomework", "getHomeworkAssignments", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Option", "RangePicker", "Title", "Text", "debounce", "fn", "delay", "timer", "args", "clearTimeout", "setTimeout", "apply", "FinishedHomeworks", "user", "_s", "navigate", "location", "loading", "setLoading", "homeworks", "setHomeworks", "assignments", "setAssignments", "pagination", "setPagination", "current", "pageSize", "total", "error", "setError", "assignmentsLoading", "setAssignmentsLoading", "lastSelectedAssignment", "setLastSelectedAssignment", "filters", "setFilters", "status", "search", "date<PERSON><PERSON><PERSON>", "assignmentId", "fetchAssignments", "console", "log", "response", "assignmentsList", "items", "Array", "isArray", "processedAssignments", "map", "assignment", "description", "statusMatch", "match", "finishedAssignments", "filter", "length", "firstAssignmentId", "id", "prev", "fetchHomeworks", "assignment_id", "params", "apiParams", "page", "limit", "start_date", "format", "end_date", "data", "warn", "warning", "state", "refresh", "forceRefresh", "timestamp", "Date", "getTime", "cache", "_t", "pathname", "replace", "handleTableChange", "handleSearch", "handleViewDetail", "handleRefresh", "info", "handleAssignmentChange", "value", "handleDelete", "success", "getStatusTag", "color", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "children", "spin", "columns", "title", "dataIndex", "key", "render", "text", "record", "href", "onClick", "e", "preventDefault", "is_teacher", "student_id", "class_id", "count", "style", "backgroundColor", "fontSize", "padding", "overflowCount", "type", "size", "stopPropagation", "sorter", "a", "b", "version_count", "date", "toLocaleString", "_", "startCorrection", "onConfirm", "okText", "cancelText", "danger", "renderContent", "textAlign", "tip", "image", "PRESENTED_IMAGE_SIMPLE", "className", "marginBottom", "marginTop", "placeholder", "width", "onChange", "target", "showIcon", "action", "spinning", "dataSource", "<PERSON><PERSON><PERSON>", "level", "_c", "$RefreshReg$"], "sources": ["D:/pythonproject/checkingsys/frontend/src/components/FinishedHomeworks.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\r\nimport { useNavigate, useLocation } from 'react-router-dom';\r\nimport {\r\n  Table, Tag, Button, Input, Select, DatePicker, Space, message,\r\n  Alert, Empty, Spin, Typography, Popconfirm, Badge\r\n} from 'antd';\r\nimport { \r\n  SearchOutlined, FileTextOutlined, CheckCircleOutlined, DeleteOutlined,\r\n  SyncOutlined, ClockCircleOutlined, HistoryOutlined\r\n} from '@ant-design/icons';\r\nimport { getHomeworks, deleteHomework, getHomeworkAssignments } from '../utils/api';\r\n\r\nconst { Option } = Select;\r\nconst { RangePicker } = DatePicker;\r\nconst { Title, Text } = Typography;\r\n\r\n// 简单的防抖函数\r\nconst debounce = (fn, delay) => {\r\n  let timer = null;\r\n  return function(...args) {\r\n    if (timer) clearTimeout(timer);\r\n    timer = setTimeout(() => {\r\n      fn.apply(this, args);\r\n    }, delay);\r\n  };\r\n};\r\n\r\nconst FinishedHomeworks = ({ user }) => {\r\n  const navigate = useNavigate();\r\n  const location = useLocation();\r\n  const [loading, setLoading] = useState(false);\r\n  const [homeworks, setHomeworks] = useState([]);\r\n  const [assignments, setAssignments] = useState([]);\r\n  const [pagination, setPagination] = useState({\r\n    current: 1,\r\n    pageSize: 10,\r\n    total: 0\r\n  });\r\n  const [error, setError] = useState(null);\r\n  const [assignmentsLoading, setAssignmentsLoading] = useState(false);\r\n  const [lastSelectedAssignment, setLastSelectedAssignment] = useState(null);\r\n  \r\n  // 过滤条件\r\n  const [filters, setFilters] = useState({\r\n    status: '',  // 默认不过滤状态，显示所有已结束的作业\r\n    search: '',\r\n    dateRange: null,\r\n    assignmentId: null\r\n  });\r\n  \r\n  // 获取作业任务列表\r\n  const fetchAssignments = async () => {\r\n    try {\r\n      setAssignmentsLoading(true);\r\n      console.log('开始获取已结束作业任务列表');\r\n      const response = await getHomeworkAssignments();\r\n      console.log('获取作业任务列表响应:', response);\r\n      \r\n      let assignmentsList = [];\r\n      \r\n      if (response && response.items) {\r\n        assignmentsList = response.items;\r\n      } else if (Array.isArray(response)) {\r\n        assignmentsList = response;\r\n      }\r\n      \r\n      console.log('解析后的作业任务列表:', assignmentsList);\r\n\r\n      // 处理作业任务数据，确保每个作业任务都有正确的status字段\r\n      const processedAssignments = assignmentsList.map(assignment => {\r\n        let status = assignment.status;\r\n\r\n        // 如果没有status字段，从description中提取状态\r\n        if (!status && assignment.description) {\r\n          // 使用正则表达式提取状态\r\n          const statusMatch = assignment.description.match(/【状态】(.*?)】/);\r\n          if (statusMatch) {\r\n            status = statusMatch[1];\r\n          } else {\r\n            status = 'active'; // 默认状态为active\r\n          }\r\n        } else if (!status) {\r\n          status = 'active'; // 默认状态为active\r\n        }\r\n\r\n        return {\r\n          ...assignment,\r\n          status: status\r\n        };\r\n      });\r\n\r\n      console.log('处理后的作业任务列表:', processedAssignments);\r\n\r\n      // 过滤未结束的作业任务，只保留已结束的作业任务\r\n      const finishedAssignments = processedAssignments.filter(assignment =>\r\n        assignment.status === 'finished'\r\n      );\r\n      \r\n      console.log('已结束的作业任务:', finishedAssignments);\r\n      setAssignments(finishedAssignments);\r\n      \r\n      // 如果有作业任务，默认选择第一个\r\n      if (finishedAssignments.length > 0 && !filters.assignmentId) {\r\n        console.log('选择第一个作业任务:', finishedAssignments[0]);\r\n        const firstAssignmentId = finishedAssignments[0].id;\r\n        \r\n        // 同时更新lastSelectedAssignment，避免重复刷新\r\n        setLastSelectedAssignment(firstAssignmentId);\r\n        \r\n        setFilters(prev => ({\r\n          ...prev,\r\n          assignmentId: firstAssignmentId\r\n        }));\r\n        \r\n        // 加载该作业任务的作业\r\n        fetchHomeworks({\r\n          assignment_id: firstAssignmentId\r\n        });\r\n      } else if (finishedAssignments.length === 0) {\r\n        // 如果没有作业任务，清空作业列表\r\n        console.log('没有已结束的作业任务，清空作业列表');\r\n        setHomeworks([]);\r\n        setLoading(false);\r\n      }\r\n    } catch (error) {\r\n      console.error('获取已结束作业任务列表失败:', error);\r\n      message.error('获取已结束作业任务列表失败');\r\n      setHomeworks([]);\r\n      setLoading(false);\r\n    } finally {\r\n      setAssignmentsLoading(false);\r\n    }\r\n  };\r\n  \r\n  // 获取作业列表\r\n  const fetchHomeworks = async (params = {}) => {\r\n    // 如果没有选择作业任务，不进行请求\r\n    if (!params.assignment_id && !filters.assignmentId) {\r\n      console.log('没有选择作业任务，不请求作业列表');\r\n      setHomeworks([]);\r\n      setLoading(false);\r\n      return;\r\n    }\r\n    \r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n      \r\n      // 构建API参数\r\n      const apiParams = {\r\n        page: params.page || pagination.current,\r\n        limit: params.pageSize || pagination.pageSize,\r\n        status: params.status || filters.status,\r\n        search: params.search || filters.search,\r\n        assignment_id: params.assignment_id || filters.assignmentId\r\n      };\r\n      \r\n      // 添加日期范围\r\n      if (params.dateRange || filters.dateRange) {\r\n        const dateRange = params.dateRange || filters.dateRange;\r\n        if (dateRange && dateRange.length === 2) {\r\n          apiParams.start_date = dateRange[0].format('YYYY-MM-DD');\r\n          apiParams.end_date = dateRange[1].format('YYYY-MM-DD');\r\n        }\r\n      }\r\n      \r\n      console.log('请求已结束作业列表，参数:', apiParams);\r\n      \r\n      // 获取数据\r\n      const data = await getHomeworks(apiParams);\r\n      console.log('已结束作业列表响应:', data);\r\n      \r\n      // 检查响应数据\r\n      if (data && Array.isArray(data.items)) {\r\n        setHomeworks(data.items);\r\n        setPagination({\r\n          ...pagination,\r\n          current: params.page || pagination.current,\r\n          total: data.total || data.items.length\r\n        });\r\n      } else if (Array.isArray(data)) {\r\n        setHomeworks(data);\r\n        setPagination({\r\n          ...pagination,\r\n          current: params.page || pagination.current,\r\n          total: data.length\r\n        });\r\n      } else if (data && typeof data === 'object' && data.id) {\r\n        setHomeworks([data]);\r\n        setPagination({\r\n          ...pagination,\r\n          current: 1,\r\n          total: 1\r\n        });\r\n      } else {\r\n        console.warn('已结束作业列表响应格式不符合预期:', data);\r\n        setHomeworks([]);\r\n        setPagination({\r\n          ...pagination,\r\n          current: 1,\r\n          total: 0\r\n        });\r\n        setError('获取已结束作业列表格式异常，暂无数据');\r\n        message.warning('暂无已结束作业数据');\r\n      }\r\n    } catch (error) {\r\n      console.error('获取已结束作业列表失败:', error);\r\n      setError(`获取已结束作业列表失败: ${error.message || '未知错误'}`);\r\n      setHomeworks([]);\r\n      setPagination({\r\n        ...pagination,\r\n        current: 1,\r\n        total: 0\r\n      });\r\n      message.error('获取已结束作业列表失败，请稍后重试');\r\n    } finally {\r\n      // 确保无论如何都设置loading为false\r\n      setLoading(false);\r\n      console.log('已结束作业列表加载完成，loading状态已重置');\r\n    }\r\n  };\r\n  \r\n  // 首次加载时获取作业任务列表和作业列表\r\n  useEffect(() => {\r\n    fetchAssignments();\r\n  }, []);\r\n  \r\n  // 处理location.state中的refresh参数\r\n  useEffect(() => {\r\n    if (location.state && location.state.refresh) {\r\n      console.log('检测到需要刷新已结束作业列表');\r\n      \r\n      // 检查是否需要强制刷新\r\n      const forceRefresh = location.state.forceRefresh === true;\r\n      const timestamp = location.state.timestamp || new Date().getTime();\r\n      console.log(`强制刷新: ${forceRefresh}, 时间戳: ${timestamp}`);\r\n      \r\n      // 先获取作业任务列表\r\n      fetchAssignments();\r\n      \r\n      // 同时刷新当前选中的作业任务的作业列表\r\n      if (filters.assignmentId) {\r\n        console.log('同时刷新当前作业任务的作业列表:', filters.assignmentId);\r\n        \r\n        // 更新lastSelectedAssignment，避免刷新后重复请求\r\n        setLastSelectedAssignment(filters.assignmentId);\r\n        \r\n        fetchHomeworks({\r\n          assignment_id: filters.assignmentId,\r\n          page: 1,  // 重置到第一页\r\n          cache: false,  // 禁用缓存\r\n          _t: timestamp  // 添加时间戳参数，确保不使用缓存\r\n        });\r\n      }\r\n      \r\n      // 清除state，避免重复刷新\r\n      navigate(location.pathname, { replace: true, state: {} });\r\n    }\r\n  }, [location.state, navigate, location.pathname, filters.assignmentId]);\r\n  \r\n  // 处理表格变化\r\n  const handleTableChange = (pagination, filters) => {\r\n    fetchHomeworks({\r\n      page: pagination.current,\r\n      pageSize: pagination.pageSize,\r\n      ...filters\r\n    });\r\n  };\r\n  \r\n  // 处理搜索\r\n  const handleSearch = () => {\r\n    fetchHomeworks({ page: 1, ...filters });\r\n  };\r\n  \r\n  // 处理查看详情\r\n  const handleViewDetail = (id) => {\r\n    navigate(`/homework/${id}?from=finished`);\r\n  };\r\n  \r\n  // 处理刷新\r\n  const handleRefresh = () => {\r\n    console.log('手动刷新已结束作业列表');\r\n    fetchAssignments();\r\n    \r\n    // 同时刷新当前选中的作业任务的作业列表\r\n    if (filters.assignmentId) {\r\n      console.log('同时刷新当前作业任务的作业列表:', filters.assignmentId);\r\n      \r\n      // 更新lastSelectedAssignment，避免刷新后重复请求\r\n      setLastSelectedAssignment(filters.assignmentId);\r\n      \r\n      fetchHomeworks({\r\n        assignment_id: filters.assignmentId,\r\n        page: 1,  // 重置到第一页\r\n        cache: false  // 禁用缓存\r\n      });\r\n    }\r\n    \r\n    message.info('正在刷新已结束作业列表...');\r\n  };\r\n  \r\n  // 处理作业任务选择变更\r\n  const handleAssignmentChange = (value) => {\r\n    // 如果选择的是同一个作业任务，不重复请求\r\n    if (value === lastSelectedAssignment) {\r\n      console.log('选择了相同的作业任务，跳过刷新');\r\n      return;\r\n    }\r\n    \r\n    // 更新上次选择的作业任务\r\n    setLastSelectedAssignment(value);\r\n    \r\n    setFilters(prev => ({\r\n      ...prev,\r\n      assignmentId: value\r\n    }));\r\n    \r\n    // 获取作业列表，只刷新一次\r\n    fetchHomeworks({\r\n      assignment_id: value,\r\n      page: 1\r\n    });\r\n  };\r\n  \r\n  // 处理删除作业\r\n  const handleDelete = async (id) => {\r\n    try {\r\n      await deleteHomework(id);\r\n      message.success('作业删除成功');\r\n      fetchHomeworks();\r\n    } catch (error) {\r\n      console.error('删除作业失败:', error);\r\n      message.error(`删除作业失败: ${error.message || '未知错误'}`);\r\n    }\r\n  };\r\n  \r\n  // 获取状态标签\r\n  const getStatusTag = (status) => {\r\n    switch (status) {\r\n      case 'submitted':\r\n        return <Tag color=\"blue\" icon={<ClockCircleOutlined />}>已提交</Tag>;\r\n      case 'grading':\r\n      case 'correcting':\r\n        return <Tag color=\"orange\" icon={<SyncOutlined spin />}>批改中</Tag>;\r\n      case 'graded':\r\n      case 'corrected':\r\n        return <Tag color=\"green\" icon={<CheckCircleOutlined />}>已批改</Tag>;\r\n      default:\r\n        return <Tag color=\"default\">未知状态</Tag>;\r\n    }\r\n  };\r\n  \r\n  // 表格列定义\r\n  const columns = [\r\n    {\r\n      title: '标题',\r\n      dataIndex: 'title',\r\n      key: 'title',\r\n      render: (text, record) => (\r\n        <a href={`/homework/${record.id}`} onClick={(e) => {\r\n          e.preventDefault();\r\n          handleViewDetail(record.id);\r\n        }}>{text}</a>\r\n      )\r\n    },\r\n    {\r\n      title: user.is_teacher ? '学生' : '作业来源',\r\n      dataIndex: user.is_teacher ? 'student_name' : 'assignment_title',\r\n      key: user.is_teacher ? 'student_name' : 'assignment_title',\r\n      render: (text, record) => {\r\n        if (user.is_teacher) {\r\n          return text || `学生ID: ${record.student_id}`;\r\n        } else {\r\n          return text || record.title || '-';\r\n        }\r\n      }\r\n    },\r\n    {\r\n      title: '班级',\r\n      dataIndex: 'class_name',\r\n      key: 'class_name',\r\n      render: (text, record) => {\r\n        if (text) {\r\n          return text;\r\n        } else if (record.class_id) {\r\n          return `班级ID: ${record.class_id}`;\r\n        } else {\r\n          return '-';\r\n        }\r\n      }\r\n    },\r\n    {\r\n      title: '提交次数',\r\n      dataIndex: 'version_count',\r\n      key: 'version_count',\r\n      render: (count, record) => (\r\n        <Space>\r\n          <Badge \r\n            count={count || 1} \r\n            style={{ \r\n              backgroundColor: count > 1 ? '#52c41a' : '#1890ff',\r\n              fontSize: '12px',\r\n              padding: '0 6px'\r\n            }} \r\n            overflowCount={99}\r\n          />\r\n          {count > 1 && (\r\n            <Button \r\n              type=\"link\" \r\n              icon={<ClockCircleOutlined />} \r\n              size=\"small\"\r\n              onClick={(e) => {\r\n                e.stopPropagation();\r\n                navigate(`/homework/history?student_id=${record.student_id}&assignment_id=${record.assignment_id}`);\r\n              }}\r\n              title=\"查看提交历史\"\r\n              style={{ padding: '0 4px' }}\r\n            />\r\n          )}\r\n        </Space>\r\n      ),\r\n      sorter: (a, b) => (a.version_count || 1) - (b.version_count || 1),\r\n    },\r\n    {\r\n      title: '状态',\r\n      dataIndex: 'status',\r\n      key: 'status',\r\n      render: (status) => getStatusTag(status)\r\n    },\r\n    {\r\n      title: '提交时间',\r\n      dataIndex: 'created_at',\r\n      key: 'created_at',\r\n      render: (date) => new Date(date).toLocaleString()\r\n    },\r\n    {\r\n      title: '操作',\r\n      key: 'action',\r\n      render: (_, record) => (\r\n        <Space>\r\n          <Button\r\n            type=\"primary\"\r\n            icon={<FileTextOutlined />}\r\n            onClick={() => handleViewDetail(record.id)}\r\n          >\r\n            查看\r\n          </Button>\r\n          {user.is_teacher && record.status === 'submitted' && (\r\n            <Button\r\n              type=\"primary\"\r\n              icon={<CheckCircleOutlined />}\r\n              onClick={() => navigate(`/homework/${record.id}?from=finished`, { state: { startCorrection: true } })}\r\n            >\r\n              批改\r\n            </Button>\r\n          )}\r\n          {user.is_teacher && (\r\n            <Popconfirm\r\n              title=\"确定删除该作业吗？\"\r\n              onConfirm={() => handleDelete(record.id)}\r\n              okText=\"确定\"\r\n              cancelText=\"取消\"\r\n            >\r\n              <Button\r\n                type=\"primary\"\r\n                danger\r\n                icon={<DeleteOutlined />}\r\n              >\r\n                删除\r\n              </Button>\r\n            </Popconfirm>\r\n          )}\r\n        </Space>\r\n      )\r\n    }\r\n  ];\r\n\r\n  // 渲染内容\r\n  const renderContent = () => {\r\n    // 如果正在加载作业任务列表\r\n    if (assignmentsLoading) {\r\n      return (\r\n        <div style={{ textAlign: 'center', padding: '50px 0' }}>\r\n          <Spin tip=\"加载作业任务列表...\" />\r\n        </div>\r\n      );\r\n    }\r\n    \r\n    // 如果没有作业任务\r\n    if (assignments.length === 0) {\r\n      return (\r\n        <Empty \r\n          description=\"没有已结束的作业任务\" \r\n          image={Empty.PRESENTED_IMAGE_SIMPLE}\r\n        />\r\n      );\r\n    }\r\n    \r\n    // 如果有作业任务但没有作业\r\n    return (\r\n      <>\r\n        {/* 筛选工具栏 */}\r\n        <div className=\"filter-toolbar\" style={{ marginBottom: 16, marginTop: 16 }}>\r\n          <Space style={{ marginBottom: 16 }}>\r\n            <Select\r\n              placeholder=\"选择作业任务\"\r\n              style={{ width: 200 }}\r\n              value={filters.assignmentId}\r\n              onChange={handleAssignmentChange}\r\n              loading={loading}\r\n            >\r\n              {assignments.map(assignment => (\r\n                <Option key={assignment.id} value={assignment.id}>\r\n                  {assignment.title}\r\n                </Option>\r\n              ))}\r\n            </Select>\r\n            \r\n            <Input\r\n              placeholder=\"搜索作业标题或学生\"\r\n              value={filters.search}\r\n              onChange={(e) => setFilters({ ...filters, search: e.target.value })}\r\n              style={{ width: 200 }}\r\n            />\r\n            \r\n            <Select\r\n              placeholder=\"选择状态\"\r\n              style={{ width: 120 }}\r\n              value={filters.status}\r\n              onChange={(value) => setFilters({ ...filters, status: value })}\r\n            >\r\n              <Option value=\"\">全部</Option>\r\n              <Option value=\"submitted\">待批改</Option>\r\n              <Option value=\"grading\">批改中</Option>\r\n              <Option value=\"graded\">已批改</Option>\r\n            </Select>\r\n            \r\n            <Button\r\n              type=\"primary\"\r\n              icon={<SearchOutlined />}\r\n              onClick={handleSearch}\r\n            >\r\n              搜索\r\n            </Button>\r\n            \r\n            <Button onClick={handleRefresh}>\r\n              刷新\r\n            </Button>\r\n          </Space>\r\n        </div>\r\n        \r\n        {/* 错误提示 */}\r\n        {error && (\r\n          <Alert\r\n            message=\"获取数据时出现错误\"\r\n            description={error}\r\n            type=\"error\"\r\n            showIcon\r\n            style={{ marginBottom: 16 }}\r\n            action={\r\n              <Button size=\"small\" onClick={handleRefresh}>\r\n                重试\r\n              </Button>\r\n            }\r\n          />\r\n        )}\r\n        \r\n        {/* 数据表格 */}\r\n        <Spin spinning={loading}>\r\n          {homeworks.length > 0 ? (\r\n            <Table\r\n              columns={columns}\r\n              dataSource={homeworks}\r\n              rowKey=\"id\"\r\n              pagination={pagination}\r\n              onChange={handleTableChange}\r\n              loading={loading}\r\n            />\r\n          ) : (\r\n            <Empty description={\r\n              loading ? \"加载中...\" : \"暂无已结束作业\"\r\n            } />\r\n          )}\r\n        </Spin>\r\n      </>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <div className=\"finished-homeworks-container\">\r\n      <Title level={3}>往日作业</Title>\r\n      <Text type=\"secondary\">显示所有已结束作业任务的作业提交情况</Text>\r\n      \r\n      {renderContent()}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FinishedHomeworks; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SACEC,KAAK,EAAEC,GAAG,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,UAAU,EAAEC,KAAK,EAAEC,OAAO,EAC7DC,KAAK,EAAEC,KAAK,EAAEC,IAAI,EAAEC,UAAU,EAAEC,UAAU,EAAEC,KAAK,QAC5C,MAAM;AACb,SACEC,cAAc,EAAEC,gBAAgB,EAAEC,mBAAmB,EAAEC,cAAc,EACrEC,YAAY,EAAEC,mBAAmB,EAAEC,eAAe,QAC7C,mBAAmB;AAC1B,SAASC,YAAY,EAAEC,cAAc,EAAEC,sBAAsB,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEpF,MAAM;EAAEC;AAAO,CAAC,GAAGxB,MAAM;AACzB,MAAM;EAAEyB;AAAY,CAAC,GAAGxB,UAAU;AAClC,MAAM;EAAEyB,KAAK;EAAEC;AAAK,CAAC,GAAGpB,UAAU;;AAElC;AACA,MAAMqB,QAAQ,GAAGA,CAACC,EAAE,EAAEC,KAAK,KAAK;EAC9B,IAAIC,KAAK,GAAG,IAAI;EAChB,OAAO,UAAS,GAAGC,IAAI,EAAE;IACvB,IAAID,KAAK,EAAEE,YAAY,CAACF,KAAK,CAAC;IAC9BA,KAAK,GAAGG,UAAU,CAAC,MAAM;MACvBL,EAAE,CAACM,KAAK,CAAC,IAAI,EAAEH,IAAI,CAAC;IACtB,CAAC,EAAEF,KAAK,CAAC;EACX,CAAC;AACH,CAAC;AAED,MAAMM,iBAAiB,GAAGA,CAAC;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EACtC,MAAMC,QAAQ,GAAG7C,WAAW,CAAC,CAAC;EAC9B,MAAM8C,QAAQ,GAAG7C,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC8C,OAAO,EAAEC,UAAU,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACoD,SAAS,EAAEC,YAAY,CAAC,GAAGrD,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACsD,WAAW,EAAEC,cAAc,CAAC,GAAGvD,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACwD,UAAU,EAAEC,aAAa,CAAC,GAAGzD,QAAQ,CAAC;IAC3C0D,OAAO,EAAE,CAAC;IACVC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAG9D,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC+D,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGhE,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACiE,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGlE,QAAQ,CAAC,IAAI,CAAC;;EAE1E;EACA,MAAM,CAACmE,OAAO,EAAEC,UAAU,CAAC,GAAGpE,QAAQ,CAAC;IACrCqE,MAAM,EAAE,EAAE;IAAG;IACbC,MAAM,EAAE,EAAE;IACVC,SAAS,EAAE,IAAI;IACfC,YAAY,EAAE;EAChB,CAAC,CAAC;;EAEF;EACA,MAAMC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFT,qBAAqB,CAAC,IAAI,CAAC;MAC3BU,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;MAC5B,MAAMC,QAAQ,GAAG,MAAMhD,sBAAsB,CAAC,CAAC;MAC/C8C,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEC,QAAQ,CAAC;MAEpC,IAAIC,eAAe,GAAG,EAAE;MAExB,IAAID,QAAQ,IAAIA,QAAQ,CAACE,KAAK,EAAE;QAC9BD,eAAe,GAAGD,QAAQ,CAACE,KAAK;MAClC,CAAC,MAAM,IAAIC,KAAK,CAACC,OAAO,CAACJ,QAAQ,CAAC,EAAE;QAClCC,eAAe,GAAGD,QAAQ;MAC5B;MAEAF,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEE,eAAe,CAAC;;MAE3C;MACA,MAAMI,oBAAoB,GAAGJ,eAAe,CAACK,GAAG,CAACC,UAAU,IAAI;QAC7D,IAAId,MAAM,GAAGc,UAAU,CAACd,MAAM;;QAE9B;QACA,IAAI,CAACA,MAAM,IAAIc,UAAU,CAACC,WAAW,EAAE;UACrC;UACA,MAAMC,WAAW,GAAGF,UAAU,CAACC,WAAW,CAACE,KAAK,CAAC,YAAY,CAAC;UAC9D,IAAID,WAAW,EAAE;YACfhB,MAAM,GAAGgB,WAAW,CAAC,CAAC,CAAC;UACzB,CAAC,MAAM;YACLhB,MAAM,GAAG,QAAQ,CAAC,CAAC;UACrB;QACF,CAAC,MAAM,IAAI,CAACA,MAAM,EAAE;UAClBA,MAAM,GAAG,QAAQ,CAAC,CAAC;QACrB;QAEA,OAAO;UACL,GAAGc,UAAU;UACbd,MAAM,EAAEA;QACV,CAAC;MACH,CAAC,CAAC;MAEFK,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEM,oBAAoB,CAAC;;MAEhD;MACA,MAAMM,mBAAmB,GAAGN,oBAAoB,CAACO,MAAM,CAACL,UAAU,IAChEA,UAAU,CAACd,MAAM,KAAK,UACxB,CAAC;MAEDK,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEY,mBAAmB,CAAC;MAC7ChC,cAAc,CAACgC,mBAAmB,CAAC;;MAEnC;MACA,IAAIA,mBAAmB,CAACE,MAAM,GAAG,CAAC,IAAI,CAACtB,OAAO,CAACK,YAAY,EAAE;QAC3DE,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEY,mBAAmB,CAAC,CAAC,CAAC,CAAC;QACjD,MAAMG,iBAAiB,GAAGH,mBAAmB,CAAC,CAAC,CAAC,CAACI,EAAE;;QAEnD;QACAzB,yBAAyB,CAACwB,iBAAiB,CAAC;QAE5CtB,UAAU,CAACwB,IAAI,KAAK;UAClB,GAAGA,IAAI;UACPpB,YAAY,EAAEkB;QAChB,CAAC,CAAC,CAAC;;QAEH;QACAG,cAAc,CAAC;UACbC,aAAa,EAAEJ;QACjB,CAAC,CAAC;MACJ,CAAC,MAAM,IAAIH,mBAAmB,CAACE,MAAM,KAAK,CAAC,EAAE;QAC3C;QACAf,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;QAChCtB,YAAY,CAAC,EAAE,CAAC;QAChBF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC,CAAC,OAAOU,KAAK,EAAE;MACda,OAAO,CAACb,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;MACtCjD,OAAO,CAACiD,KAAK,CAAC,eAAe,CAAC;MAC9BR,YAAY,CAAC,EAAE,CAAC;MAChBF,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,SAAS;MACRa,qBAAqB,CAAC,KAAK,CAAC;IAC9B;EACF,CAAC;;EAED;EACA,MAAM6B,cAAc,GAAG,MAAAA,CAAOE,MAAM,GAAG,CAAC,CAAC,KAAK;IAC5C;IACA,IAAI,CAACA,MAAM,CAACD,aAAa,IAAI,CAAC3B,OAAO,CAACK,YAAY,EAAE;MAClDE,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;MAC/BtB,YAAY,CAAC,EAAE,CAAC;MAChBF,UAAU,CAAC,KAAK,CAAC;MACjB;IACF;IAEA,IAAI;MACFA,UAAU,CAAC,IAAI,CAAC;MAChBW,QAAQ,CAAC,IAAI,CAAC;;MAEd;MACA,MAAMkC,SAAS,GAAG;QAChBC,IAAI,EAAEF,MAAM,CAACE,IAAI,IAAIzC,UAAU,CAACE,OAAO;QACvCwC,KAAK,EAAEH,MAAM,CAACpC,QAAQ,IAAIH,UAAU,CAACG,QAAQ;QAC7CU,MAAM,EAAE0B,MAAM,CAAC1B,MAAM,IAAIF,OAAO,CAACE,MAAM;QACvCC,MAAM,EAAEyB,MAAM,CAACzB,MAAM,IAAIH,OAAO,CAACG,MAAM;QACvCwB,aAAa,EAAEC,MAAM,CAACD,aAAa,IAAI3B,OAAO,CAACK;MACjD,CAAC;;MAED;MACA,IAAIuB,MAAM,CAACxB,SAAS,IAAIJ,OAAO,CAACI,SAAS,EAAE;QACzC,MAAMA,SAAS,GAAGwB,MAAM,CAACxB,SAAS,IAAIJ,OAAO,CAACI,SAAS;QACvD,IAAIA,SAAS,IAAIA,SAAS,CAACkB,MAAM,KAAK,CAAC,EAAE;UACvCO,SAAS,CAACG,UAAU,GAAG5B,SAAS,CAAC,CAAC,CAAC,CAAC6B,MAAM,CAAC,YAAY,CAAC;UACxDJ,SAAS,CAACK,QAAQ,GAAG9B,SAAS,CAAC,CAAC,CAAC,CAAC6B,MAAM,CAAC,YAAY,CAAC;QACxD;MACF;MAEA1B,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEqB,SAAS,CAAC;;MAEvC;MACA,MAAMM,IAAI,GAAG,MAAM5E,YAAY,CAACsE,SAAS,CAAC;MAC1CtB,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE2B,IAAI,CAAC;;MAE/B;MACA,IAAIA,IAAI,IAAIvB,KAAK,CAACC,OAAO,CAACsB,IAAI,CAACxB,KAAK,CAAC,EAAE;QACrCzB,YAAY,CAACiD,IAAI,CAACxB,KAAK,CAAC;QACxBrB,aAAa,CAAC;UACZ,GAAGD,UAAU;UACbE,OAAO,EAAEqC,MAAM,CAACE,IAAI,IAAIzC,UAAU,CAACE,OAAO;UAC1CE,KAAK,EAAE0C,IAAI,CAAC1C,KAAK,IAAI0C,IAAI,CAACxB,KAAK,CAACW;QAClC,CAAC,CAAC;MACJ,CAAC,MAAM,IAAIV,KAAK,CAACC,OAAO,CAACsB,IAAI,CAAC,EAAE;QAC9BjD,YAAY,CAACiD,IAAI,CAAC;QAClB7C,aAAa,CAAC;UACZ,GAAGD,UAAU;UACbE,OAAO,EAAEqC,MAAM,CAACE,IAAI,IAAIzC,UAAU,CAACE,OAAO;UAC1CE,KAAK,EAAE0C,IAAI,CAACb;QACd,CAAC,CAAC;MACJ,CAAC,MAAM,IAAIa,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAIA,IAAI,CAACX,EAAE,EAAE;QACtDtC,YAAY,CAAC,CAACiD,IAAI,CAAC,CAAC;QACpB7C,aAAa,CAAC;UACZ,GAAGD,UAAU;UACbE,OAAO,EAAE,CAAC;UACVE,KAAK,EAAE;QACT,CAAC,CAAC;MACJ,CAAC,MAAM;QACLc,OAAO,CAAC6B,IAAI,CAAC,mBAAmB,EAAED,IAAI,CAAC;QACvCjD,YAAY,CAAC,EAAE,CAAC;QAChBI,aAAa,CAAC;UACZ,GAAGD,UAAU;UACbE,OAAO,EAAE,CAAC;UACVE,KAAK,EAAE;QACT,CAAC,CAAC;QACFE,QAAQ,CAAC,oBAAoB,CAAC;QAC9BlD,OAAO,CAAC4F,OAAO,CAAC,WAAW,CAAC;MAC9B;IACF,CAAC,CAAC,OAAO3C,KAAK,EAAE;MACda,OAAO,CAACb,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpCC,QAAQ,CAAC,gBAAgBD,KAAK,CAACjD,OAAO,IAAI,MAAM,EAAE,CAAC;MACnDyC,YAAY,CAAC,EAAE,CAAC;MAChBI,aAAa,CAAC;QACZ,GAAGD,UAAU;QACbE,OAAO,EAAE,CAAC;QACVE,KAAK,EAAE;MACT,CAAC,CAAC;MACFhD,OAAO,CAACiD,KAAK,CAAC,mBAAmB,CAAC;IACpC,CAAC,SAAS;MACR;MACAV,UAAU,CAAC,KAAK,CAAC;MACjBuB,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;IACzC;EACF,CAAC;;EAED;EACA1E,SAAS,CAAC,MAAM;IACdwE,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAxE,SAAS,CAAC,MAAM;IACd,IAAIgD,QAAQ,CAACwD,KAAK,IAAIxD,QAAQ,CAACwD,KAAK,CAACC,OAAO,EAAE;MAC5ChC,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;;MAE7B;MACA,MAAMgC,YAAY,GAAG1D,QAAQ,CAACwD,KAAK,CAACE,YAAY,KAAK,IAAI;MACzD,MAAMC,SAAS,GAAG3D,QAAQ,CAACwD,KAAK,CAACG,SAAS,IAAI,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;MAClEpC,OAAO,CAACC,GAAG,CAAC,SAASgC,YAAY,UAAUC,SAAS,EAAE,CAAC;;MAEvD;MACAnC,gBAAgB,CAAC,CAAC;;MAElB;MACA,IAAIN,OAAO,CAACK,YAAY,EAAE;QACxBE,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAER,OAAO,CAACK,YAAY,CAAC;;QAErD;QACAN,yBAAyB,CAACC,OAAO,CAACK,YAAY,CAAC;QAE/CqB,cAAc,CAAC;UACbC,aAAa,EAAE3B,OAAO,CAACK,YAAY;UACnCyB,IAAI,EAAE,CAAC;UAAG;UACVc,KAAK,EAAE,KAAK;UAAG;UACfC,EAAE,EAAEJ,SAAS,CAAE;QACjB,CAAC,CAAC;MACJ;;MAEA;MACA5D,QAAQ,CAACC,QAAQ,CAACgE,QAAQ,EAAE;QAAEC,OAAO,EAAE,IAAI;QAAET,KAAK,EAAE,CAAC;MAAE,CAAC,CAAC;IAC3D;EACF,CAAC,EAAE,CAACxD,QAAQ,CAACwD,KAAK,EAAEzD,QAAQ,EAAEC,QAAQ,CAACgE,QAAQ,EAAE9C,OAAO,CAACK,YAAY,CAAC,CAAC;;EAEvE;EACA,MAAM2C,iBAAiB,GAAGA,CAAC3D,UAAU,EAAEW,OAAO,KAAK;IACjD0B,cAAc,CAAC;MACbI,IAAI,EAAEzC,UAAU,CAACE,OAAO;MACxBC,QAAQ,EAAEH,UAAU,CAACG,QAAQ;MAC7B,GAAGQ;IACL,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMiD,YAAY,GAAGA,CAAA,KAAM;IACzBvB,cAAc,CAAC;MAAEI,IAAI,EAAE,CAAC;MAAE,GAAG9B;IAAQ,CAAC,CAAC;EACzC,CAAC;;EAED;EACA,MAAMkD,gBAAgB,GAAI1B,EAAE,IAAK;IAC/B3C,QAAQ,CAAC,aAAa2C,EAAE,gBAAgB,CAAC;EAC3C,CAAC;;EAED;EACA,MAAM2B,aAAa,GAAGA,CAAA,KAAM;IAC1B5C,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;IAC1BF,gBAAgB,CAAC,CAAC;;IAElB;IACA,IAAIN,OAAO,CAACK,YAAY,EAAE;MACxBE,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAER,OAAO,CAACK,YAAY,CAAC;;MAErD;MACAN,yBAAyB,CAACC,OAAO,CAACK,YAAY,CAAC;MAE/CqB,cAAc,CAAC;QACbC,aAAa,EAAE3B,OAAO,CAACK,YAAY;QACnCyB,IAAI,EAAE,CAAC;QAAG;QACVc,KAAK,EAAE,KAAK,CAAE;MAChB,CAAC,CAAC;IACJ;IAEAnG,OAAO,CAAC2G,IAAI,CAAC,gBAAgB,CAAC;EAChC,CAAC;;EAED;EACA,MAAMC,sBAAsB,GAAIC,KAAK,IAAK;IACxC;IACA,IAAIA,KAAK,KAAKxD,sBAAsB,EAAE;MACpCS,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;MAC9B;IACF;;IAEA;IACAT,yBAAyB,CAACuD,KAAK,CAAC;IAEhCrD,UAAU,CAACwB,IAAI,KAAK;MAClB,GAAGA,IAAI;MACPpB,YAAY,EAAEiD;IAChB,CAAC,CAAC,CAAC;;IAEH;IACA5B,cAAc,CAAC;MACbC,aAAa,EAAE2B,KAAK;MACpBxB,IAAI,EAAE;IACR,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMyB,YAAY,GAAG,MAAO/B,EAAE,IAAK;IACjC,IAAI;MACF,MAAMhE,cAAc,CAACgE,EAAE,CAAC;MACxB/E,OAAO,CAAC+G,OAAO,CAAC,QAAQ,CAAC;MACzB9B,cAAc,CAAC,CAAC;IAClB,CAAC,CAAC,OAAOhC,KAAK,EAAE;MACda,OAAO,CAACb,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BjD,OAAO,CAACiD,KAAK,CAAC,WAAWA,KAAK,CAACjD,OAAO,IAAI,MAAM,EAAE,CAAC;IACrD;EACF,CAAC;;EAED;EACA,MAAMgH,YAAY,GAAIvD,MAAM,IAAK;IAC/B,QAAQA,MAAM;MACZ,KAAK,WAAW;QACd,oBAAOvC,OAAA,CAACxB,GAAG;UAACuH,KAAK,EAAC,MAAM;UAACC,IAAI,eAAEhG,OAAA,CAACN,mBAAmB;YAAAuG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAC,QAAA,EAAC;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MACnE,KAAK,SAAS;MACd,KAAK,YAAY;QACf,oBAAOpG,OAAA,CAACxB,GAAG;UAACuH,KAAK,EAAC,QAAQ;UAACC,IAAI,eAAEhG,OAAA,CAACP,YAAY;YAAC6G,IAAI;UAAA;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAC,QAAA,EAAC;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MACnE,KAAK,QAAQ;MACb,KAAK,WAAW;QACd,oBAAOpG,OAAA,CAACxB,GAAG;UAACuH,KAAK,EAAC,OAAO;UAACC,IAAI,eAAEhG,OAAA,CAACT,mBAAmB;YAAA0G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAC,QAAA,EAAC;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MACpE;QACE,oBAAOpG,OAAA,CAACxB,GAAG;UAACuH,KAAK,EAAC,SAAS;UAAAM,QAAA,EAAC;QAAI;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;IAC1C;EACF,CAAC;;EAED;EACA,MAAMG,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,OAAO;IAClBC,GAAG,EAAE,OAAO;IACZC,MAAM,EAAEA,CAACC,IAAI,EAAEC,MAAM,kBACnB7G,OAAA;MAAG8G,IAAI,EAAE,aAAaD,MAAM,CAAChD,EAAE,EAAG;MAACkD,OAAO,EAAGC,CAAC,IAAK;QACjDA,CAAC,CAACC,cAAc,CAAC,CAAC;QAClB1B,gBAAgB,CAACsB,MAAM,CAAChD,EAAE,CAAC;MAC7B,CAAE;MAAAwC,QAAA,EAAEO;IAAI;MAAAX,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI;EAEhB,CAAC,EACD;IACEI,KAAK,EAAExF,IAAI,CAACkG,UAAU,GAAG,IAAI,GAAG,MAAM;IACtCT,SAAS,EAAEzF,IAAI,CAACkG,UAAU,GAAG,cAAc,GAAG,kBAAkB;IAChER,GAAG,EAAE1F,IAAI,CAACkG,UAAU,GAAG,cAAc,GAAG,kBAAkB;IAC1DP,MAAM,EAAEA,CAACC,IAAI,EAAEC,MAAM,KAAK;MACxB,IAAI7F,IAAI,CAACkG,UAAU,EAAE;QACnB,OAAON,IAAI,IAAI,SAASC,MAAM,CAACM,UAAU,EAAE;MAC7C,CAAC,MAAM;QACL,OAAOP,IAAI,IAAIC,MAAM,CAACL,KAAK,IAAI,GAAG;MACpC;IACF;EACF,CAAC,EACD;IACEA,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,YAAY;IACvBC,GAAG,EAAE,YAAY;IACjBC,MAAM,EAAEA,CAACC,IAAI,EAAEC,MAAM,KAAK;MACxB,IAAID,IAAI,EAAE;QACR,OAAOA,IAAI;MACb,CAAC,MAAM,IAAIC,MAAM,CAACO,QAAQ,EAAE;QAC1B,OAAO,SAASP,MAAM,CAACO,QAAQ,EAAE;MACnC,CAAC,MAAM;QACL,OAAO,GAAG;MACZ;IACF;EACF,CAAC,EACD;IACEZ,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,eAAe;IAC1BC,GAAG,EAAE,eAAe;IACpBC,MAAM,EAAEA,CAACU,KAAK,EAAER,MAAM,kBACpB7G,OAAA,CAACnB,KAAK;MAAAwH,QAAA,gBACJrG,OAAA,CAACZ,KAAK;QACJiI,KAAK,EAAEA,KAAK,IAAI,CAAE;QAClBC,KAAK,EAAE;UACLC,eAAe,EAAEF,KAAK,GAAG,CAAC,GAAG,SAAS,GAAG,SAAS;UAClDG,QAAQ,EAAE,MAAM;UAChBC,OAAO,EAAE;QACX,CAAE;QACFC,aAAa,EAAE;MAAG;QAAAzB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC,EACDiB,KAAK,GAAG,CAAC,iBACRrH,OAAA,CAACvB,MAAM;QACLkJ,IAAI,EAAC,MAAM;QACX3B,IAAI,eAAEhG,OAAA,CAACN,mBAAmB;UAAAuG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC9BwB,IAAI,EAAC,OAAO;QACZb,OAAO,EAAGC,CAAC,IAAK;UACdA,CAAC,CAACa,eAAe,CAAC,CAAC;UACnB3G,QAAQ,CAAC,gCAAgC2F,MAAM,CAACM,UAAU,kBAAkBN,MAAM,CAAC7C,aAAa,EAAE,CAAC;QACrG,CAAE;QACFwC,KAAK,EAAC,sCAAQ;QACdc,KAAK,EAAE;UAAEG,OAAO,EAAE;QAAQ;MAAE;QAAAxB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CACR;IACD0B,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAK,CAACD,CAAC,CAACE,aAAa,IAAI,CAAC,KAAKD,CAAC,CAACC,aAAa,IAAI,CAAC;EAClE,CAAC,EACD;IACEzB,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAGpE,MAAM,IAAKuD,YAAY,CAACvD,MAAM;EACzC,CAAC,EACD;IACEiE,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,YAAY;IACvBC,GAAG,EAAE,YAAY;IACjBC,MAAM,EAAGuB,IAAI,IAAK,IAAInD,IAAI,CAACmD,IAAI,CAAC,CAACC,cAAc,CAAC;EAClD,CAAC,EACD;IACE3B,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAEA,CAACyB,CAAC,EAAEvB,MAAM,kBAChB7G,OAAA,CAACnB,KAAK;MAAAwH,QAAA,gBACJrG,OAAA,CAACvB,MAAM;QACLkJ,IAAI,EAAC,SAAS;QACd3B,IAAI,eAAEhG,OAAA,CAACV,gBAAgB;UAAA2G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC3BW,OAAO,EAAEA,CAAA,KAAMxB,gBAAgB,CAACsB,MAAM,CAAChD,EAAE,CAAE;QAAAwC,QAAA,EAC5C;MAED;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EACRpF,IAAI,CAACkG,UAAU,IAAIL,MAAM,CAACtE,MAAM,KAAK,WAAW,iBAC/CvC,OAAA,CAACvB,MAAM;QACLkJ,IAAI,EAAC,SAAS;QACd3B,IAAI,eAAEhG,OAAA,CAACT,mBAAmB;UAAA0G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC9BW,OAAO,EAAEA,CAAA,KAAM7F,QAAQ,CAAC,aAAa2F,MAAM,CAAChD,EAAE,gBAAgB,EAAE;UAAEc,KAAK,EAAE;YAAE0D,eAAe,EAAE;UAAK;QAAE,CAAC,CAAE;QAAAhC,QAAA,EACvG;MAED;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT,EACApF,IAAI,CAACkG,UAAU,iBACdlH,OAAA,CAACb,UAAU;QACTqH,KAAK,EAAC,wDAAW;QACjB8B,SAAS,EAAEA,CAAA,KAAM1C,YAAY,CAACiB,MAAM,CAAChD,EAAE,CAAE;QACzC0E,MAAM,EAAC,cAAI;QACXC,UAAU,EAAC,cAAI;QAAAnC,QAAA,eAEfrG,OAAA,CAACvB,MAAM;UACLkJ,IAAI,EAAC,SAAS;UACdc,MAAM;UACNzC,IAAI,eAAEhG,OAAA,CAACR,cAAc;YAAAyG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAC,QAAA,EAC1B;QAED;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACb;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EAEX,CAAC,CACF;;EAED;EACA,MAAMsC,aAAa,GAAGA,CAAA,KAAM;IAC1B;IACA,IAAIzG,kBAAkB,EAAE;MACtB,oBACEjC,OAAA;QAAKsH,KAAK,EAAE;UAAEqB,SAAS,EAAE,QAAQ;UAAElB,OAAO,EAAE;QAAS,CAAE;QAAApB,QAAA,eACrDrG,OAAA,CAACf,IAAI;UAAC2J,GAAG,EAAC;QAAa;UAAA3C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvB,CAAC;IAEV;;IAEA;IACA,IAAI5E,WAAW,CAACmC,MAAM,KAAK,CAAC,EAAE;MAC5B,oBACE3D,OAAA,CAAChB,KAAK;QACJsE,WAAW,EAAC,8DAAY;QACxBuF,KAAK,EAAE7J,KAAK,CAAC8J;MAAuB;QAAA7C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC,CAAC;IAEN;;IAEA;IACA,oBACEpG,OAAA,CAAAE,SAAA;MAAAmG,QAAA,gBAEErG,OAAA;QAAK+I,SAAS,EAAC,gBAAgB;QAACzB,KAAK,EAAE;UAAE0B,YAAY,EAAE,EAAE;UAAEC,SAAS,EAAE;QAAG,CAAE;QAAA5C,QAAA,eACzErG,OAAA,CAACnB,KAAK;UAACyI,KAAK,EAAE;YAAE0B,YAAY,EAAE;UAAG,CAAE;UAAA3C,QAAA,gBACjCrG,OAAA,CAACrB,MAAM;YACLuK,WAAW,EAAC,sCAAQ;YACpB5B,KAAK,EAAE;cAAE6B,KAAK,EAAE;YAAI,CAAE;YACtBxD,KAAK,EAAEtD,OAAO,CAACK,YAAa;YAC5B0G,QAAQ,EAAE1D,sBAAuB;YACjCtE,OAAO,EAAEA,OAAQ;YAAAiF,QAAA,EAEhB7E,WAAW,CAAC4B,GAAG,CAACC,UAAU,iBACzBrD,OAAA,CAACG,MAAM;cAAqBwF,KAAK,EAAEtC,UAAU,CAACQ,EAAG;cAAAwC,QAAA,EAC9ChD,UAAU,CAACmD;YAAK,GADNnD,UAAU,CAACQ,EAAE;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAElB,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAETpG,OAAA,CAACtB,KAAK;YACJwK,WAAW,EAAC,wDAAW;YACvBvD,KAAK,EAAEtD,OAAO,CAACG,MAAO;YACtB4G,QAAQ,EAAGpC,CAAC,IAAK1E,UAAU,CAAC;cAAE,GAAGD,OAAO;cAAEG,MAAM,EAAEwE,CAAC,CAACqC,MAAM,CAAC1D;YAAM,CAAC,CAAE;YACpE2B,KAAK,EAAE;cAAE6B,KAAK,EAAE;YAAI;UAAE;YAAAlD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC,eAEFpG,OAAA,CAACrB,MAAM;YACLuK,WAAW,EAAC,0BAAM;YAClB5B,KAAK,EAAE;cAAE6B,KAAK,EAAE;YAAI,CAAE;YACtBxD,KAAK,EAAEtD,OAAO,CAACE,MAAO;YACtB6G,QAAQ,EAAGzD,KAAK,IAAKrD,UAAU,CAAC;cAAE,GAAGD,OAAO;cAAEE,MAAM,EAAEoD;YAAM,CAAC,CAAE;YAAAU,QAAA,gBAE/DrG,OAAA,CAACG,MAAM;cAACwF,KAAK,EAAC,EAAE;cAAAU,QAAA,EAAC;YAAE;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC5BpG,OAAA,CAACG,MAAM;cAACwF,KAAK,EAAC,WAAW;cAAAU,QAAA,EAAC;YAAG;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtCpG,OAAA,CAACG,MAAM;cAACwF,KAAK,EAAC,SAAS;cAAAU,QAAA,EAAC;YAAG;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACpCpG,OAAA,CAACG,MAAM;cAACwF,KAAK,EAAC,QAAQ;cAAAU,QAAA,EAAC;YAAG;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eAETpG,OAAA,CAACvB,MAAM;YACLkJ,IAAI,EAAC,SAAS;YACd3B,IAAI,eAAEhG,OAAA,CAACX,cAAc;cAAA4G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzBW,OAAO,EAAEzB,YAAa;YAAAe,QAAA,EACvB;UAED;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAETpG,OAAA,CAACvB,MAAM;YAACsI,OAAO,EAAEvB,aAAc;YAAAa,QAAA,EAAC;UAEhC;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,EAGLrE,KAAK,iBACJ/B,OAAA,CAACjB,KAAK;QACJD,OAAO,EAAC,wDAAW;QACnBwE,WAAW,EAAEvB,KAAM;QACnB4F,IAAI,EAAC,OAAO;QACZ2B,QAAQ;QACRhC,KAAK,EAAE;UAAE0B,YAAY,EAAE;QAAG,CAAE;QAC5BO,MAAM,eACJvJ,OAAA,CAACvB,MAAM;UAACmJ,IAAI,EAAC,OAAO;UAACb,OAAO,EAAEvB,aAAc;UAAAa,QAAA,EAAC;QAE7C;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MACT;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CACF,eAGDpG,OAAA,CAACf,IAAI;QAACuK,QAAQ,EAAEpI,OAAQ;QAAAiF,QAAA,EACrB/E,SAAS,CAACqC,MAAM,GAAG,CAAC,gBACnB3D,OAAA,CAACzB,KAAK;UACJgI,OAAO,EAAEA,OAAQ;UACjBkD,UAAU,EAAEnI,SAAU;UACtBoI,MAAM,EAAC,IAAI;UACXhI,UAAU,EAAEA,UAAW;UACvB0H,QAAQ,EAAE/D,iBAAkB;UAC5BjE,OAAO,EAAEA;QAAQ;UAAA6E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC,gBAEFpG,OAAA,CAAChB,KAAK;UAACsE,WAAW,EAChBlC,OAAO,GAAG,QAAQ,GAAG;QACtB;UAAA6E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MACJ;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA,eACP,CAAC;EAEP,CAAC;EAED,oBACEpG,OAAA;IAAK+I,SAAS,EAAC,8BAA8B;IAAA1C,QAAA,gBAC3CrG,OAAA,CAACK,KAAK;MAACsJ,KAAK,EAAE,CAAE;MAAAtD,QAAA,EAAC;IAAI;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAC7BpG,OAAA,CAACM,IAAI;MAACqH,IAAI,EAAC,WAAW;MAAAtB,QAAA,EAAC;IAAkB;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,EAE/CsC,aAAa,CAAC,CAAC;EAAA;IAAAzC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACb,CAAC;AAEV,CAAC;AAACnF,EAAA,CAzjBIF,iBAAiB;EAAA,QACJ1C,WAAW,EACXC,WAAW;AAAA;AAAAsL,EAAA,GAFxB7I,iBAAiB;AA2jBvB,eAAeA,iBAAiB;AAAC,IAAA6I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}