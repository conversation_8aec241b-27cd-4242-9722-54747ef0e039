{"ast": null, "code": "var _jsxFileName = \"D:\\\\pythonproject\\\\checkingsys\\\\frontend\\\\src\\\\pages\\\\Home.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { Row, Col, Card, Typography, Button, Input, List, Avatar, message, Spin, Drawer } from 'antd';\nimport AIAssistant from '../components/AIAssistant';\nimport { UploadOutlined, FileTextOutlined, BookOutlined, CheckCircleOutlined, SyncOutlined, BarChartOutlined, RobotOutlined, UserOutlined, TeamOutlined, SettingOutlined, ClockCircleOutlined, ExclamationCircleOutlined, TrophyOutlined, CalendarOutlined } from '@ant-design/icons';\nimport { getHomeworks, getStudentHomeworkAssignments } from '../utils/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Paragraph\n} = Typography;\nconst {\n  Search\n} = Input;\n\n// 模拟数据，当API请求失败时使用\nconst mockHomeworks = [{\n  id: 1,\n  title: \"数学作业\",\n  status: \"graded\",\n  student_name: \"张三\",\n  created_at: new Date().toISOString()\n}, {\n  id: 2,\n  title: \"语文作业\",\n  status: \"submitted\",\n  student_name: \"李四\",\n  created_at: new Date().toISOString()\n}];\nconst Home = ({\n  user,\n  onLogout\n}) => {\n  _s();\n  const navigate = useNavigate();\n  const [loading, setLoading] = useState(true);\n  const [recentHomeworks, setRecentHomeworks] = useState([]);\n  const [studentStats, setStudentStats] = useState({});\n  const [error, setError] = useState(null);\n  const [aiDrawerVisible, setAiDrawerVisible] = useState(false);\n\n  // 判断作业是否已结束（基于description中的状态标记）\n  const isAssignmentEnded = assignment => {\n    if (!assignment.description) return false;\n    return assignment.description.includes('【状态】finished】');\n  };\n\n  // 计算学生作业统计数据\n  const calculateStudentStats = assignments => {\n    const unsubmittedAssignments = assignments.filter(item => item.submission_status === '未提交');\n    const pending = unsubmittedAssignments.filter(item => !isAssignmentEnded(item)).length;\n    const incomplete = unsubmittedAssignments.filter(item => isAssignmentEnded(item)).length;\n    const submittedAssignments = assignments.filter(item => item.submission_status === '已提交');\n    const submitted = submittedAssignments.filter(item => !isAssignmentEnded(item)).length;\n    const makeup = submittedAssignments.filter(item => isAssignmentEnded(item)).length;\n    const graded = assignments.filter(item => item.grading_status === '已批改').length;\n    return {\n      pending,\n      incomplete,\n      submitted,\n      makeup,\n      graded,\n      total: assignments.length\n    };\n  };\n\n  // 获取最近作业\n  useEffect(() => {\n    const fetchData = async () => {\n      if (!user) {\n        setLoading(false);\n        return;\n      }\n      try {\n        setLoading(true);\n        setError(null);\n\n        // 获取令牌，检查是否有效\n        const token = localStorage.getItem('token');\n        if (!token) {\n          throw new Error(\"找不到认证令牌，请重新登录\");\n        }\n\n        // 根据用户类型获取不同的数据\n        if (user.is_teacher || user.is_admin) {\n          // 教师和管理员获取作业数据\n          try {\n            const homeworksData = await getHomeworks({\n              limit: 5\n            });\n\n            // 确保homeworksData是数组\n            if (Array.isArray(homeworksData)) {\n              setRecentHomeworks(homeworksData);\n            } else if (homeworksData && Array.isArray(homeworksData.items)) {\n              setRecentHomeworks(homeworksData.items);\n            } else if (homeworksData && typeof homeworksData === 'object' && homeworksData.id) {\n              setRecentHomeworks([homeworksData]);\n            } else {\n              setRecentHomeworks(mockHomeworks);\n              message.warning(\"作业数据格式异常，显示模拟数据\");\n            }\n          } catch (homeworkError) {\n            console.error(\"获取作业数据失败:\", homeworkError);\n            setRecentHomeworks(mockHomeworks);\n            message.warning(\"无法加载作业数据，显示模拟数据\");\n          }\n        } else {\n          // 学生获取作业任务数据\n          try {\n            const assignments = await getStudentHomeworkAssignments();\n            console.log('获取到的学生作业任务:', assignments);\n\n            // 计算统计数据\n            const stats = calculateStudentStats(assignments);\n            setStudentStats(stats);\n\n            // 获取最近的作业任务作为展示\n            const recent = assignments.slice(0, 5).map(assignment => ({\n              id: assignment.homework_id || assignment.id,\n              // 优先使用homework_id，如果没有则使用assignment_id\n              assignment_id: assignment.id,\n              // 保留作业任务ID\n              homework_id: assignment.homework_id,\n              // 作业ID\n              title: assignment.title,\n              status: assignment.submission_status === '已提交' ? 'submitted' : 'pending',\n              student_name: user.username,\n              created_at: assignment.created_at\n            }));\n            setRecentHomeworks(recent);\n          } catch (studentError) {\n            console.error(\"获取学生作业数据失败:\", studentError);\n            setRecentHomeworks(mockHomeworks);\n            message.warning(\"无法加载作业数据，显示模拟数据\");\n          }\n        }\n      } catch (error) {\n        console.error('获取数据失败:', error);\n        setError(error.message || \"获取数据失败，请稍后重试\");\n\n        // 使用模拟数据\n        setRecentHomeworks(mockHomeworks);\n        message.error('获取数据失败，请稍后刷新页面');\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchData();\n  }, [user]);\n\n  // 搜索处理\n  const onSearch = value => {\n    if (!value) return;\n    navigate(`/homework?search=${encodeURIComponent(value)}`);\n  };\n\n  // 快捷操作处理\n  const handleQuickAction = action => {\n    console.log('Quick action:', action);\n    switch (action) {\n      case 'upload':\n        navigate('/homework/upload');\n        break;\n      case 'submit':\n        // 学生点击提交作业，跳转到作业管理页面\n        navigate('/homework');\n        break;\n      case 'pending':\n        // 跳转到作业管理页面并过滤待提交作业\n        navigate('/homework', {\n          state: {\n            filter: 'pending'\n          }\n        });\n        break;\n      case 'incomplete':\n        // 跳转到作业管理页面并过滤未完成作业\n        navigate('/homework', {\n          state: {\n            filter: 'incomplete'\n          }\n        });\n        break;\n      case 'review':\n        // 跳转到作业管理页面并过滤已批改作业\n        navigate('/homework', {\n          state: {\n            filter: 'graded'\n          }\n        });\n        break;\n      case 'training':\n        navigate('/training');\n        break;\n      case 'photo-solve':\n        navigate('/photo-solve');\n        break;\n      case 'statistics':\n        navigate('/statistics');\n        break;\n      case 'settings':\n        navigate('/profile');\n        break;\n      case 'class':\n        navigate('/class-management');\n        break;\n      case 'admin':\n        navigate('/admin');\n        break;\n      case 'user':\n        if (user.is_admin) {\n          navigate('/user-management');\n        } else if (user.is_teacher) {\n          navigate('/teacher-user-management');\n        }\n        break;\n      default:\n        break;\n    }\n  };\n\n  // 快捷操作项\n  const quickActions = !user ? [] : user.is_admin ? [{\n    key: 'admin',\n    title: '管理后台',\n    icon: /*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 216,\n      columnNumber: 17\n    }, this),\n    color: '#ff4d4f'\n  }, {\n    key: 'class',\n    title: '班级管理',\n    icon: /*#__PURE__*/_jsxDEV(TeamOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 222,\n      columnNumber: 17\n    }, this),\n    color: '#52c41a'\n  }, {\n    key: 'user',\n    title: '用户管理',\n    icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 228,\n      columnNumber: 17\n    }, this),\n    color: '#1890ff'\n  }, {\n    key: 'statistics',\n    title: '统计报表',\n    icon: /*#__PURE__*/_jsxDEV(BarChartOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 234,\n      columnNumber: 17\n    }, this),\n    color: '#faad14'\n  }] : user.is_teacher ? [{\n    key: 'upload',\n    title: '上传作业',\n    icon: /*#__PURE__*/_jsxDEV(UploadOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 243,\n      columnNumber: 17\n    }, this),\n    color: '#1890ff'\n  }, {\n    key: 'class',\n    title: '班级管理',\n    icon: /*#__PURE__*/_jsxDEV(TeamOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 249,\n      columnNumber: 17\n    }, this),\n    color: '#52c41a'\n  }, {\n    key: 'user',\n    title: '学生信息',\n    icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 255,\n      columnNumber: 17\n    }, this),\n    color: '#722ed1'\n  }, {\n    key: 'review',\n    title: '批改结果',\n    icon: /*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 261,\n      columnNumber: 17\n    }, this),\n    color: '#722ed1'\n  }, {\n    key: 'statistics',\n    title: '统计报表',\n    icon: /*#__PURE__*/_jsxDEV(BarChartOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 267,\n      columnNumber: 17\n    }, this),\n    color: '#faad14'\n  }] : [{\n    key: 'pending',\n    title: '待提交作业',\n    subtitle: `${studentStats.pending || 0}份`,\n    icon: '📝',\n    color: '#FF9500',\n    bgColor: 'rgba(255, 149, 0, 0.1)',\n    description: '需要及时完成的作业'\n  }, {\n    key: 'incomplete',\n    title: '未完成作业',\n    subtitle: `${studentStats.incomplete || 0}份`,\n    icon: '⚠️',\n    color: '#FF3B30',\n    bgColor: 'rgba(255, 59, 48, 0.1)',\n    description: '可以补交的作业'\n  }, {\n    key: 'review',\n    title: '查看批改',\n    subtitle: `${studentStats.graded || 0}份已批改`,\n    icon: '⭐',\n    color: '#30D158',\n    bgColor: 'rgba(48, 209, 88, 0.1)',\n    description: '查看批改结果和成绩'\n  }, {\n    key: 'training',\n    title: '错题训练',\n    subtitle: '强化练习',\n    icon: '💪',\n    color: '#AF52DE',\n    bgColor: 'rgba(175, 82, 222, 0.1)',\n    description: '针对性练习提升'\n  }, {\n    key: 'submit',\n    title: '作业管理',\n    subtitle: `共${studentStats.total || 0}份作业`,\n    icon: '📚',\n    color: '#4A90E2',\n    bgColor: 'rgba(74, 144, 226, 0.1)',\n    description: '查看所有作业任务'\n  }, {\n    key: 'photo-solve',\n    title: '拍照解题',\n    subtitle: 'AI智能解题',\n    icon: '📸',\n    color: '#FF6B6B',\n    bgColor: 'rgba(255, 107, 107, 0.1)',\n    description: '拍照上传题目，AI为您解答'\n  }, {\n    key: 'settings',\n    title: '个人中心',\n    subtitle: '设置与信息',\n    icon: '👤',\n    color: '#34C759',\n    bgColor: 'rgba(52, 199, 89, 0.1)',\n    description: '个人信息和设置'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '20px 0'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 2,\n      children: [\"\\u6B22\\u8FCE\\u56DE\\u6765\\uFF0C\", (user === null || user === void 0 ? void 0 : user.username) || '用户', \"\\uFF01\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 339,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n      children: \"\\u667A\\u6559\\u4E91\\u7AEF\\u667A\\u80FD\\u8F85\\u5BFC\\u5E73\\u53F0\\uFF0C\\u8BA9\\u5B66\\u4E60\\u66F4\\u9AD8\\u6548\\uFF01\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 340,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Spin, {\n      spinning: loading,\n      children: [/*#__PURE__*/_jsxDEV(Row, {\n        gutter: [16, 16],\n        children: /*#__PURE__*/_jsxDEV(Col, {\n          span: 24,\n          children: /*#__PURE__*/_jsxDEV(Search, {\n            placeholder: \"\\u641C\\u7D22\\u4F5C\\u4E1A\\u6216\\u6279\\u6539\\u8BB0\\u5F55...\",\n            allowClear: true,\n            enterButton: \"\\u641C\\u7D22\",\n            size: \"large\",\n            onSearch: onSearch\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 344,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(Row, {\n        gutter: [16, 16],\n        style: {\n          marginTop: 20\n        },\n        children: /*#__PURE__*/_jsxDEV(Col, {\n          span: 24,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            style: {\n              backgroundColor: '#fff2f0',\n              borderColor: '#ffccc7'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"\\u6570\\u636E\\u52A0\\u8F7D\\u51FA\\u9519: \", error]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 360,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              style: {\n                marginTop: 10\n              },\n              onClick: () => window.location.reload(),\n              children: \"\\u5237\\u65B0\\u9875\\u9762\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 361,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 358,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 357,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: 30\n        },\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          level: 4,\n          children: user && !user.is_teacher && !user.is_admin ? '📚 学习中心' : '快捷操作'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 375,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: [16, 16],\n          children: quickActions.map(action => /*#__PURE__*/_jsxDEV(Col, {\n            xs: 12,\n            sm: 8,\n            md: 6,\n            lg: 6,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              hoverable: true,\n              onClick: () => handleQuickAction(action.key),\n              style: {\n                textAlign: 'center',\n                background: action.bgColor || '#ffffff',\n                border: action.bgColor ? `1px solid ${action.color}20` : '1px solid #d9d9d9',\n                borderRadius: '12px',\n                transition: 'all 0.3s ease'\n              },\n              className: \"quick-action-card\",\n              bodyStyle: {\n                padding: '20px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: action.bgColor ? 28 : 32,\n                  color: action.color,\n                  marginBottom: action.bgColor ? 8 : 10\n                },\n                children: action.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 394,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '16px',\n                  fontWeight: 600,\n                  color: action.color,\n                  marginBottom: action.subtitle ? '4px' : '0'\n                },\n                children: action.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 401,\n                columnNumber: 19\n              }, this), action.subtitle && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '12px',\n                  color: action.color,\n                  opacity: 0.8,\n                  marginBottom: '8px'\n                },\n                children: action.subtitle\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 410,\n                columnNumber: 21\n              }, this), action.description && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '11px',\n                  color: '#666666',\n                  lineHeight: 1.3\n                },\n                children: action.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 420,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 381,\n              columnNumber: 17\n            }, this)\n          }, action.key, false, {\n            fileName: _jsxFileName,\n            lineNumber: 380,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 378,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 374,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: 30\n        },\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          level: 4,\n          children: \"\\u6700\\u8FD1\\u4F5C\\u4E1A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 436,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(List, {\n          itemLayout: \"horizontal\",\n          dataSource: Array.isArray(recentHomeworks) ? recentHomeworks : (recentHomeworks === null || recentHomeworks === void 0 ? void 0 : recentHomeworks.items) || [],\n          renderItem: item => /*#__PURE__*/_jsxDEV(List.Item, {\n            onClick: () => navigate(`/homework/${item.id}`),\n            style: {\n              cursor: 'pointer'\n            },\n            children: /*#__PURE__*/_jsxDEV(List.Item.Meta, {\n              avatar: /*#__PURE__*/_jsxDEV(Avatar, {\n                icon: /*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 447,\n                  columnNumber: 41\n                }, this),\n                style: {\n                  backgroundColor: item.status === 'graded' ? '#52c41a' : item.status === 'grading' ? '#faad14' : '#1890ff'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 447,\n                columnNumber: 27\n              }, this),\n              title: item.title,\n              description: `状态: ${item.status === 'graded' ? '已批改' : item.status === 'grading' ? '批改中' : '已提交'} | 学生: ${item.student_name || '未知'} | 提交时间: ${new Date(item.created_at).toLocaleString()}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 446,\n              columnNumber: 17\n            }, this)\n          }, item.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 441,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 437,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 435,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        style: {\n          marginTop: 30,\n          textAlign: 'center',\n          background: '#f0f7ff'\n        },\n        children: /*#__PURE__*/_jsxDEV(Row, {\n          align: \"middle\",\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(RobotOutlined, {\n              style: {\n                fontSize: 48,\n                color: '#1890ff'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 467,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 466,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 16,\n            style: {\n              textAlign: 'left'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Title, {\n              level: 4,\n              children: \"AI\\u667A\\u80FD\\u52A9\\u624B\\u968F\\u65F6\\u4E3A\\u60A8\\u670D\\u52A1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 470,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n              children: \"\\u6709\\u4EFB\\u4F55\\u95EE\\u9898\\uFF0C\\u53EF\\u4EE5\\u70B9\\u51FB\\u53F3\\u4E0A\\u89D2\\u7684\\\"AI\\u52A9\\u624B\\\"\\u6309\\u94AE\\uFF0C\\u83B7\\u53D6\\u667A\\u80FD\\u89E3\\u7B54\\u548C\\u8F85\\u5BFC\\u3002\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 471,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 469,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              size: \"large\",\n              onClick: () => {\n                console.log('点击立即咨询按钮');\n                setAiDrawerVisible(true);\n              },\n              children: \"\\u7ACB\\u5373\\u54A8\\u8BE2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 476,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 475,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 465,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 464,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 342,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Drawer, {\n      title: \"AI\\u667A\\u80FD\\u52A9\\u624B\",\n      placement: \"right\",\n      onClose: () => setAiDrawerVisible(false),\n      open: aiDrawerVisible,\n      width: window.innerWidth <= 768 ? '100%' : 400,\n      style: {\n        zIndex: 1000\n      },\n      children: /*#__PURE__*/_jsxDEV(AIAssistant, {\n        user: user\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 500,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 492,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 338,\n    columnNumber: 5\n  }, this);\n};\n_s(Home, \"6zShinAXCNcgEd5/r/l6cThbe/Y=\", false, function () {\n  return [useNavigate];\n});\n_c = Home;\nexport default Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "Row", "Col", "Card", "Typography", "<PERSON><PERSON>", "Input", "List", "Avatar", "message", "Spin", "Drawer", "AIAssistant", "UploadOutlined", "FileTextOutlined", "BookOutlined", "CheckCircleOutlined", "SyncOutlined", "BarChartOutlined", "RobotOutlined", "UserOutlined", "TeamOutlined", "SettingOutlined", "ClockCircleOutlined", "ExclamationCircleOutlined", "TrophyOutlined", "CalendarOutlined", "getHomeworks", "getStudentHomeworkAssignments", "jsxDEV", "_jsxDEV", "Title", "Paragraph", "Search", "mockHomeworks", "id", "title", "status", "student_name", "created_at", "Date", "toISOString", "Home", "user", "onLogout", "_s", "navigate", "loading", "setLoading", "recentHomeworks", "setRecentHomeworks", "studentStats", "setStudentStats", "error", "setError", "aiDrawerVisible", "setAiDrawerVisible", "isAssignmentEnded", "assignment", "description", "includes", "calculateStudentStats", "assignments", "unsubmittedAssignments", "filter", "item", "submission_status", "pending", "length", "incomplete", "submittedAssignments", "submitted", "makeup", "graded", "grading_status", "total", "fetchData", "token", "localStorage", "getItem", "Error", "is_teacher", "is_admin", "homeworksData", "limit", "Array", "isArray", "items", "warning", "homeworkError", "console", "log", "stats", "recent", "slice", "map", "homework_id", "assignment_id", "username", "studentError", "onSearch", "value", "encodeURIComponent", "handleQuickAction", "action", "state", "quickActions", "key", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "subtitle", "bgColor", "style", "padding", "children", "level", "spinning", "gutter", "span", "placeholder", "allowClear", "enterButton", "size", "marginTop", "backgroundColor", "borderColor", "type", "onClick", "window", "location", "reload", "xs", "sm", "md", "lg", "hoverable", "textAlign", "background", "border", "borderRadius", "transition", "className", "bodyStyle", "fontSize", "marginBottom", "fontWeight", "opacity", "lineHeight", "itemLayout", "dataSource", "renderItem", "<PERSON><PERSON>", "cursor", "Meta", "avatar", "toLocaleString", "align", "placement", "onClose", "open", "width", "innerWidth", "zIndex", "_c", "$RefreshReg$"], "sources": ["D:/pythonproject/checkingsys/frontend/src/pages/Home.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport {\r\n  Row, Col, Card, Typography, Button, Input,\r\n  List, Avatar, message, Spin, Drawer\r\n} from 'antd';\r\nimport AIAssistant from '../components/AIAssistant';\r\nimport {\r\n  UploadOutlined, FileTextOutlined, BookOutlined,\r\n  CheckCircleOutlined, SyncOutlined, BarChartOutlined,\r\n  RobotOutlined, UserOutlined, TeamOutlined, SettingOutlined,\r\n  ClockCircleOutlined, ExclamationCircleOutlined, TrophyOutlined,\r\n  CalendarOutlined\r\n} from '@ant-design/icons';\r\nimport { getHomeworks, getStudentHomeworkAssignments } from '../utils/api';\r\n\r\nconst { Title, Paragraph } = Typography;\r\nconst { Search } = Input;\r\n\r\n// 模拟数据，当API请求失败时使用\r\nconst mockHomeworks = [\r\n  {\r\n    id: 1,\r\n    title: \"数学作业\",\r\n    status: \"graded\",\r\n    student_name: \"张三\",\r\n    created_at: new Date().toISOString()\r\n  },\r\n  {\r\n    id: 2,\r\n    title: \"语文作业\",\r\n    status: \"submitted\",\r\n    student_name: \"李四\",\r\n    created_at: new Date().toISOString()\r\n  }\r\n];\r\n\r\nconst Home = ({ user, onLogout }) => {\r\n  const navigate = useNavigate();\r\n  const [loading, setLoading] = useState(true);\r\n  const [recentHomeworks, setRecentHomeworks] = useState([]);\r\n  const [studentStats, setStudentStats] = useState({});\r\n  const [error, setError] = useState(null);\r\n  const [aiDrawerVisible, setAiDrawerVisible] = useState(false);\r\n\r\n  // 判断作业是否已结束（基于description中的状态标记）\r\n  const isAssignmentEnded = (assignment) => {\r\n    if (!assignment.description) return false;\r\n    return assignment.description.includes('【状态】finished】');\r\n  };\r\n\r\n  // 计算学生作业统计数据\r\n  const calculateStudentStats = (assignments) => {\r\n    const unsubmittedAssignments = assignments.filter(item => item.submission_status === '未提交');\r\n    const pending = unsubmittedAssignments.filter(item => !isAssignmentEnded(item)).length;\r\n    const incomplete = unsubmittedAssignments.filter(item => isAssignmentEnded(item)).length;\r\n\r\n    const submittedAssignments = assignments.filter(item => item.submission_status === '已提交');\r\n    const submitted = submittedAssignments.filter(item => !isAssignmentEnded(item)).length;\r\n    const makeup = submittedAssignments.filter(item => isAssignmentEnded(item)).length;\r\n\r\n    const graded = assignments.filter(item => item.grading_status === '已批改').length;\r\n\r\n    return { pending, incomplete, submitted, makeup, graded, total: assignments.length };\r\n  };\r\n\r\n  // 获取最近作业\r\n  useEffect(() => {\r\n    const fetchData = async () => {\r\n      if (!user) {\r\n        setLoading(false);\r\n        return;\r\n      }\r\n      \r\n      try {\r\n        setLoading(true);\r\n        setError(null);\r\n        \r\n        // 获取令牌，检查是否有效\r\n        const token = localStorage.getItem('token');\r\n        if (!token) {\r\n          throw new Error(\"找不到认证令牌，请重新登录\");\r\n        }\r\n        \r\n        // 根据用户类型获取不同的数据\r\n        if (user.is_teacher || user.is_admin) {\r\n          // 教师和管理员获取作业数据\r\n          try {\r\n            const homeworksData = await getHomeworks({ limit: 5 });\r\n\r\n            // 确保homeworksData是数组\r\n            if (Array.isArray(homeworksData)) {\r\n              setRecentHomeworks(homeworksData);\r\n            } else if (homeworksData && Array.isArray(homeworksData.items)) {\r\n              setRecentHomeworks(homeworksData.items);\r\n            } else if (homeworksData && typeof homeworksData === 'object' && homeworksData.id) {\r\n              setRecentHomeworks([homeworksData]);\r\n            } else {\r\n              setRecentHomeworks(mockHomeworks);\r\n              message.warning(\"作业数据格式异常，显示模拟数据\");\r\n            }\r\n          } catch (homeworkError) {\r\n            console.error(\"获取作业数据失败:\", homeworkError);\r\n            setRecentHomeworks(mockHomeworks);\r\n            message.warning(\"无法加载作业数据，显示模拟数据\");\r\n          }\r\n        } else {\r\n          // 学生获取作业任务数据\r\n          try {\r\n            const assignments = await getStudentHomeworkAssignments();\r\n            console.log('获取到的学生作业任务:', assignments);\r\n\r\n            // 计算统计数据\r\n            const stats = calculateStudentStats(assignments);\r\n            setStudentStats(stats);\r\n\r\n            // 获取最近的作业任务作为展示\r\n            const recent = assignments.slice(0, 5).map(assignment => ({\r\n              id: assignment.homework_id || assignment.id, // 优先使用homework_id，如果没有则使用assignment_id\r\n              assignment_id: assignment.id, // 保留作业任务ID\r\n              homework_id: assignment.homework_id, // 作业ID\r\n              title: assignment.title,\r\n              status: assignment.submission_status === '已提交' ? 'submitted' : 'pending',\r\n              student_name: user.username,\r\n              created_at: assignment.created_at\r\n            }));\r\n            setRecentHomeworks(recent);\r\n          } catch (studentError) {\r\n            console.error(\"获取学生作业数据失败:\", studentError);\r\n            setRecentHomeworks(mockHomeworks);\r\n            message.warning(\"无法加载作业数据，显示模拟数据\");\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('获取数据失败:', error);\r\n        setError(error.message || \"获取数据失败，请稍后重试\");\r\n        \r\n        // 使用模拟数据\r\n        setRecentHomeworks(mockHomeworks);\r\n        \r\n        message.error('获取数据失败，请稍后刷新页面');\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n    \r\n    fetchData();\r\n  }, [user]);\r\n  \r\n  // 搜索处理\r\n  const onSearch = (value) => {\r\n    if (!value) return;\r\n    navigate(`/homework?search=${encodeURIComponent(value)}`);\r\n  };\r\n  \r\n  // 快捷操作处理\r\n  const handleQuickAction = (action) => {\r\n    console.log('Quick action:', action);\r\n    \r\n    switch (action) {\r\n      case 'upload':\r\n        navigate('/homework/upload');\r\n        break;\r\n      case 'submit':\r\n        // 学生点击提交作业，跳转到作业管理页面\r\n        navigate('/homework');\r\n        break;\r\n      case 'pending':\r\n        // 跳转到作业管理页面并过滤待提交作业\r\n        navigate('/homework', { state: { filter: 'pending' } });\r\n        break;\r\n      case 'incomplete':\r\n        // 跳转到作业管理页面并过滤未完成作业\r\n        navigate('/homework', { state: { filter: 'incomplete' } });\r\n        break;\r\n      case 'review':\r\n        // 跳转到作业管理页面并过滤已批改作业\r\n        navigate('/homework', { state: { filter: 'graded' } });\r\n        break;\r\n      case 'training':\r\n        navigate('/training');\r\n        break;\r\n      case 'photo-solve':\r\n        navigate('/photo-solve');\r\n        break;\r\n      case 'statistics':\r\n        navigate('/statistics');\r\n        break;\r\n      case 'settings':\r\n        navigate('/profile');\r\n        break;\r\n      case 'class':\r\n        navigate('/class-management');\r\n        break;\r\n      case 'admin':\r\n        navigate('/admin');\r\n        break;\r\n      case 'user':\r\n        if (user.is_admin) {\r\n          navigate('/user-management');\r\n        } else if (user.is_teacher) {\r\n          navigate('/teacher-user-management');\r\n        }\r\n        break;\r\n      default:\r\n        break;\r\n    }\r\n  };\r\n  \r\n  // 快捷操作项\r\n  const quickActions = !user ? [] : user.is_admin\r\n    ? [\r\n        {\r\n          key: 'admin',\r\n          title: '管理后台',\r\n          icon: <SettingOutlined />,\r\n          color: '#ff4d4f'\r\n        },\r\n        {\r\n          key: 'class',\r\n          title: '班级管理',\r\n          icon: <TeamOutlined />,\r\n          color: '#52c41a'\r\n        },\r\n        {\r\n          key: 'user',\r\n          title: '用户管理',\r\n          icon: <UserOutlined />,\r\n          color: '#1890ff'\r\n        },\r\n        {\r\n          key: 'statistics',\r\n          title: '统计报表',\r\n          icon: <BarChartOutlined />,\r\n          color: '#faad14'\r\n        }\r\n      ]\r\n    : user.is_teacher\r\n    ? [\r\n        {\r\n          key: 'upload',\r\n          title: '上传作业',\r\n          icon: <UploadOutlined />,\r\n          color: '#1890ff'\r\n        },\r\n        {\r\n          key: 'class',\r\n          title: '班级管理',\r\n          icon: <TeamOutlined />,\r\n          color: '#52c41a'\r\n        },\r\n        {\r\n          key: 'user',\r\n          title: '学生信息',\r\n          icon: <UserOutlined />,\r\n          color: '#722ed1'\r\n        },\r\n        {\r\n          key: 'review',\r\n          title: '批改结果',\r\n          icon: <FileTextOutlined />,\r\n          color: '#722ed1'\r\n        },\r\n        {\r\n          key: 'statistics',\r\n          title: '统计报表',\r\n          icon: <BarChartOutlined />,\r\n          color: '#faad14'\r\n        }\r\n      ]\r\n    : [\r\n        {\r\n          key: 'pending',\r\n          title: '待提交作业',\r\n          subtitle: `${studentStats.pending || 0}份`,\r\n          icon: '📝',\r\n          color: '#FF9500',\r\n          bgColor: 'rgba(255, 149, 0, 0.1)',\r\n          description: '需要及时完成的作业'\r\n        },\r\n        {\r\n          key: 'incomplete',\r\n          title: '未完成作业',\r\n          subtitle: `${studentStats.incomplete || 0}份`,\r\n          icon: '⚠️',\r\n          color: '#FF3B30',\r\n          bgColor: 'rgba(255, 59, 48, 0.1)',\r\n          description: '可以补交的作业'\r\n        },\r\n        {\r\n          key: 'review',\r\n          title: '查看批改',\r\n          subtitle: `${studentStats.graded || 0}份已批改`,\r\n          icon: '⭐',\r\n          color: '#30D158',\r\n          bgColor: 'rgba(48, 209, 88, 0.1)',\r\n          description: '查看批改结果和成绩'\r\n        },\r\n        {\r\n          key: 'training',\r\n          title: '错题训练',\r\n          subtitle: '强化练习',\r\n          icon: '💪',\r\n          color: '#AF52DE',\r\n          bgColor: 'rgba(175, 82, 222, 0.1)',\r\n          description: '针对性练习提升'\r\n        },\r\n        {\r\n          key: 'submit',\r\n          title: '作业管理',\r\n          subtitle: `共${studentStats.total || 0}份作业`,\r\n          icon: '📚',\r\n          color: '#4A90E2',\r\n          bgColor: 'rgba(74, 144, 226, 0.1)',\r\n          description: '查看所有作业任务'\r\n        },\r\n        {\r\n          key: 'photo-solve',\r\n          title: '拍照解题',\r\n          subtitle: 'AI智能解题',\r\n          icon: '📸',\r\n          color: '#FF6B6B',\r\n          bgColor: 'rgba(255, 107, 107, 0.1)',\r\n          description: '拍照上传题目，AI为您解答'\r\n        },\r\n        {\r\n          key: 'settings',\r\n          title: '个人中心',\r\n          subtitle: '设置与信息',\r\n          icon: '👤',\r\n          color: '#34C759',\r\n          bgColor: 'rgba(52, 199, 89, 0.1)',\r\n          description: '个人信息和设置'\r\n        }\r\n      ];\r\n\r\n  return (\r\n    <div style={{ padding: '20px 0' }}>\r\n      <Title level={2}>欢迎回来，{user?.username || '用户'}！</Title>\r\n      <Paragraph>智教云端智能辅导平台，让学习更高效！</Paragraph>\r\n      \r\n      <Spin spinning={loading}>\r\n        {/* 搜索框 */}\r\n        <Row gutter={[16, 16]}>\r\n          <Col span={24}>\r\n            <Search\r\n              placeholder=\"搜索作业或批改记录...\"\r\n              allowClear\r\n              enterButton=\"搜索\"\r\n              size=\"large\"\r\n              onSearch={onSearch}\r\n            />\r\n          </Col>\r\n        </Row>\r\n        \r\n        {error && (\r\n          <Row gutter={[16, 16]} style={{ marginTop: 20 }}>\r\n            <Col span={24}>\r\n              <Card style={{ backgroundColor: '#fff2f0', borderColor: '#ffccc7' }}>\r\n                <div>数据加载出错: {error}</div>\r\n                <Button \r\n                  type=\"primary\" \r\n                  style={{ marginTop: 10 }}\r\n                  onClick={() => window.location.reload()}\r\n                >\r\n                  刷新页面\r\n                </Button>\r\n              </Card>\r\n            </Col>\r\n          </Row>\r\n        )}\r\n        \r\n        {/* 快捷操作 */}\r\n        <div style={{ marginTop: 30 }}>\r\n          <Title level={4}>\r\n            {user && !user.is_teacher && !user.is_admin ? '📚 学习中心' : '快捷操作'}\r\n          </Title>\r\n          <Row gutter={[16, 16]}>\r\n            {quickActions.map((action) => (\r\n              <Col xs={12} sm={8} md={6} lg={6} key={action.key}>\r\n                <Card\r\n                  hoverable\r\n                  onClick={() => handleQuickAction(action.key)}\r\n                  style={{\r\n                    textAlign: 'center',\r\n                    background: action.bgColor || '#ffffff',\r\n                    border: action.bgColor ? `1px solid ${action.color}20` : '1px solid #d9d9d9',\r\n                    borderRadius: '12px',\r\n                    transition: 'all 0.3s ease'\r\n                  }}\r\n                  className=\"quick-action-card\"\r\n                  bodyStyle={{ padding: '20px' }}\r\n                >\r\n                  <div style={{\r\n                    fontSize: action.bgColor ? 28 : 32,\r\n                    color: action.color,\r\n                    marginBottom: action.bgColor ? 8 : 10\r\n                  }}>\r\n                    {action.icon}\r\n                  </div>\r\n                  <div style={{\r\n                    fontSize: '16px',\r\n                    fontWeight: 600,\r\n                    color: action.color,\r\n                    marginBottom: action.subtitle ? '4px' : '0'\r\n                  }}>\r\n                    {action.title}\r\n                  </div>\r\n                  {action.subtitle && (\r\n                    <div style={{\r\n                      fontSize: '12px',\r\n                      color: action.color,\r\n                      opacity: 0.8,\r\n                      marginBottom: '8px'\r\n                    }}>\r\n                      {action.subtitle}\r\n                    </div>\r\n                  )}\r\n                  {action.description && (\r\n                    <div style={{\r\n                      fontSize: '11px',\r\n                      color: '#666666',\r\n                      lineHeight: 1.3\r\n                    }}>\r\n                      {action.description}\r\n                    </div>\r\n                  )}\r\n                </Card>\r\n              </Col>\r\n            ))}\r\n          </Row>\r\n        </div>\r\n        \r\n        {/* 最近作业 */}\r\n        <div style={{ marginTop: 30 }}>\r\n          <Title level={4}>最近作业</Title>\r\n          <List\r\n            itemLayout=\"horizontal\"\r\n            dataSource={Array.isArray(recentHomeworks) ? recentHomeworks : (recentHomeworks?.items || [])}\r\n            renderItem={item => (\r\n              <List.Item \r\n                key={item.id}\r\n                onClick={() => navigate(`/homework/${item.id}`)}\r\n                style={{ cursor: 'pointer' }}\r\n              >\r\n                <List.Item.Meta\r\n                  avatar={<Avatar icon={<FileTextOutlined />} style={{ \r\n                    backgroundColor: \r\n                      item.status === 'graded' ? '#52c41a' : \r\n                      item.status === 'grading' ? '#faad14' : '#1890ff' \r\n                  }} />}\r\n                  title={item.title}\r\n                  description={`状态: ${\r\n                    item.status === 'graded' ? '已批改' : \r\n                    item.status === 'grading' ? '批改中' : '已提交'\r\n                  } | 学生: ${item.student_name || '未知'} | 提交时间: ${new Date(item.created_at).toLocaleString()}`}\r\n                />\r\n              </List.Item>\r\n            )}\r\n          />\r\n        </div>\r\n        \r\n        {/* AI助手提示 */}\r\n        <Card style={{ marginTop: 30, textAlign: 'center', background: '#f0f7ff' }}>\r\n          <Row align=\"middle\" gutter={16}>\r\n            <Col xs={24} sm={4}>\r\n              <RobotOutlined style={{ fontSize: 48, color: '#1890ff' }} />\r\n            </Col>\r\n            <Col xs={24} sm={16} style={{ textAlign: 'left' }}>\r\n              <Title level={4}>AI智能助手随时为您服务</Title>\r\n              <Paragraph>\r\n                有任何问题，可以点击右上角的\"AI助手\"按钮，获取智能解答和辅导。\r\n              </Paragraph>\r\n            </Col>\r\n            <Col xs={24} sm={4}>\r\n              <Button\r\n                type=\"primary\"\r\n                size=\"large\"\r\n                onClick={() => {\r\n                  console.log('点击立即咨询按钮');\r\n                  setAiDrawerVisible(true);\r\n                }}\r\n              >\r\n                立即咨询\r\n              </Button>\r\n            </Col>\r\n          </Row>\r\n        </Card>\r\n      </Spin>\r\n\r\n      {/* AI助手抽屉 */}\r\n      <Drawer\r\n        title=\"AI智能助手\"\r\n        placement=\"right\"\r\n        onClose={() => setAiDrawerVisible(false)}\r\n        open={aiDrawerVisible}\r\n        width={window.innerWidth <= 768 ? '100%' : 400}\r\n        style={{ zIndex: 1000 }}\r\n      >\r\n        <AIAssistant user={user} />\r\n      </Drawer>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Home;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SACEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,UAAU,EAAEC,MAAM,EAAEC,KAAK,EACzCC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAEC,IAAI,EAAEC,MAAM,QAC9B,MAAM;AACb,OAAOC,WAAW,MAAM,2BAA2B;AACnD,SACEC,cAAc,EAAEC,gBAAgB,EAAEC,YAAY,EAC9CC,mBAAmB,EAAEC,YAAY,EAAEC,gBAAgB,EACnDC,aAAa,EAAEC,YAAY,EAAEC,YAAY,EAAEC,eAAe,EAC1DC,mBAAmB,EAAEC,yBAAyB,EAAEC,cAAc,EAC9DC,gBAAgB,QACX,mBAAmB;AAC1B,SAASC,YAAY,EAAEC,6BAA6B,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3E,MAAM;EAAEC,KAAK;EAAEC;AAAU,CAAC,GAAG5B,UAAU;AACvC,MAAM;EAAE6B;AAAO,CAAC,GAAG3B,KAAK;;AAExB;AACA,MAAM4B,aAAa,GAAG,CACpB;EACEC,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,MAAM;EACbC,MAAM,EAAE,QAAQ;EAChBC,YAAY,EAAE,IAAI;EAClBC,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;AACrC,CAAC,EACD;EACEN,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,MAAM;EACbC,MAAM,EAAE,WAAW;EACnBC,YAAY,EAAE,IAAI;EAClBC,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;AACrC,CAAC,CACF;AAED,MAAMC,IAAI,GAAGA,CAAC;EAAEC,IAAI;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACnC,MAAMC,QAAQ,GAAG9C,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC+C,OAAO,EAAEC,UAAU,CAAC,GAAGlD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACmD,eAAe,EAAEC,kBAAkB,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACqD,YAAY,EAAEC,eAAe,CAAC,GAAGtD,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpD,MAAM,CAACuD,KAAK,EAAEC,QAAQ,CAAC,GAAGxD,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACyD,eAAe,EAAEC,kBAAkB,CAAC,GAAG1D,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACA,MAAM2D,iBAAiB,GAAIC,UAAU,IAAK;IACxC,IAAI,CAACA,UAAU,CAACC,WAAW,EAAE,OAAO,KAAK;IACzC,OAAOD,UAAU,CAACC,WAAW,CAACC,QAAQ,CAAC,eAAe,CAAC;EACzD,CAAC;;EAED;EACA,MAAMC,qBAAqB,GAAIC,WAAW,IAAK;IAC7C,MAAMC,sBAAsB,GAAGD,WAAW,CAACE,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,iBAAiB,KAAK,KAAK,CAAC;IAC3F,MAAMC,OAAO,GAAGJ,sBAAsB,CAACC,MAAM,CAACC,IAAI,IAAI,CAACR,iBAAiB,CAACQ,IAAI,CAAC,CAAC,CAACG,MAAM;IACtF,MAAMC,UAAU,GAAGN,sBAAsB,CAACC,MAAM,CAACC,IAAI,IAAIR,iBAAiB,CAACQ,IAAI,CAAC,CAAC,CAACG,MAAM;IAExF,MAAME,oBAAoB,GAAGR,WAAW,CAACE,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,iBAAiB,KAAK,KAAK,CAAC;IACzF,MAAMK,SAAS,GAAGD,oBAAoB,CAACN,MAAM,CAACC,IAAI,IAAI,CAACR,iBAAiB,CAACQ,IAAI,CAAC,CAAC,CAACG,MAAM;IACtF,MAAMI,MAAM,GAAGF,oBAAoB,CAACN,MAAM,CAACC,IAAI,IAAIR,iBAAiB,CAACQ,IAAI,CAAC,CAAC,CAACG,MAAM;IAElF,MAAMK,MAAM,GAAGX,WAAW,CAACE,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACS,cAAc,KAAK,KAAK,CAAC,CAACN,MAAM;IAE/E,OAAO;MAAED,OAAO;MAAEE,UAAU;MAAEE,SAAS;MAAEC,MAAM;MAAEC,MAAM;MAAEE,KAAK,EAAEb,WAAW,CAACM;IAAO,CAAC;EACtF,CAAC;;EAED;EACArE,SAAS,CAAC,MAAM;IACd,MAAM6E,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI,CAACjC,IAAI,EAAE;QACTK,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;MAEA,IAAI;QACFA,UAAU,CAAC,IAAI,CAAC;QAChBM,QAAQ,CAAC,IAAI,CAAC;;QAEd;QACA,MAAMuB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC3C,IAAI,CAACF,KAAK,EAAE;UACV,MAAM,IAAIG,KAAK,CAAC,eAAe,CAAC;QAClC;;QAEA;QACA,IAAIrC,IAAI,CAACsC,UAAU,IAAItC,IAAI,CAACuC,QAAQ,EAAE;UACpC;UACA,IAAI;YACF,MAAMC,aAAa,GAAG,MAAMxD,YAAY,CAAC;cAAEyD,KAAK,EAAE;YAAE,CAAC,CAAC;;YAEtD;YACA,IAAIC,KAAK,CAACC,OAAO,CAACH,aAAa,CAAC,EAAE;cAChCjC,kBAAkB,CAACiC,aAAa,CAAC;YACnC,CAAC,MAAM,IAAIA,aAAa,IAAIE,KAAK,CAACC,OAAO,CAACH,aAAa,CAACI,KAAK,CAAC,EAAE;cAC9DrC,kBAAkB,CAACiC,aAAa,CAACI,KAAK,CAAC;YACzC,CAAC,MAAM,IAAIJ,aAAa,IAAI,OAAOA,aAAa,KAAK,QAAQ,IAAIA,aAAa,CAAChD,EAAE,EAAE;cACjFe,kBAAkB,CAAC,CAACiC,aAAa,CAAC,CAAC;YACrC,CAAC,MAAM;cACLjC,kBAAkB,CAAChB,aAAa,CAAC;cACjCzB,OAAO,CAAC+E,OAAO,CAAC,iBAAiB,CAAC;YACpC;UACF,CAAC,CAAC,OAAOC,aAAa,EAAE;YACtBC,OAAO,CAACrC,KAAK,CAAC,WAAW,EAAEoC,aAAa,CAAC;YACzCvC,kBAAkB,CAAChB,aAAa,CAAC;YACjCzB,OAAO,CAAC+E,OAAO,CAAC,iBAAiB,CAAC;UACpC;QACF,CAAC,MAAM;UACL;UACA,IAAI;YACF,MAAM1B,WAAW,GAAG,MAAMlC,6BAA6B,CAAC,CAAC;YACzD8D,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE7B,WAAW,CAAC;;YAEvC;YACA,MAAM8B,KAAK,GAAG/B,qBAAqB,CAACC,WAAW,CAAC;YAChDV,eAAe,CAACwC,KAAK,CAAC;;YAEtB;YACA,MAAMC,MAAM,GAAG/B,WAAW,CAACgC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAACrC,UAAU,KAAK;cACxDvB,EAAE,EAAEuB,UAAU,CAACsC,WAAW,IAAItC,UAAU,CAACvB,EAAE;cAAE;cAC7C8D,aAAa,EAAEvC,UAAU,CAACvB,EAAE;cAAE;cAC9B6D,WAAW,EAAEtC,UAAU,CAACsC,WAAW;cAAE;cACrC5D,KAAK,EAAEsB,UAAU,CAACtB,KAAK;cACvBC,MAAM,EAAEqB,UAAU,CAACQ,iBAAiB,KAAK,KAAK,GAAG,WAAW,GAAG,SAAS;cACxE5B,YAAY,EAAEK,IAAI,CAACuD,QAAQ;cAC3B3D,UAAU,EAAEmB,UAAU,CAACnB;YACzB,CAAC,CAAC,CAAC;YACHW,kBAAkB,CAAC2C,MAAM,CAAC;UAC5B,CAAC,CAAC,OAAOM,YAAY,EAAE;YACrBT,OAAO,CAACrC,KAAK,CAAC,aAAa,EAAE8C,YAAY,CAAC;YAC1CjD,kBAAkB,CAAChB,aAAa,CAAC;YACjCzB,OAAO,CAAC+E,OAAO,CAAC,iBAAiB,CAAC;UACpC;QACF;MACF,CAAC,CAAC,OAAOnC,KAAK,EAAE;QACdqC,OAAO,CAACrC,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;QAC/BC,QAAQ,CAACD,KAAK,CAAC5C,OAAO,IAAI,cAAc,CAAC;;QAEzC;QACAyC,kBAAkB,CAAChB,aAAa,CAAC;QAEjCzB,OAAO,CAAC4C,KAAK,CAAC,gBAAgB,CAAC;MACjC,CAAC,SAAS;QACRL,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED4B,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACjC,IAAI,CAAC,CAAC;;EAEV;EACA,MAAMyD,QAAQ,GAAIC,KAAK,IAAK;IAC1B,IAAI,CAACA,KAAK,EAAE;IACZvD,QAAQ,CAAC,oBAAoBwD,kBAAkB,CAACD,KAAK,CAAC,EAAE,CAAC;EAC3D,CAAC;;EAED;EACA,MAAME,iBAAiB,GAAIC,MAAM,IAAK;IACpCd,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEa,MAAM,CAAC;IAEpC,QAAQA,MAAM;MACZ,KAAK,QAAQ;QACX1D,QAAQ,CAAC,kBAAkB,CAAC;QAC5B;MACF,KAAK,QAAQ;QACX;QACAA,QAAQ,CAAC,WAAW,CAAC;QACrB;MACF,KAAK,SAAS;QACZ;QACAA,QAAQ,CAAC,WAAW,EAAE;UAAE2D,KAAK,EAAE;YAAEzC,MAAM,EAAE;UAAU;QAAE,CAAC,CAAC;QACvD;MACF,KAAK,YAAY;QACf;QACAlB,QAAQ,CAAC,WAAW,EAAE;UAAE2D,KAAK,EAAE;YAAEzC,MAAM,EAAE;UAAa;QAAE,CAAC,CAAC;QAC1D;MACF,KAAK,QAAQ;QACX;QACAlB,QAAQ,CAAC,WAAW,EAAE;UAAE2D,KAAK,EAAE;YAAEzC,MAAM,EAAE;UAAS;QAAE,CAAC,CAAC;QACtD;MACF,KAAK,UAAU;QACblB,QAAQ,CAAC,WAAW,CAAC;QACrB;MACF,KAAK,aAAa;QAChBA,QAAQ,CAAC,cAAc,CAAC;QACxB;MACF,KAAK,YAAY;QACfA,QAAQ,CAAC,aAAa,CAAC;QACvB;MACF,KAAK,UAAU;QACbA,QAAQ,CAAC,UAAU,CAAC;QACpB;MACF,KAAK,OAAO;QACVA,QAAQ,CAAC,mBAAmB,CAAC;QAC7B;MACF,KAAK,OAAO;QACVA,QAAQ,CAAC,QAAQ,CAAC;QAClB;MACF,KAAK,MAAM;QACT,IAAIH,IAAI,CAACuC,QAAQ,EAAE;UACjBpC,QAAQ,CAAC,kBAAkB,CAAC;QAC9B,CAAC,MAAM,IAAIH,IAAI,CAACsC,UAAU,EAAE;UAC1BnC,QAAQ,CAAC,0BAA0B,CAAC;QACtC;QACA;MACF;QACE;IACJ;EACF,CAAC;;EAED;EACA,MAAM4D,YAAY,GAAG,CAAC/D,IAAI,GAAG,EAAE,GAAGA,IAAI,CAACuC,QAAQ,GAC3C,CACE;IACEyB,GAAG,EAAE,OAAO;IACZvE,KAAK,EAAE,MAAM;IACbwE,IAAI,eAAE9E,OAAA,CAACR,eAAe;MAAAuF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,OAAO;IACZvE,KAAK,EAAE,MAAM;IACbwE,IAAI,eAAE9E,OAAA,CAACT,YAAY;MAAAwF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,MAAM;IACXvE,KAAK,EAAE,MAAM;IACbwE,IAAI,eAAE9E,OAAA,CAACV,YAAY;MAAAyF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,YAAY;IACjBvE,KAAK,EAAE,MAAM;IACbwE,IAAI,eAAE9E,OAAA,CAACZ,gBAAgB;MAAA2F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1BC,KAAK,EAAE;EACT,CAAC,CACF,GACDtE,IAAI,CAACsC,UAAU,GACf,CACE;IACE0B,GAAG,EAAE,QAAQ;IACbvE,KAAK,EAAE,MAAM;IACbwE,IAAI,eAAE9E,OAAA,CAACjB,cAAc;MAAAgG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,OAAO;IACZvE,KAAK,EAAE,MAAM;IACbwE,IAAI,eAAE9E,OAAA,CAACT,YAAY;MAAAwF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,MAAM;IACXvE,KAAK,EAAE,MAAM;IACbwE,IAAI,eAAE9E,OAAA,CAACV,YAAY;MAAAyF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,QAAQ;IACbvE,KAAK,EAAE,MAAM;IACbwE,IAAI,eAAE9E,OAAA,CAAChB,gBAAgB;MAAA+F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1BC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,YAAY;IACjBvE,KAAK,EAAE,MAAM;IACbwE,IAAI,eAAE9E,OAAA,CAACZ,gBAAgB;MAAA2F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1BC,KAAK,EAAE;EACT,CAAC,CACF,GACD,CACE;IACEN,GAAG,EAAE,SAAS;IACdvE,KAAK,EAAE,OAAO;IACd8E,QAAQ,EAAE,GAAG/D,YAAY,CAACgB,OAAO,IAAI,CAAC,GAAG;IACzCyC,IAAI,EAAE,IAAI;IACVK,KAAK,EAAE,SAAS;IAChBE,OAAO,EAAE,wBAAwB;IACjCxD,WAAW,EAAE;EACf,CAAC,EACD;IACEgD,GAAG,EAAE,YAAY;IACjBvE,KAAK,EAAE,OAAO;IACd8E,QAAQ,EAAE,GAAG/D,YAAY,CAACkB,UAAU,IAAI,CAAC,GAAG;IAC5CuC,IAAI,EAAE,IAAI;IACVK,KAAK,EAAE,SAAS;IAChBE,OAAO,EAAE,wBAAwB;IACjCxD,WAAW,EAAE;EACf,CAAC,EACD;IACEgD,GAAG,EAAE,QAAQ;IACbvE,KAAK,EAAE,MAAM;IACb8E,QAAQ,EAAE,GAAG/D,YAAY,CAACsB,MAAM,IAAI,CAAC,MAAM;IAC3CmC,IAAI,EAAE,GAAG;IACTK,KAAK,EAAE,SAAS;IAChBE,OAAO,EAAE,wBAAwB;IACjCxD,WAAW,EAAE;EACf,CAAC,EACD;IACEgD,GAAG,EAAE,UAAU;IACfvE,KAAK,EAAE,MAAM;IACb8E,QAAQ,EAAE,MAAM;IAChBN,IAAI,EAAE,IAAI;IACVK,KAAK,EAAE,SAAS;IAChBE,OAAO,EAAE,yBAAyB;IAClCxD,WAAW,EAAE;EACf,CAAC,EACD;IACEgD,GAAG,EAAE,QAAQ;IACbvE,KAAK,EAAE,MAAM;IACb8E,QAAQ,EAAE,IAAI/D,YAAY,CAACwB,KAAK,IAAI,CAAC,KAAK;IAC1CiC,IAAI,EAAE,IAAI;IACVK,KAAK,EAAE,SAAS;IAChBE,OAAO,EAAE,yBAAyB;IAClCxD,WAAW,EAAE;EACf,CAAC,EACD;IACEgD,GAAG,EAAE,aAAa;IAClBvE,KAAK,EAAE,MAAM;IACb8E,QAAQ,EAAE,QAAQ;IAClBN,IAAI,EAAE,IAAI;IACVK,KAAK,EAAE,SAAS;IAChBE,OAAO,EAAE,0BAA0B;IACnCxD,WAAW,EAAE;EACf,CAAC,EACD;IACEgD,GAAG,EAAE,UAAU;IACfvE,KAAK,EAAE,MAAM;IACb8E,QAAQ,EAAE,OAAO;IACjBN,IAAI,EAAE,IAAI;IACVK,KAAK,EAAE,SAAS;IAChBE,OAAO,EAAE,wBAAwB;IACjCxD,WAAW,EAAE;EACf,CAAC,CACF;EAEL,oBACE7B,OAAA;IAAKsF,KAAK,EAAE;MAAEC,OAAO,EAAE;IAAS,CAAE;IAAAC,QAAA,gBAChCxF,OAAA,CAACC,KAAK;MAACwF,KAAK,EAAE,CAAE;MAAAD,QAAA,GAAC,gCAAK,EAAC,CAAA3E,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuD,QAAQ,KAAI,IAAI,EAAC,QAAC;IAAA;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eACvDlF,OAAA,CAACE,SAAS;MAAAsF,QAAA,EAAC;IAAkB;MAAAT,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAW,CAAC,eAEzClF,OAAA,CAACpB,IAAI;MAAC8G,QAAQ,EAAEzE,OAAQ;MAAAuE,QAAA,gBAEtBxF,OAAA,CAAC7B,GAAG;QAACwH,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;QAAAH,QAAA,eACpBxF,OAAA,CAAC5B,GAAG;UAACwH,IAAI,EAAE,EAAG;UAAAJ,QAAA,eACZxF,OAAA,CAACG,MAAM;YACL0F,WAAW,EAAC,2DAAc;YAC1BC,UAAU;YACVC,WAAW,EAAC,cAAI;YAChBC,IAAI,EAAC,OAAO;YACZ1B,QAAQ,EAAEA;UAAS;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAEL3D,KAAK,iBACJvB,OAAA,CAAC7B,GAAG;QAACwH,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;QAACL,KAAK,EAAE;UAAEW,SAAS,EAAE;QAAG,CAAE;QAAAT,QAAA,eAC9CxF,OAAA,CAAC5B,GAAG;UAACwH,IAAI,EAAE,EAAG;UAAAJ,QAAA,eACZxF,OAAA,CAAC3B,IAAI;YAACiH,KAAK,EAAE;cAAEY,eAAe,EAAE,SAAS;cAAEC,WAAW,EAAE;YAAU,CAAE;YAAAX,QAAA,gBAClExF,OAAA;cAAAwF,QAAA,GAAK,wCAAQ,EAACjE,KAAK;YAAA;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1BlF,OAAA,CAACzB,MAAM;cACL6H,IAAI,EAAC,SAAS;cACdd,KAAK,EAAE;gBAAEW,SAAS,EAAE;cAAG,CAAE;cACzBI,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;cAAAhB,QAAA,EACzC;YAED;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAGDlF,OAAA;QAAKsF,KAAK,EAAE;UAAEW,SAAS,EAAE;QAAG,CAAE;QAAAT,QAAA,gBAC5BxF,OAAA,CAACC,KAAK;UAACwF,KAAK,EAAE,CAAE;UAAAD,QAAA,EACb3E,IAAI,IAAI,CAACA,IAAI,CAACsC,UAAU,IAAI,CAACtC,IAAI,CAACuC,QAAQ,GAAG,SAAS,GAAG;QAAM;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC,eACRlF,OAAA,CAAC7B,GAAG;UAACwH,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;UAAAH,QAAA,EACnBZ,YAAY,CAACX,GAAG,CAAES,MAAM,iBACvB1E,OAAA,CAAC5B,GAAG;YAACqI,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAApB,QAAA,eAC/BxF,OAAA,CAAC3B,IAAI;cACHwI,SAAS;cACTR,OAAO,EAAEA,CAAA,KAAM5B,iBAAiB,CAACC,MAAM,CAACG,GAAG,CAAE;cAC7CS,KAAK,EAAE;gBACLwB,SAAS,EAAE,QAAQ;gBACnBC,UAAU,EAAErC,MAAM,CAACW,OAAO,IAAI,SAAS;gBACvC2B,MAAM,EAAEtC,MAAM,CAACW,OAAO,GAAG,aAAaX,MAAM,CAACS,KAAK,IAAI,GAAG,mBAAmB;gBAC5E8B,YAAY,EAAE,MAAM;gBACpBC,UAAU,EAAE;cACd,CAAE;cACFC,SAAS,EAAC,mBAAmB;cAC7BC,SAAS,EAAE;gBAAE7B,OAAO,EAAE;cAAO,CAAE;cAAAC,QAAA,gBAE/BxF,OAAA;gBAAKsF,KAAK,EAAE;kBACV+B,QAAQ,EAAE3C,MAAM,CAACW,OAAO,GAAG,EAAE,GAAG,EAAE;kBAClCF,KAAK,EAAET,MAAM,CAACS,KAAK;kBACnBmC,YAAY,EAAE5C,MAAM,CAACW,OAAO,GAAG,CAAC,GAAG;gBACrC,CAAE;gBAAAG,QAAA,EACCd,MAAM,CAACI;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACNlF,OAAA;gBAAKsF,KAAK,EAAE;kBACV+B,QAAQ,EAAE,MAAM;kBAChBE,UAAU,EAAE,GAAG;kBACfpC,KAAK,EAAET,MAAM,CAACS,KAAK;kBACnBmC,YAAY,EAAE5C,MAAM,CAACU,QAAQ,GAAG,KAAK,GAAG;gBAC1C,CAAE;gBAAAI,QAAA,EACCd,MAAM,CAACpE;cAAK;gBAAAyE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,EACLR,MAAM,CAACU,QAAQ,iBACdpF,OAAA;gBAAKsF,KAAK,EAAE;kBACV+B,QAAQ,EAAE,MAAM;kBAChBlC,KAAK,EAAET,MAAM,CAACS,KAAK;kBACnBqC,OAAO,EAAE,GAAG;kBACZF,YAAY,EAAE;gBAChB,CAAE;gBAAA9B,QAAA,EACCd,MAAM,CAACU;cAAQ;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CACN,EACAR,MAAM,CAAC7C,WAAW,iBACjB7B,OAAA;gBAAKsF,KAAK,EAAE;kBACV+B,QAAQ,EAAE,MAAM;kBAChBlC,KAAK,EAAE,SAAS;kBAChBsC,UAAU,EAAE;gBACd,CAAE;gBAAAjC,QAAA,EACCd,MAAM,CAAC7C;cAAW;gBAAAkD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UAAC,GAhD8BR,MAAM,CAACG,GAAG;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAiD5C,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNlF,OAAA;QAAKsF,KAAK,EAAE;UAAEW,SAAS,EAAE;QAAG,CAAE;QAAAT,QAAA,gBAC5BxF,OAAA,CAACC,KAAK;UAACwF,KAAK,EAAE,CAAE;UAAAD,QAAA,EAAC;QAAI;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC7BlF,OAAA,CAACvB,IAAI;UACHiJ,UAAU,EAAC,YAAY;UACvBC,UAAU,EAAEpE,KAAK,CAACC,OAAO,CAACrC,eAAe,CAAC,GAAGA,eAAe,GAAI,CAAAA,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEsC,KAAK,KAAI,EAAI;UAC9FmE,UAAU,EAAEzF,IAAI,iBACdnC,OAAA,CAACvB,IAAI,CAACoJ,IAAI;YAERxB,OAAO,EAAEA,CAAA,KAAMrF,QAAQ,CAAC,aAAamB,IAAI,CAAC9B,EAAE,EAAE,CAAE;YAChDiF,KAAK,EAAE;cAAEwC,MAAM,EAAE;YAAU,CAAE;YAAAtC,QAAA,eAE7BxF,OAAA,CAACvB,IAAI,CAACoJ,IAAI,CAACE,IAAI;cACbC,MAAM,eAAEhI,OAAA,CAACtB,MAAM;gBAACoG,IAAI,eAAE9E,OAAA,CAAChB,gBAAgB;kBAAA+F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAACI,KAAK,EAAE;kBACjDY,eAAe,EACb/D,IAAI,CAAC5B,MAAM,KAAK,QAAQ,GAAG,SAAS,GACpC4B,IAAI,CAAC5B,MAAM,KAAK,SAAS,GAAG,SAAS,GAAG;gBAC5C;cAAE;gBAAAwE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACN5E,KAAK,EAAE6B,IAAI,CAAC7B,KAAM;cAClBuB,WAAW,EAAE,OACXM,IAAI,CAAC5B,MAAM,KAAK,QAAQ,GAAG,KAAK,GAChC4B,IAAI,CAAC5B,MAAM,KAAK,SAAS,GAAG,KAAK,GAAG,KAAK,UACjC4B,IAAI,CAAC3B,YAAY,IAAI,IAAI,YAAY,IAAIE,IAAI,CAACyB,IAAI,CAAC1B,UAAU,CAAC,CAACwH,cAAc,CAAC,CAAC;YAAG;cAAAlD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7F;UAAC,GAfG/C,IAAI,CAAC9B,EAAE;YAAA0E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgBH;QACX;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNlF,OAAA,CAAC3B,IAAI;QAACiH,KAAK,EAAE;UAAEW,SAAS,EAAE,EAAE;UAAEa,SAAS,EAAE,QAAQ;UAAEC,UAAU,EAAE;QAAU,CAAE;QAAAvB,QAAA,eACzExF,OAAA,CAAC7B,GAAG;UAAC+J,KAAK,EAAC,QAAQ;UAACvC,MAAM,EAAE,EAAG;UAAAH,QAAA,gBAC7BxF,OAAA,CAAC5B,GAAG;YAACqI,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAlB,QAAA,eACjBxF,OAAA,CAACX,aAAa;cAACiG,KAAK,EAAE;gBAAE+B,QAAQ,EAAE,EAAE;gBAAElC,KAAK,EAAE;cAAU;YAAE;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,eACNlF,OAAA,CAAC5B,GAAG;YAACqI,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACpB,KAAK,EAAE;cAAEwB,SAAS,EAAE;YAAO,CAAE;YAAAtB,QAAA,gBAChDxF,OAAA,CAACC,KAAK;cAACwF,KAAK,EAAE,CAAE;cAAAD,QAAA,EAAC;YAAY;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACrClF,OAAA,CAACE,SAAS;cAAAsF,QAAA,EAAC;YAEX;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNlF,OAAA,CAAC5B,GAAG;YAACqI,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAlB,QAAA,eACjBxF,OAAA,CAACzB,MAAM;cACL6H,IAAI,EAAC,SAAS;cACdJ,IAAI,EAAC,OAAO;cACZK,OAAO,EAAEA,CAAA,KAAM;gBACbzC,OAAO,CAACC,GAAG,CAAC,UAAU,CAAC;gBACvBnC,kBAAkB,CAAC,IAAI,CAAC;cAC1B,CAAE;cAAA8D,QAAA,EACH;YAED;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGPlF,OAAA,CAACnB,MAAM;MACLyB,KAAK,EAAC,4BAAQ;MACd6H,SAAS,EAAC,OAAO;MACjBC,OAAO,EAAEA,CAAA,KAAM1G,kBAAkB,CAAC,KAAK,CAAE;MACzC2G,IAAI,EAAE5G,eAAgB;MACtB6G,KAAK,EAAEhC,MAAM,CAACiC,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,GAAI;MAC/CjD,KAAK,EAAE;QAAEkD,MAAM,EAAE;MAAK,CAAE;MAAAhD,QAAA,eAExBxF,OAAA,CAAClB,WAAW;QAAC+B,IAAI,EAAEA;MAAK;QAAAkE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACnE,EAAA,CAldIH,IAAI;EAAA,QACS1C,WAAW;AAAA;AAAAuK,EAAA,GADxB7H,IAAI;AAodV,eAAeA,IAAI;AAAC,IAAA6H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}