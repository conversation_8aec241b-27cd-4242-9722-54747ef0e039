{"ast": null, "code": "var _jsxFileName = \"D:\\\\pythonproject\\\\checkingsys\\\\frontend\\\\src\\\\pages\\\\HomeworkManagement.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect, createContext } from 'react';\nimport { useNavigate, useLocation, useSearchParams, Routes, Route } from 'react-router-dom';\nimport { Layout, Menu, Breadcrumb, Typography, Card, Row, Col, Select, Space, Button, message, Tabs } from 'antd';\nimport { UploadOutlined, FileTextOutlined, PlusOutlined, AppstoreOutlined, CloudUploadOutlined, BarChartOutlined, CheckCircleOutlined, CalendarOutlined, ExclamationCircleOutlined, FilterOutlined, ReloadOutlined } from '@ant-design/icons';\nimport HomeworkUpload from '../components/HomeworkUpload';\nimport HomeworkDetail from '../components/HomeworkDetail';\nimport HomeworkAssignmentCreate from '../components/HomeworkAssignmentCreate';\nimport HomeworkAssignmentList from '../components/HomeworkAssignmentList';\nimport StudentHomeworkUpload from '../components/StudentHomeworkUpload';\nimport HomeworkAssignmentDetail from '../components/HomeworkAssignmentDetail';\nimport HomeworkUploadOptions from '../components/HomeworkUploadOptions';\nimport HomeworkStatistics from '../components/HomeworkStatistics';\nimport HomeworkAssignmentProgress from '../components/HomeworkAssignmentProgress';\nimport PendingReviewHomeworks from '../components/PendingReviewHomeworks';\nimport HomeworkCalendar from '../components/HomeworkCalendar';\nimport FinishedHomeworks from '../components/FinishedHomeworks';\nimport StudentHomeworkAssignmentList from '../components/StudentHomeworkAssignmentList';\nimport StudentSubmitChoice from '../components/StudentSubmitChoice';\nimport StudentHomeworkHistory from '../components/student/StudentHomeworkHistory';\nimport StudentHomeworkReview from '../components/student/StudentHomeworkReview';\nimport StudentHomeworkDetail from '../components/student/StudentHomeworkDetail';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Sider,\n  Content\n} = Layout;\nconst {\n  Title\n} = Typography;\nconst {\n  ItemGroup\n} = Menu;\nconst {\n  Option\n} = Select;\n\n// 创建筛选上下文\nconst FilterContext = /*#__PURE__*/createContext();\n\n// 筛选组件\nconst SystemHomeworkFilter = ({\n  user,\n  onFilterChange\n}) => {\n  _s();\n  const [schools, setSchools] = useState([]);\n  const [grades, setGrades] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [filters, setFilters] = useState({\n    schoolId: null,\n    gradeId: null\n  });\n\n  // 是否为超级管理员\n  const isSuperAdmin = user.is_admin && user.role === '超级管理员';\n\n  // 获取学校列表\n  const fetchSchools = async () => {\n    try {\n      setLoading(true);\n      const response = await fetch('/api/admin/schools', {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        }\n      });\n      if (response.ok) {\n        const data = await response.json();\n        setSchools(data);\n\n        // 设置默认学校为四川省双流中学（ID=1）\n        const defaultSchool = data.find(school => school.id === 1);\n        if (defaultSchool) {\n          const defaultFilters = {\n            schoolId: 1,\n            gradeId: null,\n            gradeName: null\n          };\n          setFilters(defaultFilters);\n          fetchGrades(1);\n          onFilterChange(defaultFilters);\n        } else {\n          // 过滤掉ID无效的学校\n          const validSchools = data.filter(school => school.id !== undefined && school.id !== null && school.name);\n\n          // 查找四川省双流中学\n          const shuangliuSchool = validSchools.find(school => school.name.includes('双流中学'));\n          let defaultSchool = null;\n          if (shuangliuSchool) {\n            defaultSchool = shuangliuSchool;\n          } else if (validSchools.length > 0) {\n            defaultSchool = validSchools[0];\n          }\n          if (defaultSchool) {\n            const defaultFilters = {\n              schoolId: defaultSchool.id,\n              gradeId: null,\n              gradeName: null\n            };\n            setFilters(defaultFilters);\n            fetchGrades(defaultSchool.id);\n            onFilterChange(defaultFilters);\n          }\n        }\n      }\n    } catch (error) {\n      console.error('获取学校列表失败:', error);\n      message.error('获取学校列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 年级排序函数\n  const sortGrades = grades => {\n    const gradeOrder = {\n      // 小学\n      '小学一年级': 1,\n      '小学二年级': 2,\n      '小学三年级': 3,\n      '小学四年级': 4,\n      '小学五年级': 5,\n      '小学六年级': 6,\n      // 初中\n      '初中一年级': 7,\n      '初中二年级': 8,\n      '初中三年级': 9,\n      '初一': 7,\n      '初二': 8,\n      '初三': 9,\n      // 高中\n      '高中一年级': 10,\n      '高中二年级': 11,\n      '高中三年级': 12,\n      '高一': 10,\n      '高二': 11,\n      '高三': 12,\n      // 其他常见格式\n      '一年级': 1,\n      '二年级': 2,\n      '三年级': 3,\n      '四年级': 4,\n      '五年级': 5,\n      '六年级': 6,\n      '七年级': 7,\n      '八年级': 8,\n      '九年级': 9,\n      '十年级': 10,\n      '十一年级': 11,\n      '十二年级': 12\n    };\n    return grades.sort((a, b) => {\n      const orderA = gradeOrder[a.name] || 999;\n      const orderB = gradeOrder[b.name] || 999;\n      return orderA - orderB;\n    });\n  };\n\n  // 获取年级列表\n  const fetchGrades = async schoolId => {\n    if (!schoolId) {\n      setGrades([]);\n      return;\n    }\n    try {\n      const response = await fetch(`/api/admin/classes/grades?school_id=${schoolId}`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        }\n      });\n      if (response.ok) {\n        const data = await response.json();\n        // 将年级名称转换为带ID的对象\n        const gradeOptions = data.map((gradeName, index) => ({\n          id: index + 1,\n          name: gradeName\n        }));\n        // 对年级进行排序\n        const sortedGrades = sortGrades(gradeOptions);\n        setGrades(sortedGrades);\n      } else {\n        setGrades([]);\n      }\n    } catch (error) {\n      console.error('获取年级列表失败:', error);\n      message.error('获取年级列表失败');\n      // 设置默认年级列表\n      const defaultGrades = [{\n        id: 1,\n        name: '小学一年级'\n      }, {\n        id: 2,\n        name: '小学二年级'\n      }, {\n        id: 3,\n        name: '小学三年级'\n      }, {\n        id: 4,\n        name: '小学四年级'\n      }, {\n        id: 5,\n        name: '小学五年级'\n      }, {\n        id: 6,\n        name: '小学六年级'\n      }, {\n        id: 7,\n        name: '初中一年级'\n      }, {\n        id: 8,\n        name: '初中二年级'\n      }, {\n        id: 9,\n        name: '初中三年级'\n      }, {\n        id: 10,\n        name: '高中一年级'\n      }, {\n        id: 11,\n        name: '高中二年级'\n      }, {\n        id: 12,\n        name: '高中三年级'\n      }];\n      setGrades(defaultGrades);\n    }\n  };\n\n  // 初始化数据\n  useEffect(() => {\n    if (isSuperAdmin) {\n      fetchSchools(); // fetchSchools内部会设置默认学校\n    } else {\n      // 学校管理员直接获取年级\n      if (user.school_id) {\n        fetchGrades(user.school_id);\n        const defaultFilters = {\n          schoolId: user.school_id,\n          gradeId: null,\n          gradeName: null\n        };\n        setFilters(defaultFilters);\n        onFilterChange(defaultFilters);\n      }\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [user, isSuperAdmin]);\n\n  // 处理学校变化\n  const handleSchoolChange = value => {\n    const newFilters = {\n      schoolId: value,\n      gradeId: null,\n      gradeName: null\n    };\n    setFilters(newFilters);\n    setGrades([]);\n    if (value) {\n      fetchGrades(value);\n    }\n    onFilterChange(newFilters);\n  };\n\n  // 处理年级变化\n  const handleGradeChange = value => {\n    const selectedGrade = grades.find(g => g.id === value);\n    const newFilters = {\n      ...filters,\n      gradeId: value,\n      gradeName: selectedGrade === null || selectedGrade === void 0 ? void 0 : selectedGrade.name\n    };\n    setFilters(newFilters);\n    onFilterChange(newFilters);\n  };\n\n  // 重置筛选\n  const handleReset = () => {\n    let defaultSchoolId = user.school_id;\n    if (isSuperAdmin) {\n      // 超级管理员：使用四川省双流中学（ID=1）作为默认\n      const defaultSchool = schools.find(school => school.id === 1);\n      defaultSchoolId = defaultSchool ? 1 : schools.length > 0 ? schools[0].id : null;\n    }\n    const resetFilters = {\n      schoolId: defaultSchoolId,\n      gradeId: null,\n      gradeName: null\n    };\n    setFilters(resetFilters);\n    setGrades([]);\n\n    // 重新获取年级数据\n    if (defaultSchoolId) {\n      fetchGrades(defaultSchoolId);\n    }\n    onFilterChange(resetFilters);\n  };\n  return /*#__PURE__*/_jsxDEV(Card, {\n    title: /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(FilterOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 11\n      }, this), \"\\u7B5B\\u9009\\u6761\\u4EF6\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 271,\n      columnNumber: 9\n    }, this),\n    size: \"small\",\n    style: {\n      marginBottom: 16\n    },\n    children: /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      children: [isSuperAdmin && /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Select, {\n          placeholder: \"\\u9009\\u62E9\\u5B66\\u6821\",\n          style: {\n            width: '100%'\n          },\n          value: filters.schoolId,\n          onChange: handleSchoolChange,\n          loading: loading,\n          allowClear: true,\n          children: schools.filter(school => school.id !== undefined && school.id !== null && school.name).map(school => /*#__PURE__*/_jsxDEV(Option, {\n            value: school.id,\n            children: school.name\n          }, school.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 282,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: isSuperAdmin ? 8 : 12,\n        children: /*#__PURE__*/_jsxDEV(Select, {\n          placeholder: \"\\u9009\\u62E9\\u5E74\\u7EA7\",\n          style: {\n            width: '100%'\n          },\n          value: filters.gradeId,\n          onChange: handleGradeChange,\n          disabled: isSuperAdmin && !filters.schoolId,\n          allowClear: true,\n          children: grades.map(grade => /*#__PURE__*/_jsxDEV(Option, {\n            value: grade.id,\n            children: grade.name\n          }, grade.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 307,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: isSuperAdmin ? 8 : 12,\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 19\n          }, this),\n          onClick: handleReset,\n          children: \"\\u91CD\\u7F6E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 324,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 279,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 269,\n    columnNumber: 5\n  }, this);\n};\n_s(SystemHomeworkFilter, \"FQU6jSD7Y+/a/7qvxdrj2cjBjHA=\");\n_c = SystemHomeworkFilter;\nconst HomeworkManagement = ({\n  user,\n  onLogout,\n  isSystemLevel = false\n}) => {\n  _s2();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [searchParams] = useSearchParams();\n  const [selectedKey, setSelectedKey] = useState(user.is_teacher || isSystemLevel ? '7' : 'student-assignments');\n  const [systemFilters, setSystemFilters] = useState({\n    schoolId: null,\n    gradeId: null,\n    classId: null\n  });\n\n  // 移动端检测\n  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);\n  useEffect(() => {\n    const handleResize = () => {\n      setIsMobile(window.innerWidth <= 768);\n    };\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, []);\n\n  // 根据当前路径设置选中的菜单项\n  useEffect(() => {\n    const path = location.pathname;\n    if (path.includes('/upload/batch')) {\n      setSelectedKey('2');\n    } else if (path.includes('/upload/single')) {\n      setSelectedKey('5');\n    } else if (path.includes('/create')) {\n      setSelectedKey('3');\n    } else if (path.includes('/assignment-list')) {\n      setSelectedKey('4');\n    } else if (path.includes('/statistics')) {\n      setSelectedKey('6');\n    } else if (path.includes('/pending-review')) {\n      setSelectedKey('7');\n    } else if (path.includes('/calendar')) {\n      setSelectedKey('8');\n    } else if (path.includes('/wrong-questions')) {\n      setSelectedKey('9');\n    } else if (path.includes('/feedback')) {\n      setSelectedKey('10');\n    } else if (path.includes('/student-assignments')) {\n      setSelectedKey('student-assignments');\n    } else if (path.match(/\\/homework\\/\\d+/) || path.match(/\\/system-homework\\/\\d+/)) {\n      // 根据查询参数决定选中哪个菜单\n      const fromParam = searchParams.get('from');\n      if (fromParam === 'today') {\n        setSelectedKey('7'); // 今日作业\n      } else if (fromParam === 'finished') {\n        setSelectedKey('1'); // 往日作业\n      } else {\n        // 如果没有from参数，默认选择往日作业（保持原有行为）\n        setSelectedKey('1');\n      }\n    } else if (path === '/homework' || path === '/system-homework') {\n      // 如果是根路径，根据用户类型选择默认菜单\n      setSelectedKey(user.is_teacher || isSystemLevel ? '7' : 'student-assignments');\n    }\n  }, [location.pathname, user.is_teacher, isSystemLevel, searchParams]);\n\n  // 处理菜单点击\n  const handleMenuClick = e => {\n    const key = e.key;\n    setSelectedKey(key);\n\n    // 根据是否为系统级别构建不同的路由前缀\n    const routePrefix = isSystemLevel ? '/system-homework' : '/homework';\n    switch (key) {\n      case '1':\n        navigate(`${routePrefix}/finished`);\n        break;\n      case '2':\n        navigate(`${routePrefix}/upload/batch`);\n        break;\n      case '3':\n        // 传递筛选参数到创建页面\n        navigate(`${routePrefix}/create`, {\n          state: {\n            filters: systemFilters,\n            isSystemLevel: isSystemLevel\n          }\n        });\n        break;\n      case '4':\n        navigate(`${routePrefix}/assignment-list`);\n        break;\n      case '5':\n        navigate(`${routePrefix}/upload/single`);\n        break;\n      case '6':\n        // 根据是否为系统级别决定跳转到不同的作业分析页面\n        if (isSystemLevel) {\n          navigate(`/system-homework-analysis`);\n        } else {\n          navigate(`/homework-analysis`);\n        }\n        break;\n      case '7':\n        navigate(`${routePrefix}/pending-review`);\n        break;\n      case '8':\n        navigate(`${routePrefix}/calendar`);\n        break;\n      case '9':\n        navigate(`${routePrefix}/wrong-questions`);\n        break;\n      case '10':\n        navigate(`${routePrefix}/feedback`);\n        break;\n      case 'student-assignments':\n        navigate(`${routePrefix}/student-assignments`);\n        break;\n      default:\n        if (user.is_teacher || isSystemLevel) {\n          navigate(`${routePrefix}/pending-review`);\n        } else {\n          navigate(`${routePrefix}/student-assignments`);\n        }\n    }\n  };\n\n  // 确保user存在\n  if (!user) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: \"\\u52A0\\u8F7D\\u4E2D...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 464,\n      columnNumber: 12\n    }, this);\n  }\n\n  // 处理筛选变化\n  const handleFilterChange = filters => {\n    setSystemFilters(filters);\n  };\n\n  // 设置页面标题和面包屑\n  const pageTitle = isSystemLevel ? '系统作业管理' : user.is_teacher ? '作业管理' : '作业提交';\n  return /*#__PURE__*/_jsxDEV(FilterContext.Provider, {\n    value: systemFilters,\n    children: /*#__PURE__*/_jsxDEV(Layout, {\n      className: \"homework-management-layout\",\n      children: [/*#__PURE__*/_jsxDEV(Breadcrumb, {\n        style: {\n          margin: '16px 0'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Breadcrumb.Item, {\n          children: \"\\u9996\\u9875\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 479,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Breadcrumb.Item, {\n          children: pageTitle\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 480,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 478,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Title, {\n        level: 2,\n        children: pageTitle\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 483,\n        columnNumber: 9\n      }, this), isSystemLevel && /*#__PURE__*/_jsxDEV(SystemHomeworkFilter, {\n        user: user,\n        onFilterChange: handleFilterChange\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 489,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Layout, {\n        className: isSystemLevel ? \"system-homework-management-page\" : \"homework-management-page\",\n        style: {\n          background: '#fff',\n          padding: isMobile ? '0' : '24px 0'\n        },\n        children: [isMobile && (user.is_teacher || isSystemLevel) && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mobile-top-nav\",\n          children: /*#__PURE__*/_jsxDEV(Tabs, {\n            activeKey: selectedKey,\n            onChange: key => handleMenuClick({\n              key\n            }),\n            type: \"line\",\n            size: \"small\",\n            items: [{\n              key: '7',\n              label: '今日作业',\n              icon: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 510,\n                columnNumber: 25\n              }, this)\n            }, {\n              key: '3',\n              label: '添加任务',\n              icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 515,\n                columnNumber: 25\n              }, this)\n            }, {\n              key: '4',\n              label: '作业任务',\n              icon: /*#__PURE__*/_jsxDEV(AppstoreOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 520,\n                columnNumber: 25\n              }, this)\n            }, {\n              key: '5',\n              label: '单个上传',\n              icon: /*#__PURE__*/_jsxDEV(CloudUploadOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 525,\n                columnNumber: 25\n              }, this)\n            }, {\n              key: '2',\n              label: '批量上传',\n              icon: /*#__PURE__*/_jsxDEV(UploadOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 530,\n                columnNumber: 25\n              }, this)\n            }, {\n              key: '1',\n              label: '往日作业',\n              icon: /*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 535,\n                columnNumber: 25\n              }, this)\n            }, {\n              key: '6',\n              label: '作业分析',\n              icon: /*#__PURE__*/_jsxDEV(BarChartOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 540,\n                columnNumber: 25\n              }, this)\n            }, {\n              key: '8',\n              label: '作业日历',\n              icon: /*#__PURE__*/_jsxDEV(CalendarOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 545,\n                columnNumber: 25\n              }, this)\n            }, {\n              key: '9',\n              label: '错题收集',\n              icon: /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 550,\n                columnNumber: 25\n              }, this)\n            }]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 501,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 500,\n          columnNumber: 11\n        }, this), !isMobile && (user.is_teacher || isSystemLevel) && /*#__PURE__*/_jsxDEV(Sider, {\n          width: 200,\n          style: {\n            background: '#fff'\n          },\n          children: /*#__PURE__*/_jsxDEV(Menu, {\n            mode: \"inline\",\n            selectedKeys: [selectedKey],\n            style: {\n              height: '100%'\n            },\n            onClick: handleMenuClick,\n            children: [/*#__PURE__*/_jsxDEV(ItemGroup, {\n              title: \"\\u4F5C\\u4E1A\\u521B\\u5EFA\",\n              children: [/*#__PURE__*/_jsxDEV(Menu.Item, {\n                icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 567,\n                  columnNumber: 42\n                }, this),\n                children: \"\\u6DFB\\u52A0\\u4F5C\\u4E1A\\u4EFB\\u52A1\"\n              }, \"3\", false, {\n                fileName: _jsxFileName,\n                lineNumber: 567,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Menu.Item, {\n                icon: /*#__PURE__*/_jsxDEV(AppstoreOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 568,\n                  columnNumber: 42\n                }, this),\n                children: \"\\u4F5C\\u4E1A\\u4EFB\\u52A1\"\n              }, \"4\", false, {\n                fileName: _jsxFileName,\n                lineNumber: 568,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 566,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ItemGroup, {\n              title: isSystemLevel ? \"系统作业管理\" : \"作业管理\",\n              children: [/*#__PURE__*/_jsxDEV(Menu.Item, {\n                icon: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 572,\n                  columnNumber: 42\n                }, this),\n                children: \"\\u4ECA\\u65E5\\u4F5C\\u4E1A\"\n              }, \"7\", false, {\n                fileName: _jsxFileName,\n                lineNumber: 572,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Menu.Item, {\n                icon: /*#__PURE__*/_jsxDEV(CloudUploadOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 573,\n                  columnNumber: 42\n                }, this),\n                children: \"\\u5355\\u4E2A\\u4E0A\\u4F20\"\n              }, \"5\", false, {\n                fileName: _jsxFileName,\n                lineNumber: 573,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Menu.Item, {\n                icon: /*#__PURE__*/_jsxDEV(UploadOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 574,\n                  columnNumber: 42\n                }, this),\n                children: \"\\u6279\\u91CF\\u4E0A\\u4F20\"\n              }, \"2\", false, {\n                fileName: _jsxFileName,\n                lineNumber: 574,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Menu.Item, {\n                icon: /*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 575,\n                  columnNumber: 42\n                }, this),\n                children: \"\\u5F80\\u65E5\\u4F5C\\u4E1A\"\n              }, \"1\", false, {\n                fileName: _jsxFileName,\n                lineNumber: 575,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 571,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ItemGroup, {\n              title: \"\\u6570\\u636E\\u5206\\u6790\",\n              children: [/*#__PURE__*/_jsxDEV(Menu.Item, {\n                icon: /*#__PURE__*/_jsxDEV(BarChartOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 579,\n                  columnNumber: 42\n                }, this),\n                children: \"\\u4F5C\\u4E1A\\u5206\\u6790\"\n              }, \"6\", false, {\n                fileName: _jsxFileName,\n                lineNumber: 579,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Menu.Item, {\n                icon: /*#__PURE__*/_jsxDEV(CalendarOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 580,\n                  columnNumber: 42\n                }, this),\n                children: \"\\u4F5C\\u4E1A\\u65E5\\u5386\"\n              }, \"8\", false, {\n                fileName: _jsxFileName,\n                lineNumber: 580,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Menu.Item, {\n                icon: /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 581,\n                  columnNumber: 42\n                }, this),\n                children: \"\\u9519\\u9898\\u6536\\u96C6\"\n              }, \"9\", false, {\n                fileName: _jsxFileName,\n                lineNumber: 581,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 578,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 560,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 559,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Content, {\n          className: isMobile ? \"mobile-content\" : \"\",\n          style: {\n            padding: user.is_teacher || isSystemLevel ? isMobile ? '0' : '0 24px' : '0',\n            minHeight: 280\n          },\n          children: /*#__PURE__*/_jsxDEV(Routes, {\n            children: [/*#__PURE__*/_jsxDEV(Route, {\n              index: true,\n              element: user.is_teacher || isSystemLevel ? /*#__PURE__*/_jsxDEV(PendingReviewHomeworks, {\n                user: user,\n                isSystemLevel: isSystemLevel\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 597,\n                columnNumber: 17\n              }, this) : /*#__PURE__*/_jsxDEV(StudentHomeworkAssignmentList, {\n                user: user\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 598,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 595,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"upload/batch\",\n              element: /*#__PURE__*/_jsxDEV(HomeworkUpload, {\n                user: user,\n                batchMode: true,\n                isSystemLevel: isSystemLevel\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 600,\n                columnNumber: 49\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 600,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"upload/single\",\n              element: /*#__PURE__*/_jsxDEV(StudentHomeworkUpload, {\n                user: user,\n                isSystemLevel: isSystemLevel\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 601,\n                columnNumber: 50\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 601,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"submit\",\n              element: /*#__PURE__*/_jsxDEV(StudentSubmitChoice, {\n                user: user\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 602,\n                columnNumber: 43\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 602,\n              columnNumber: 13\n            }, this), !user.is_teacher && !isSystemLevel && /*#__PURE__*/_jsxDEV(Route, {\n              path: \"history\",\n              element: /*#__PURE__*/_jsxDEV(StudentHomeworkHistory, {\n                user: user\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 605,\n                columnNumber: 46\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 605,\n              columnNumber: 15\n            }, this), !user.is_teacher && !isSystemLevel && /*#__PURE__*/_jsxDEV(Route, {\n              path: \"review\",\n              element: /*#__PURE__*/_jsxDEV(StudentHomeworkReview, {\n                user: user\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 609,\n                columnNumber: 45\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 609,\n              columnNumber: 15\n            }, this), !user.is_teacher && !isSystemLevel && /*#__PURE__*/_jsxDEV(Route, {\n              path: \"detail/:homeworkId\",\n              element: /*#__PURE__*/_jsxDEV(StudentHomeworkDetail, {\n                user: user\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 613,\n                columnNumber: 57\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 613,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \":homeworkId\",\n              element: /*#__PURE__*/_jsxDEV(HomeworkDetail, {\n                user: user,\n                isSystemLevel: isSystemLevel\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 615,\n                columnNumber: 48\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 615,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"create\",\n              element: /*#__PURE__*/_jsxDEV(HomeworkAssignmentCreate, {\n                user: user,\n                isSystemLevel: isSystemLevel\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 616,\n                columnNumber: 43\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 616,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"assignment-list\",\n              element: /*#__PURE__*/_jsxDEV(HomeworkAssignmentList, {\n                user: user,\n                isSystemLevel: isSystemLevel\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 617,\n                columnNumber: 52\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 617,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"student-assignments\",\n              element: /*#__PURE__*/_jsxDEV(StudentHomeworkAssignmentList, {\n                user: user\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 618,\n                columnNumber: 56\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 618,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"assignment-detail/:assignmentId\",\n              element: /*#__PURE__*/_jsxDEV(HomeworkAssignmentDetail, {\n                user: user,\n                isSystemLevel: isSystemLevel\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 619,\n                columnNumber: 68\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 619,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"assignment-progress/:assignmentId\",\n              element: /*#__PURE__*/_jsxDEV(HomeworkAssignmentProgress, {\n                user: user,\n                isSystemLevel: isSystemLevel\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 620,\n                columnNumber: 70\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 620,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"upload-options\",\n              element: /*#__PURE__*/_jsxDEV(HomeworkUploadOptions, {\n                user: user,\n                isSystemLevel: isSystemLevel\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 621,\n                columnNumber: 51\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 621,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"statistics\",\n              element: /*#__PURE__*/_jsxDEV(HomeworkStatistics, {\n                user: user,\n                isSystemLevel: isSystemLevel\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 622,\n                columnNumber: 47\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 622,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"pending-review\",\n              element: /*#__PURE__*/_jsxDEV(PendingReviewHomeworks, {\n                user: user,\n                isSystemLevel: isSystemLevel\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 623,\n                columnNumber: 51\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 623,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"finished\",\n              element: /*#__PURE__*/_jsxDEV(FinishedHomeworks, {\n                user: user,\n                isSystemLevel: isSystemLevel\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 624,\n                columnNumber: 45\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 624,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"history\",\n              element: /*#__PURE__*/_jsxDEV(HomeworkDetail, {\n                user: user,\n                showHistory: true,\n                isSystemLevel: isSystemLevel\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 625,\n                columnNumber: 44\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 625,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"calendar\",\n              element: /*#__PURE__*/_jsxDEV(HomeworkCalendar, {\n                user: user,\n                isSystemLevel: isSystemLevel\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 626,\n                columnNumber: 45\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 626,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"wrong-questions\",\n              element: /*#__PURE__*/_jsxDEV(\"div\", {\n                children: \"\\u9519\\u9898\\u6536\\u96C6\\u529F\\u80FD\\u6B63\\u5728\\u5F00\\u53D1\\u4E2D...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 627,\n                columnNumber: 52\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 627,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"feedback\",\n              element: /*#__PURE__*/_jsxDEV(\"div\", {\n                children: \"\\u4F5C\\u4E1A\\u53CD\\u9988\\u529F\\u80FD\\u6B63\\u5728\\u5F00\\u53D1\\u4E2D...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 628,\n                columnNumber: 45\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 628,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 594,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 587,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 497,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 477,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 476,\n    columnNumber: 5\n  }, this);\n};\n_s2(HomeworkManagement, \"VFnpHTzkxcfmhWE7bcK5I8lSYpo=\", false, function () {\n  return [useNavigate, useLocation, useSearchParams];\n});\n_c2 = HomeworkManagement;\nexport default HomeworkManagement;\nvar _c, _c2;\n$RefreshReg$(_c, \"SystemHomeworkFilter\");\n$RefreshReg$(_c2, \"HomeworkManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "createContext", "useNavigate", "useLocation", "useSearchParams", "Routes", "Route", "Layout", "<PERSON><PERSON>", "Breadcrumb", "Typography", "Card", "Row", "Col", "Select", "Space", "<PERSON><PERSON>", "message", "Tabs", "UploadOutlined", "FileTextOutlined", "PlusOutlined", "AppstoreOutlined", "CloudUploadOutlined", "BarChartOutlined", "CheckCircleOutlined", "CalendarOutlined", "ExclamationCircleOutlined", "FilterOutlined", "ReloadOutlined", "HomeworkUpload", "HomeworkDetail", "HomeworkAssignmentCreate", "HomeworkAssignmentList", "StudentHomeworkUpload", "HomeworkAssignmentDetail", "HomeworkUploadOptions", "HomeworkStatistics", "HomeworkAssignmentProgress", "PendingReviewHomeworks", "HomeworkCalendar", "FinishedHomeworks", "StudentHomeworkAssignmentList", "StudentSubmitChoice", "StudentHomeworkHistory", "StudentHomeworkReview", "StudentHomeworkDetail", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "Content", "Title", "ItemGroup", "Option", "FilterContext", "SystemHomeworkFilter", "user", "onFilterChange", "_s", "schools", "setSchools", "grades", "setGrades", "loading", "setLoading", "filters", "setFilters", "schoolId", "gradeId", "isSuperAdmin", "is_admin", "role", "fetchSchools", "response", "fetch", "headers", "localStorage", "getItem", "ok", "data", "json", "defaultSchool", "find", "school", "id", "defaultFilters", "gradeName", "fetchGrades", "validSchools", "filter", "undefined", "name", "shuangliuSchool", "includes", "length", "error", "console", "sortGrades", "gradeOrder", "sort", "a", "b", "orderA", "orderB", "gradeOptions", "map", "index", "sortedGrades", "defaultGrades", "school_id", "handleSchoolChange", "value", "newFilters", "handleGradeChange", "selected<PERSON><PERSON>", "g", "handleReset", "defaultSchoolId", "resetFilters", "title", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "style", "marginBottom", "gutter", "span", "placeholder", "width", "onChange", "allowClear", "disabled", "grade", "icon", "onClick", "_c", "HomeworkManagement", "onLogout", "isSystemLevel", "_s2", "navigate", "location", "searchParams", "<PERSON><PERSON><PERSON>", "setSelectedKey", "is_teacher", "systemFilters", "setSystemFilters", "classId", "isMobile", "setIsMobile", "window", "innerWidth", "handleResize", "addEventListener", "removeEventListener", "path", "pathname", "match", "fromParam", "get", "handleMenuClick", "e", "key", "routePrefix", "state", "handleFilterChange", "pageTitle", "Provider", "className", "margin", "<PERSON><PERSON>", "level", "background", "padding", "active<PERSON><PERSON>", "type", "items", "label", "mode", "<PERSON><PERSON><PERSON><PERSON>", "height", "minHeight", "element", "batchMode", "showHistory", "_c2", "$RefreshReg$"], "sources": ["D:/pythonproject/checkingsys/frontend/src/pages/HomeworkManagement.js"], "sourcesContent": ["import React, { useState, useEffect, createContext } from 'react';\r\nimport { useNavigate, useLocation, useSearchParams, Routes, Route } from 'react-router-dom';\r\nimport {\r\n  Layout, Menu, Breadcrumb, Typography, Card, Row, Col, Select, Space, Button, message, Tabs\r\n} from 'antd';\r\nimport {\r\n  UploadOutlined, FileTextOutlined, PlusOutlined, AppstoreOutlined, CloudUploadOutlined,\r\n  BarChartOutlined, CheckCircleOutlined, CalendarOutlined, ExclamationCircleOutlined,\r\n  FilterOutlined, ReloadOutlined\r\n} from '@ant-design/icons';\r\nimport HomeworkUpload from '../components/HomeworkUpload';\r\nimport HomeworkDetail from '../components/HomeworkDetail';\r\nimport HomeworkAssignmentCreate from '../components/HomeworkAssignmentCreate';\r\nimport HomeworkAssignmentList from '../components/HomeworkAssignmentList';\r\nimport StudentHomeworkUpload from '../components/StudentHomeworkUpload';\r\nimport HomeworkAssignmentDetail from '../components/HomeworkAssignmentDetail';\r\nimport HomeworkUploadOptions from '../components/HomeworkUploadOptions';\r\nimport HomeworkStatistics from '../components/HomeworkStatistics';\r\nimport HomeworkAssignmentProgress from '../components/HomeworkAssignmentProgress';\r\nimport PendingReviewHomeworks from '../components/PendingReviewHomeworks';\r\nimport HomeworkCalendar from '../components/HomeworkCalendar';\r\nimport FinishedHomeworks from '../components/FinishedHomeworks';\r\nimport StudentHomeworkAssignmentList from '../components/StudentHomeworkAssignmentList';\r\nimport StudentSubmitChoice from '../components/StudentSubmitChoice';\r\nimport StudentHomeworkHistory from '../components/student/StudentHomeworkHistory';\r\nimport StudentHomeworkReview from '../components/student/StudentHomeworkReview';\r\nimport StudentHomeworkDetail from '../components/student/StudentHomeworkDetail';\r\n\r\nconst { Sider, Content } = Layout;\r\nconst { Title } = Typography;\r\nconst { ItemGroup } = Menu;\r\nconst { Option } = Select;\r\n\r\n// 创建筛选上下文\r\nconst FilterContext = createContext();\r\n\r\n// 筛选组件\r\nconst SystemHomeworkFilter = ({ user, onFilterChange }) => {\r\n  const [schools, setSchools] = useState([]);\r\n  const [grades, setGrades] = useState([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [filters, setFilters] = useState({\r\n    schoolId: null,\r\n    gradeId: null\r\n  });\r\n\r\n  // 是否为超级管理员\r\n  const isSuperAdmin = user.is_admin && user.role === '超级管理员';\r\n\r\n\r\n\r\n  // 获取学校列表\r\n  const fetchSchools = async () => {\r\n    try {\r\n      setLoading(true);\r\n      const response = await fetch('/api/admin/schools', {\r\n        headers: {\r\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\r\n        }\r\n      });\r\n      if (response.ok) {\r\n        const data = await response.json();\r\n        setSchools(data);\r\n\r\n        // 设置默认学校为四川省双流中学（ID=1）\r\n        const defaultSchool = data.find(school => school.id === 1);\r\n\r\n        if (defaultSchool) {\r\n          const defaultFilters = {\r\n            schoolId: 1,\r\n            gradeId: null,\r\n            gradeName: null\r\n          };\r\n          setFilters(defaultFilters);\r\n          fetchGrades(1);\r\n          onFilterChange(defaultFilters);\r\n        } else {\r\n          // 过滤掉ID无效的学校\r\n          const validSchools = data.filter(school =>\r\n            school.id !== undefined &&\r\n            school.id !== null &&\r\n            school.name\r\n          );\r\n\r\n          // 查找四川省双流中学\r\n          const shuangliuSchool = validSchools.find(school =>\r\n            school.name.includes('双流中学')\r\n          );\r\n\r\n          let defaultSchool = null;\r\n          if (shuangliuSchool) {\r\n            defaultSchool = shuangliuSchool;\r\n          } else if (validSchools.length > 0) {\r\n            defaultSchool = validSchools[0];\r\n          }\r\n\r\n          if (defaultSchool) {\r\n            const defaultFilters = {\r\n              schoolId: defaultSchool.id,\r\n              gradeId: null,\r\n              gradeName: null\r\n            };\r\n            setFilters(defaultFilters);\r\n            fetchGrades(defaultSchool.id);\r\n            onFilterChange(defaultFilters);\r\n          }\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error('获取学校列表失败:', error);\r\n      message.error('获取学校列表失败');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // 年级排序函数\r\n  const sortGrades = (grades) => {\r\n    const gradeOrder = {\r\n      // 小学\r\n      '小学一年级': 1, '小学二年级': 2, '小学三年级': 3,\r\n      '小学四年级': 4, '小学五年级': 5, '小学六年级': 6,\r\n      // 初中\r\n      '初中一年级': 7, '初中二年级': 8, '初中三年级': 9,\r\n      '初一': 7, '初二': 8, '初三': 9,\r\n      // 高中\r\n      '高中一年级': 10, '高中二年级': 11, '高中三年级': 12,\r\n      '高一': 10, '高二': 11, '高三': 12,\r\n      // 其他常见格式\r\n      '一年级': 1, '二年级': 2, '三年级': 3, '四年级': 4, '五年级': 5, '六年级': 6,\r\n      '七年级': 7, '八年级': 8, '九年级': 9,\r\n      '十年级': 10, '十一年级': 11, '十二年级': 12\r\n    };\r\n\r\n    return grades.sort((a, b) => {\r\n      const orderA = gradeOrder[a.name] || 999;\r\n      const orderB = gradeOrder[b.name] || 999;\r\n      return orderA - orderB;\r\n    });\r\n  };\r\n\r\n  // 获取年级列表\r\n  const fetchGrades = async (schoolId) => {\r\n    if (!schoolId) {\r\n      setGrades([]);\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const response = await fetch(`/api/admin/classes/grades?school_id=${schoolId}`, {\r\n        headers: {\r\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\r\n        }\r\n      });\r\n      if (response.ok) {\r\n        const data = await response.json();\r\n        // 将年级名称转换为带ID的对象\r\n        const gradeOptions = data.map((gradeName, index) => ({\r\n          id: index + 1,\r\n          name: gradeName\r\n        }));\r\n        // 对年级进行排序\r\n        const sortedGrades = sortGrades(gradeOptions);\r\n        setGrades(sortedGrades);\r\n      } else {\r\n        setGrades([]);\r\n      }\r\n    } catch (error) {\r\n      console.error('获取年级列表失败:', error);\r\n      message.error('获取年级列表失败');\r\n      // 设置默认年级列表\r\n      const defaultGrades = [\r\n        { id: 1, name: '小学一年级' },\r\n        { id: 2, name: '小学二年级' },\r\n        { id: 3, name: '小学三年级' },\r\n        { id: 4, name: '小学四年级' },\r\n        { id: 5, name: '小学五年级' },\r\n        { id: 6, name: '小学六年级' },\r\n        { id: 7, name: '初中一年级' },\r\n        { id: 8, name: '初中二年级' },\r\n        { id: 9, name: '初中三年级' },\r\n        { id: 10, name: '高中一年级' },\r\n        { id: 11, name: '高中二年级' },\r\n        { id: 12, name: '高中三年级' }\r\n      ];\r\n      setGrades(defaultGrades);\r\n    }\r\n  };\r\n\r\n\r\n\r\n  // 初始化数据\r\n  useEffect(() => {\r\n    if (isSuperAdmin) {\r\n      fetchSchools(); // fetchSchools内部会设置默认学校\r\n    } else {\r\n      // 学校管理员直接获取年级\r\n      if (user.school_id) {\r\n        fetchGrades(user.school_id);\r\n        const defaultFilters = {\r\n          schoolId: user.school_id,\r\n          gradeId: null,\r\n          gradeName: null\r\n        };\r\n        setFilters(defaultFilters);\r\n        onFilterChange(defaultFilters);\r\n      }\r\n    }\r\n  // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [user, isSuperAdmin]);\r\n\r\n  // 处理学校变化\r\n  const handleSchoolChange = (value) => {\r\n    const newFilters = {\r\n      schoolId: value,\r\n      gradeId: null,\r\n      gradeName: null\r\n    };\r\n    setFilters(newFilters);\r\n    setGrades([]);\r\n\r\n    if (value) {\r\n      fetchGrades(value);\r\n    }\r\n\r\n    onFilterChange(newFilters);\r\n  };\r\n\r\n  // 处理年级变化\r\n  const handleGradeChange = (value) => {\r\n    const selectedGrade = grades.find(g => g.id === value);\r\n    const newFilters = {\r\n      ...filters,\r\n      gradeId: value,\r\n      gradeName: selectedGrade?.name\r\n    };\r\n    setFilters(newFilters);\r\n\r\n    onFilterChange(newFilters);\r\n  };\r\n\r\n  // 重置筛选\r\n  const handleReset = () => {\r\n    let defaultSchoolId = user.school_id;\r\n\r\n    if (isSuperAdmin) {\r\n      // 超级管理员：使用四川省双流中学（ID=1）作为默认\r\n      const defaultSchool = schools.find(school => school.id === 1);\r\n      defaultSchoolId = defaultSchool ? 1 : (schools.length > 0 ? schools[0].id : null);\r\n    }\r\n\r\n    const resetFilters = {\r\n      schoolId: defaultSchoolId,\r\n      gradeId: null,\r\n      gradeName: null\r\n    };\r\n    setFilters(resetFilters);\r\n    setGrades([]);\r\n\r\n    // 重新获取年级数据\r\n    if (defaultSchoolId) {\r\n      fetchGrades(defaultSchoolId);\r\n    }\r\n\r\n    onFilterChange(resetFilters);\r\n  };\r\n\r\n  return (\r\n    <Card\r\n      title={\r\n        <Space>\r\n          <FilterOutlined />\r\n          筛选条件\r\n        </Space>\r\n      }\r\n      size=\"small\"\r\n      style={{ marginBottom: 16 }}\r\n    >\r\n      <Row gutter={16}>\r\n        {/* 超级管理员显示学校筛选 */}\r\n        {isSuperAdmin && (\r\n          <Col span={6}>\r\n            <Select\r\n              placeholder=\"选择学校\"\r\n              style={{ width: '100%' }}\r\n              value={filters.schoolId}\r\n              onChange={handleSchoolChange}\r\n              loading={loading}\r\n              allowClear\r\n            >\r\n              {schools\r\n                .filter(school =>\r\n                  school.id !== undefined &&\r\n                  school.id !== null &&\r\n                  school.name\r\n                )\r\n                .map(school => (\r\n                  <Option key={school.id} value={school.id}>\r\n                    {school.name}\r\n                  </Option>\r\n                ))\r\n              }\r\n            </Select>\r\n          </Col>\r\n        )}\r\n\r\n        <Col span={isSuperAdmin ? 8 : 12}>\r\n          <Select\r\n            placeholder=\"选择年级\"\r\n            style={{ width: '100%' }}\r\n            value={filters.gradeId}\r\n            onChange={handleGradeChange}\r\n            disabled={isSuperAdmin && !filters.schoolId}\r\n            allowClear\r\n          >\r\n            {grades.map(grade => (\r\n              <Option key={grade.id} value={grade.id}>\r\n                {grade.name}\r\n              </Option>\r\n            ))}\r\n          </Select>\r\n        </Col>\r\n\r\n        <Col span={isSuperAdmin ? 8 : 12}>\r\n          <Button\r\n            icon={<ReloadOutlined />}\r\n            onClick={handleReset}\r\n          >\r\n            重置\r\n          </Button>\r\n        </Col>\r\n      </Row>\r\n    </Card>\r\n  );\r\n};\r\n\r\nconst HomeworkManagement = ({ user, onLogout, isSystemLevel = false }) => {\r\n  const navigate = useNavigate();\r\n  const location = useLocation();\r\n  const [searchParams] = useSearchParams();\r\n  const [selectedKey, setSelectedKey] = useState(user.is_teacher || isSystemLevel ? '7' : 'student-assignments');\r\n  const [systemFilters, setSystemFilters] = useState({\r\n    schoolId: null,\r\n    gradeId: null,\r\n    classId: null\r\n  });\r\n\r\n  // 移动端检测\r\n  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);\r\n\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      setIsMobile(window.innerWidth <= 768);\r\n    };\r\n\r\n    window.addEventListener('resize', handleResize);\r\n    return () => window.removeEventListener('resize', handleResize);\r\n  }, []);\r\n  \r\n  // 根据当前路径设置选中的菜单项\r\n  useEffect(() => {\r\n    const path = location.pathname;\r\n    if (path.includes('/upload/batch')) {\r\n      setSelectedKey('2');\r\n    } else if (path.includes('/upload/single')) {\r\n      setSelectedKey('5');\r\n    } else if (path.includes('/create')) {\r\n      setSelectedKey('3');\r\n    } else if (path.includes('/assignment-list')) {\r\n      setSelectedKey('4');\r\n    } else if (path.includes('/statistics')) {\r\n      setSelectedKey('6');\r\n    } else if (path.includes('/pending-review')) {\r\n      setSelectedKey('7');\r\n    } else if (path.includes('/calendar')) {\r\n      setSelectedKey('8');\r\n    } else if (path.includes('/wrong-questions')) {\r\n      setSelectedKey('9');\r\n    } else if (path.includes('/feedback')) {\r\n      setSelectedKey('10');\r\n    } else if (path.includes('/student-assignments')) {\r\n      setSelectedKey('student-assignments');\r\n    } else if (path.match(/\\/homework\\/\\d+/) || path.match(/\\/system-homework\\/\\d+/)) {\r\n      // 根据查询参数决定选中哪个菜单\r\n      const fromParam = searchParams.get('from');\r\n      if (fromParam === 'today') {\r\n        setSelectedKey('7'); // 今日作业\r\n      } else if (fromParam === 'finished') {\r\n        setSelectedKey('1'); // 往日作业\r\n      } else {\r\n        // 如果没有from参数，默认选择往日作业（保持原有行为）\r\n        setSelectedKey('1');\r\n      }\r\n    } else if (path === '/homework' || path === '/system-homework') {\r\n      // 如果是根路径，根据用户类型选择默认菜单\r\n      setSelectedKey(user.is_teacher || isSystemLevel ? '7' : 'student-assignments');\r\n    }\r\n  }, [location.pathname, user.is_teacher, isSystemLevel, searchParams]);\r\n  \r\n  // 处理菜单点击\r\n  const handleMenuClick = (e) => {\r\n    const key = e.key;\r\n    setSelectedKey(key);\r\n\r\n    // 根据是否为系统级别构建不同的路由前缀\r\n    const routePrefix = isSystemLevel ? '/system-homework' : '/homework';\r\n\r\n    switch (key) {\r\n      case '1':\r\n        navigate(`${routePrefix}/finished`);\r\n        break;\r\n      case '2':\r\n        navigate(`${routePrefix}/upload/batch`);\r\n        break;\r\n      case '3':\r\n        // 传递筛选参数到创建页面\r\n        navigate(`${routePrefix}/create`, {\r\n          state: {\r\n            filters: systemFilters,\r\n            isSystemLevel: isSystemLevel\r\n          }\r\n        });\r\n        break;\r\n      case '4':\r\n        navigate(`${routePrefix}/assignment-list`);\r\n        break;\r\n      case '5':\r\n        navigate(`${routePrefix}/upload/single`);\r\n        break;\r\n      case '6':\r\n        // 根据是否为系统级别决定跳转到不同的作业分析页面\r\n        if (isSystemLevel) {\r\n          navigate(`/system-homework-analysis`);\r\n        } else {\r\n          navigate(`/homework-analysis`);\r\n        }\r\n        break;\r\n      case '7':\r\n        navigate(`${routePrefix}/pending-review`);\r\n        break;\r\n      case '8':\r\n        navigate(`${routePrefix}/calendar`);\r\n        break;\r\n      case '9':\r\n        navigate(`${routePrefix}/wrong-questions`);\r\n        break;\r\n      case '10':\r\n        navigate(`${routePrefix}/feedback`);\r\n        break;\r\n      case 'student-assignments':\r\n        navigate(`${routePrefix}/student-assignments`);\r\n        break;\r\n      default:\r\n        if (user.is_teacher || isSystemLevel) {\r\n          navigate(`${routePrefix}/pending-review`);\r\n        } else {\r\n          navigate(`${routePrefix}/student-assignments`);\r\n        }\r\n    }\r\n  };\r\n\r\n  // 确保user存在\r\n  if (!user) {\r\n    return <div>加载中...</div>;\r\n  }\r\n\r\n  // 处理筛选变化\r\n  const handleFilterChange = (filters) => {\r\n    setSystemFilters(filters);\r\n  };\r\n\r\n  // 设置页面标题和面包屑\r\n  const pageTitle = isSystemLevel ? '系统作业管理' : (user.is_teacher ? '作业管理' : '作业提交');\r\n\r\n  return (\r\n    <FilterContext.Provider value={systemFilters}>\r\n      <Layout className=\"homework-management-layout\">\r\n        <Breadcrumb style={{ margin: '16px 0' }}>\r\n          <Breadcrumb.Item>首页</Breadcrumb.Item>\r\n          <Breadcrumb.Item>{pageTitle}</Breadcrumb.Item>\r\n        </Breadcrumb>\r\n\r\n        <Title level={2}>\r\n          {pageTitle}\r\n        </Title>\r\n\r\n        {/* 系统级别显示筛选组件 */}\r\n        {isSystemLevel && (\r\n          <SystemHomeworkFilter\r\n            user={user}\r\n            onFilterChange={handleFilterChange}\r\n          />\r\n        )}\r\n\r\n\r\n\r\n        <Layout className={isSystemLevel ? \"system-homework-management-page\" : \"homework-management-page\"} style={{ background: '#fff', padding: isMobile ? '0' : '24px 0' }}>\r\n        {/* 移动端顶部导航 */}\r\n        {isMobile && (user.is_teacher || isSystemLevel) && (\r\n          <div className=\"mobile-top-nav\">\r\n            <Tabs\r\n              activeKey={selectedKey}\r\n              onChange={(key) => handleMenuClick({ key })}\r\n              type=\"line\"\r\n              size=\"small\"\r\n              items={[\r\n                {\r\n                  key: '7',\r\n                  label: '今日作业',\r\n                  icon: <CheckCircleOutlined />\r\n                },\r\n                {\r\n                  key: '3',\r\n                  label: '添加任务',\r\n                  icon: <PlusOutlined />\r\n                },\r\n                {\r\n                  key: '4',\r\n                  label: '作业任务',\r\n                  icon: <AppstoreOutlined />\r\n                },\r\n                {\r\n                  key: '5',\r\n                  label: '单个上传',\r\n                  icon: <CloudUploadOutlined />\r\n                },\r\n                {\r\n                  key: '2',\r\n                  label: '批量上传',\r\n                  icon: <UploadOutlined />\r\n                },\r\n                {\r\n                  key: '1',\r\n                  label: '往日作业',\r\n                  icon: <FileTextOutlined />\r\n                },\r\n                {\r\n                  key: '6',\r\n                  label: '作业分析',\r\n                  icon: <BarChartOutlined />\r\n                },\r\n                {\r\n                  key: '8',\r\n                  label: '作业日历',\r\n                  icon: <CalendarOutlined />\r\n                },\r\n                {\r\n                  key: '9',\r\n                  label: '错题收集',\r\n                  icon: <ExclamationCircleOutlined />\r\n                }\r\n              ]}\r\n            />\r\n          </div>\r\n        )}\r\n\r\n        {/* 桌面端侧边菜单 */}\r\n        {!isMobile && (user.is_teacher || isSystemLevel) && (\r\n          <Sider width={200} style={{ background: '#fff' }}>\r\n            <Menu\r\n              mode=\"inline\"\r\n              selectedKeys={[selectedKey]}\r\n              style={{ height: '100%' }}\r\n              onClick={handleMenuClick}\r\n            >\r\n              <ItemGroup title=\"作业创建\">\r\n                <Menu.Item key=\"3\" icon={<PlusOutlined />}>添加作业任务</Menu.Item>\r\n                <Menu.Item key=\"4\" icon={<AppstoreOutlined />}>作业任务</Menu.Item>\r\n              </ItemGroup>\r\n\r\n              <ItemGroup title={isSystemLevel ? \"系统作业管理\" : \"作业管理\"}>\r\n                <Menu.Item key=\"7\" icon={<CheckCircleOutlined />}>今日作业</Menu.Item>\r\n                <Menu.Item key=\"5\" icon={<CloudUploadOutlined />}>单个上传</Menu.Item>\r\n                <Menu.Item key=\"2\" icon={<UploadOutlined />}>批量上传</Menu.Item>\r\n                <Menu.Item key=\"1\" icon={<FileTextOutlined />}>往日作业</Menu.Item>\r\n              </ItemGroup>\r\n\r\n              <ItemGroup title=\"数据分析\">\r\n                <Menu.Item key=\"6\" icon={<BarChartOutlined />}>作业分析</Menu.Item>\r\n                <Menu.Item key=\"8\" icon={<CalendarOutlined />}>作业日历</Menu.Item>\r\n                <Menu.Item key=\"9\" icon={<ExclamationCircleOutlined />}>错题收集</Menu.Item>\r\n              </ItemGroup>\r\n            </Menu>\r\n          </Sider>\r\n        )}\r\n\r\n        <Content\r\n          className={isMobile ? \"mobile-content\" : \"\"}\r\n          style={{\r\n            padding: (user.is_teacher || isSystemLevel) ? (isMobile ? '0' : '0 24px') : '0',\r\n            minHeight: 280\r\n          }}\r\n        >\r\n          <Routes>\r\n            <Route index element={\r\n              user.is_teacher || isSystemLevel ?\r\n                <PendingReviewHomeworks user={user} isSystemLevel={isSystemLevel} /> :\r\n                <StudentHomeworkAssignmentList user={user} />\r\n            } />\r\n            <Route path=\"upload/batch\" element={<HomeworkUpload user={user} batchMode={true} isSystemLevel={isSystemLevel} />} />\r\n            <Route path=\"upload/single\" element={<StudentHomeworkUpload user={user} isSystemLevel={isSystemLevel} />} />\r\n            <Route path=\"submit\" element={<StudentSubmitChoice user={user} />} />\r\n            {/* 学生专用的往日作业页面 */}\r\n            {!user.is_teacher && !isSystemLevel && (\r\n              <Route path=\"history\" element={<StudentHomeworkHistory user={user} />} />\r\n            )}\r\n            {/* 学生专用的作业点评页面 */}\r\n            {!user.is_teacher && !isSystemLevel && (\r\n              <Route path=\"review\" element={<StudentHomeworkReview user={user} />} />\r\n            )}\r\n            {/* 学生专用的作业详情页面 */}\r\n            {!user.is_teacher && !isSystemLevel && (\r\n              <Route path=\"detail/:homeworkId\" element={<StudentHomeworkDetail user={user} />} />\r\n            )}\r\n            <Route path=\":homeworkId\" element={<HomeworkDetail user={user} isSystemLevel={isSystemLevel} />} />\r\n            <Route path=\"create\" element={<HomeworkAssignmentCreate user={user} isSystemLevel={isSystemLevel} />} />\r\n            <Route path=\"assignment-list\" element={<HomeworkAssignmentList user={user} isSystemLevel={isSystemLevel} />} />\r\n            <Route path=\"student-assignments\" element={<StudentHomeworkAssignmentList user={user} />} />\r\n            <Route path=\"assignment-detail/:assignmentId\" element={<HomeworkAssignmentDetail user={user} isSystemLevel={isSystemLevel} />} />\r\n            <Route path=\"assignment-progress/:assignmentId\" element={<HomeworkAssignmentProgress user={user} isSystemLevel={isSystemLevel} />} />\r\n            <Route path=\"upload-options\" element={<HomeworkUploadOptions user={user} isSystemLevel={isSystemLevel} />} />\r\n            <Route path=\"statistics\" element={<HomeworkStatistics user={user} isSystemLevel={isSystemLevel} />} />\r\n            <Route path=\"pending-review\" element={<PendingReviewHomeworks user={user} isSystemLevel={isSystemLevel} />} />\r\n            <Route path=\"finished\" element={<FinishedHomeworks user={user} isSystemLevel={isSystemLevel} />} />\r\n            <Route path=\"history\" element={<HomeworkDetail user={user} showHistory={true} isSystemLevel={isSystemLevel} />} />\r\n            <Route path=\"calendar\" element={<HomeworkCalendar user={user} isSystemLevel={isSystemLevel} />} />\r\n            <Route path=\"wrong-questions\" element={<div>错题收集功能正在开发中...</div>} />\r\n            <Route path=\"feedback\" element={<div>作业反馈功能正在开发中...</div>} />\r\n          </Routes>\r\n        </Content>\r\n      </Layout>\r\n    </Layout>\r\n    </FilterContext.Provider>\r\n  );\r\n};\r\n\r\nexport default HomeworkManagement; "], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,aAAa,QAAQ,OAAO;AACjE,SAASC,WAAW,EAAEC,WAAW,EAAEC,eAAe,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AAC3F,SACEC,MAAM,EAAEC,IAAI,EAAEC,UAAU,EAAEC,UAAU,EAAEC,IAAI,EAAEC,GAAG,EAAEC,GAAG,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,OAAO,EAAEC,IAAI,QACrF,MAAM;AACb,SACEC,cAAc,EAAEC,gBAAgB,EAAEC,YAAY,EAAEC,gBAAgB,EAAEC,mBAAmB,EACrFC,gBAAgB,EAAEC,mBAAmB,EAAEC,gBAAgB,EAAEC,yBAAyB,EAClFC,cAAc,EAAEC,cAAc,QACzB,mBAAmB;AAC1B,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,wBAAwB,MAAM,wCAAwC;AAC7E,OAAOC,sBAAsB,MAAM,sCAAsC;AACzE,OAAOC,qBAAqB,MAAM,qCAAqC;AACvE,OAAOC,wBAAwB,MAAM,wCAAwC;AAC7E,OAAOC,qBAAqB,MAAM,qCAAqC;AACvE,OAAOC,kBAAkB,MAAM,kCAAkC;AACjE,OAAOC,0BAA0B,MAAM,0CAA0C;AACjF,OAAOC,sBAAsB,MAAM,sCAAsC;AACzE,OAAOC,gBAAgB,MAAM,gCAAgC;AAC7D,OAAOC,iBAAiB,MAAM,iCAAiC;AAC/D,OAAOC,6BAA6B,MAAM,6CAA6C;AACvF,OAAOC,mBAAmB,MAAM,mCAAmC;AACnE,OAAOC,sBAAsB,MAAM,8CAA8C;AACjF,OAAOC,qBAAqB,MAAM,6CAA6C;AAC/E,OAAOC,qBAAqB,MAAM,6CAA6C;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhF,MAAM;EAAEC,KAAK;EAAEC;AAAQ,CAAC,GAAG3C,MAAM;AACjC,MAAM;EAAE4C;AAAM,CAAC,GAAGzC,UAAU;AAC5B,MAAM;EAAE0C;AAAU,CAAC,GAAG5C,IAAI;AAC1B,MAAM;EAAE6C;AAAO,CAAC,GAAGvC,MAAM;;AAEzB;AACA,MAAMwC,aAAa,gBAAGrD,aAAa,CAAC,CAAC;;AAErC;AACA,MAAMsD,oBAAoB,GAAGA,CAAC;EAAEC,IAAI;EAAEC;AAAe,CAAC,KAAK;EAAAC,EAAA;EACzD,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG7D,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC8D,MAAM,EAAEC,SAAS,CAAC,GAAG/D,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACgE,OAAO,EAAEC,UAAU,CAAC,GAAGjE,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACkE,OAAO,EAAEC,UAAU,CAAC,GAAGnE,QAAQ,CAAC;IACrCoE,QAAQ,EAAE,IAAI;IACdC,OAAO,EAAE;EACX,CAAC,CAAC;;EAEF;EACA,MAAMC,YAAY,GAAGb,IAAI,CAACc,QAAQ,IAAId,IAAI,CAACe,IAAI,KAAK,OAAO;;EAI3D;EACA,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACFR,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMS,QAAQ,GAAG,MAAMC,KAAK,CAAC,oBAAoB,EAAE;QACjDC,OAAO,EAAE;UACP,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC1D;MACF,CAAC,CAAC;MACF,IAAIJ,QAAQ,CAACK,EAAE,EAAE;QACf,MAAMC,IAAI,GAAG,MAAMN,QAAQ,CAACO,IAAI,CAAC,CAAC;QAClCpB,UAAU,CAACmB,IAAI,CAAC;;QAEhB;QACA,MAAME,aAAa,GAAGF,IAAI,CAACG,IAAI,CAACC,MAAM,IAAIA,MAAM,CAACC,EAAE,KAAK,CAAC,CAAC;QAE1D,IAAIH,aAAa,EAAE;UACjB,MAAMI,cAAc,GAAG;YACrBlB,QAAQ,EAAE,CAAC;YACXC,OAAO,EAAE,IAAI;YACbkB,SAAS,EAAE;UACb,CAAC;UACDpB,UAAU,CAACmB,cAAc,CAAC;UAC1BE,WAAW,CAAC,CAAC,CAAC;UACd9B,cAAc,CAAC4B,cAAc,CAAC;QAChC,CAAC,MAAM;UACL;UACA,MAAMG,YAAY,GAAGT,IAAI,CAACU,MAAM,CAACN,MAAM,IACrCA,MAAM,CAACC,EAAE,KAAKM,SAAS,IACvBP,MAAM,CAACC,EAAE,KAAK,IAAI,IAClBD,MAAM,CAACQ,IACT,CAAC;;UAED;UACA,MAAMC,eAAe,GAAGJ,YAAY,CAACN,IAAI,CAACC,MAAM,IAC9CA,MAAM,CAACQ,IAAI,CAACE,QAAQ,CAAC,MAAM,CAC7B,CAAC;UAED,IAAIZ,aAAa,GAAG,IAAI;UACxB,IAAIW,eAAe,EAAE;YACnBX,aAAa,GAAGW,eAAe;UACjC,CAAC,MAAM,IAAIJ,YAAY,CAACM,MAAM,GAAG,CAAC,EAAE;YAClCb,aAAa,GAAGO,YAAY,CAAC,CAAC,CAAC;UACjC;UAEA,IAAIP,aAAa,EAAE;YACjB,MAAMI,cAAc,GAAG;cACrBlB,QAAQ,EAAEc,aAAa,CAACG,EAAE;cAC1BhB,OAAO,EAAE,IAAI;cACbkB,SAAS,EAAE;YACb,CAAC;YACDpB,UAAU,CAACmB,cAAc,CAAC;YAC1BE,WAAW,CAACN,aAAa,CAACG,EAAE,CAAC;YAC7B3B,cAAc,CAAC4B,cAAc,CAAC;UAChC;QACF;MACF;IACF,CAAC,CAAC,OAAOU,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC9E,OAAO,CAAC8E,KAAK,CAAC,UAAU,CAAC;IAC3B,CAAC,SAAS;MACR/B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMiC,UAAU,GAAIpC,MAAM,IAAK;IAC7B,MAAMqC,UAAU,GAAG;MACjB;MACA,OAAO,EAAE,CAAC;MAAE,OAAO,EAAE,CAAC;MAAE,OAAO,EAAE,CAAC;MAClC,OAAO,EAAE,CAAC;MAAE,OAAO,EAAE,CAAC;MAAE,OAAO,EAAE,CAAC;MAClC;MACA,OAAO,EAAE,CAAC;MAAE,OAAO,EAAE,CAAC;MAAE,OAAO,EAAE,CAAC;MAClC,IAAI,EAAE,CAAC;MAAE,IAAI,EAAE,CAAC;MAAE,IAAI,EAAE,CAAC;MACzB;MACA,OAAO,EAAE,EAAE;MAAE,OAAO,EAAE,EAAE;MAAE,OAAO,EAAE,EAAE;MACrC,IAAI,EAAE,EAAE;MAAE,IAAI,EAAE,EAAE;MAAE,IAAI,EAAE,EAAE;MAC5B;MACA,KAAK,EAAE,CAAC;MAAE,KAAK,EAAE,CAAC;MAAE,KAAK,EAAE,CAAC;MAAE,KAAK,EAAE,CAAC;MAAE,KAAK,EAAE,CAAC;MAAE,KAAK,EAAE,CAAC;MAC1D,KAAK,EAAE,CAAC;MAAE,KAAK,EAAE,CAAC;MAAE,KAAK,EAAE,CAAC;MAC5B,KAAK,EAAE,EAAE;MAAE,MAAM,EAAE,EAAE;MAAE,MAAM,EAAE;IACjC,CAAC;IAED,OAAOrC,MAAM,CAACsC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MAC3B,MAAMC,MAAM,GAAGJ,UAAU,CAACE,CAAC,CAACT,IAAI,CAAC,IAAI,GAAG;MACxC,MAAMY,MAAM,GAAGL,UAAU,CAACG,CAAC,CAACV,IAAI,CAAC,IAAI,GAAG;MACxC,OAAOW,MAAM,GAAGC,MAAM;IACxB,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMhB,WAAW,GAAG,MAAOpB,QAAQ,IAAK;IACtC,IAAI,CAACA,QAAQ,EAAE;MACbL,SAAS,CAAC,EAAE,CAAC;MACb;IACF;IAEA,IAAI;MACF,MAAMW,QAAQ,GAAG,MAAMC,KAAK,CAAC,uCAAuCP,QAAQ,EAAE,EAAE;QAC9EQ,OAAO,EAAE;UACP,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC1D;MACF,CAAC,CAAC;MACF,IAAIJ,QAAQ,CAACK,EAAE,EAAE;QACf,MAAMC,IAAI,GAAG,MAAMN,QAAQ,CAACO,IAAI,CAAC,CAAC;QAClC;QACA,MAAMwB,YAAY,GAAGzB,IAAI,CAAC0B,GAAG,CAAC,CAACnB,SAAS,EAAEoB,KAAK,MAAM;UACnDtB,EAAE,EAAEsB,KAAK,GAAG,CAAC;UACbf,IAAI,EAAEL;QACR,CAAC,CAAC,CAAC;QACH;QACA,MAAMqB,YAAY,GAAGV,UAAU,CAACO,YAAY,CAAC;QAC7C1C,SAAS,CAAC6C,YAAY,CAAC;MACzB,CAAC,MAAM;QACL7C,SAAS,CAAC,EAAE,CAAC;MACf;IACF,CAAC,CAAC,OAAOiC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC9E,OAAO,CAAC8E,KAAK,CAAC,UAAU,CAAC;MACzB;MACA,MAAMa,aAAa,GAAG,CACpB;QAAExB,EAAE,EAAE,CAAC;QAAEO,IAAI,EAAE;MAAQ,CAAC,EACxB;QAAEP,EAAE,EAAE,CAAC;QAAEO,IAAI,EAAE;MAAQ,CAAC,EACxB;QAAEP,EAAE,EAAE,CAAC;QAAEO,IAAI,EAAE;MAAQ,CAAC,EACxB;QAAEP,EAAE,EAAE,CAAC;QAAEO,IAAI,EAAE;MAAQ,CAAC,EACxB;QAAEP,EAAE,EAAE,CAAC;QAAEO,IAAI,EAAE;MAAQ,CAAC,EACxB;QAAEP,EAAE,EAAE,CAAC;QAAEO,IAAI,EAAE;MAAQ,CAAC,EACxB;QAAEP,EAAE,EAAE,CAAC;QAAEO,IAAI,EAAE;MAAQ,CAAC,EACxB;QAAEP,EAAE,EAAE,CAAC;QAAEO,IAAI,EAAE;MAAQ,CAAC,EACxB;QAAEP,EAAE,EAAE,CAAC;QAAEO,IAAI,EAAE;MAAQ,CAAC,EACxB;QAAEP,EAAE,EAAE,EAAE;QAAEO,IAAI,EAAE;MAAQ,CAAC,EACzB;QAAEP,EAAE,EAAE,EAAE;QAAEO,IAAI,EAAE;MAAQ,CAAC,EACzB;QAAEP,EAAE,EAAE,EAAE;QAAEO,IAAI,EAAE;MAAQ,CAAC,CAC1B;MACD7B,SAAS,CAAC8C,aAAa,CAAC;IAC1B;EACF,CAAC;;EAID;EACA5G,SAAS,CAAC,MAAM;IACd,IAAIqE,YAAY,EAAE;MAChBG,YAAY,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,MAAM;MACL;MACA,IAAIhB,IAAI,CAACqD,SAAS,EAAE;QAClBtB,WAAW,CAAC/B,IAAI,CAACqD,SAAS,CAAC;QAC3B,MAAMxB,cAAc,GAAG;UACrBlB,QAAQ,EAAEX,IAAI,CAACqD,SAAS;UACxBzC,OAAO,EAAE,IAAI;UACbkB,SAAS,EAAE;QACb,CAAC;QACDpB,UAAU,CAACmB,cAAc,CAAC;QAC1B5B,cAAc,CAAC4B,cAAc,CAAC;MAChC;IACF;IACF;EACA,CAAC,EAAE,CAAC7B,IAAI,EAAEa,YAAY,CAAC,CAAC;;EAExB;EACA,MAAMyC,kBAAkB,GAAIC,KAAK,IAAK;IACpC,MAAMC,UAAU,GAAG;MACjB7C,QAAQ,EAAE4C,KAAK;MACf3C,OAAO,EAAE,IAAI;MACbkB,SAAS,EAAE;IACb,CAAC;IACDpB,UAAU,CAAC8C,UAAU,CAAC;IACtBlD,SAAS,CAAC,EAAE,CAAC;IAEb,IAAIiD,KAAK,EAAE;MACTxB,WAAW,CAACwB,KAAK,CAAC;IACpB;IAEAtD,cAAc,CAACuD,UAAU,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAIF,KAAK,IAAK;IACnC,MAAMG,aAAa,GAAGrD,MAAM,CAACqB,IAAI,CAACiC,CAAC,IAAIA,CAAC,CAAC/B,EAAE,KAAK2B,KAAK,CAAC;IACtD,MAAMC,UAAU,GAAG;MACjB,GAAG/C,OAAO;MACVG,OAAO,EAAE2C,KAAK;MACdzB,SAAS,EAAE4B,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEvB;IAC5B,CAAC;IACDzB,UAAU,CAAC8C,UAAU,CAAC;IAEtBvD,cAAc,CAACuD,UAAU,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMI,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAIC,eAAe,GAAG7D,IAAI,CAACqD,SAAS;IAEpC,IAAIxC,YAAY,EAAE;MAChB;MACA,MAAMY,aAAa,GAAGtB,OAAO,CAACuB,IAAI,CAACC,MAAM,IAAIA,MAAM,CAACC,EAAE,KAAK,CAAC,CAAC;MAC7DiC,eAAe,GAAGpC,aAAa,GAAG,CAAC,GAAItB,OAAO,CAACmC,MAAM,GAAG,CAAC,GAAGnC,OAAO,CAAC,CAAC,CAAC,CAACyB,EAAE,GAAG,IAAK;IACnF;IAEA,MAAMkC,YAAY,GAAG;MACnBnD,QAAQ,EAAEkD,eAAe;MACzBjD,OAAO,EAAE,IAAI;MACbkB,SAAS,EAAE;IACb,CAAC;IACDpB,UAAU,CAACoD,YAAY,CAAC;IACxBxD,SAAS,CAAC,EAAE,CAAC;;IAEb;IACA,IAAIuD,eAAe,EAAE;MACnB9B,WAAW,CAAC8B,eAAe,CAAC;IAC9B;IAEA5D,cAAc,CAAC6D,YAAY,CAAC;EAC9B,CAAC;EAED,oBACEtE,OAAA,CAACrC,IAAI;IACH4G,KAAK,eACHvE,OAAA,CAACjC,KAAK;MAAAyG,QAAA,gBACJxE,OAAA,CAACpB,cAAc;QAAA6F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,4BAEpB;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CACR;IACDC,IAAI,EAAC,OAAO;IACZC,KAAK,EAAE;MAAEC,YAAY,EAAE;IAAG,CAAE;IAAAP,QAAA,eAE5BxE,OAAA,CAACpC,GAAG;MAACoH,MAAM,EAAE,EAAG;MAAAR,QAAA,GAEbnD,YAAY,iBACXrB,OAAA,CAACnC,GAAG;QAACoH,IAAI,EAAE,CAAE;QAAAT,QAAA,eACXxE,OAAA,CAAClC,MAAM;UACLoH,WAAW,EAAC,0BAAM;UAClBJ,KAAK,EAAE;YAAEK,KAAK,EAAE;UAAO,CAAE;UACzBpB,KAAK,EAAE9C,OAAO,CAACE,QAAS;UACxBiE,QAAQ,EAAEtB,kBAAmB;UAC7B/C,OAAO,EAAEA,OAAQ;UACjBsE,UAAU;UAAAb,QAAA,EAET7D,OAAO,CACL8B,MAAM,CAACN,MAAM,IACZA,MAAM,CAACC,EAAE,KAAKM,SAAS,IACvBP,MAAM,CAACC,EAAE,KAAK,IAAI,IAClBD,MAAM,CAACQ,IACT,CAAC,CACAc,GAAG,CAACtB,MAAM,iBACTnC,OAAA,CAACK,MAAM;YAAiB0D,KAAK,EAAE5B,MAAM,CAACC,EAAG;YAAAoC,QAAA,EACtCrC,MAAM,CAACQ;UAAI,GADDR,MAAM,CAACC,EAAE;YAAAqC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEd,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,eAED5E,OAAA,CAACnC,GAAG;QAACoH,IAAI,EAAE5D,YAAY,GAAG,CAAC,GAAG,EAAG;QAAAmD,QAAA,eAC/BxE,OAAA,CAAClC,MAAM;UACLoH,WAAW,EAAC,0BAAM;UAClBJ,KAAK,EAAE;YAAEK,KAAK,EAAE;UAAO,CAAE;UACzBpB,KAAK,EAAE9C,OAAO,CAACG,OAAQ;UACvBgE,QAAQ,EAAEnB,iBAAkB;UAC5BqB,QAAQ,EAAEjE,YAAY,IAAI,CAACJ,OAAO,CAACE,QAAS;UAC5CkE,UAAU;UAAAb,QAAA,EAET3D,MAAM,CAAC4C,GAAG,CAAC8B,KAAK,iBACfvF,OAAA,CAACK,MAAM;YAAgB0D,KAAK,EAAEwB,KAAK,CAACnD,EAAG;YAAAoC,QAAA,EACpCe,KAAK,CAAC5C;UAAI,GADA4C,KAAK,CAACnD,EAAE;YAAAqC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEb,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEN5E,OAAA,CAACnC,GAAG;QAACoH,IAAI,EAAE5D,YAAY,GAAG,CAAC,GAAG,EAAG;QAAAmD,QAAA,eAC/BxE,OAAA,CAAChC,MAAM;UACLwH,IAAI,eAAExF,OAAA,CAACnB,cAAc;YAAA4F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBa,OAAO,EAAErB,WAAY;UAAAI,QAAA,EACtB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEX,CAAC;AAAClE,EAAA,CAzSIH,oBAAoB;AAAAmF,EAAA,GAApBnF,oBAAoB;AA2S1B,MAAMoF,kBAAkB,GAAGA,CAAC;EAAEnF,IAAI;EAAEoF,QAAQ;EAAEC,aAAa,GAAG;AAAM,CAAC,KAAK;EAAAC,GAAA;EACxE,MAAMC,QAAQ,GAAG7I,WAAW,CAAC,CAAC;EAC9B,MAAM8I,QAAQ,GAAG7I,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC8I,YAAY,CAAC,GAAG7I,eAAe,CAAC,CAAC;EACxC,MAAM,CAAC8I,WAAW,EAAEC,cAAc,CAAC,GAAGpJ,QAAQ,CAACyD,IAAI,CAAC4F,UAAU,IAAIP,aAAa,GAAG,GAAG,GAAG,qBAAqB,CAAC;EAC9G,MAAM,CAACQ,aAAa,EAAEC,gBAAgB,CAAC,GAAGvJ,QAAQ,CAAC;IACjDoE,QAAQ,EAAE,IAAI;IACdC,OAAO,EAAE,IAAI;IACbmF,OAAO,EAAE;EACX,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG1J,QAAQ,CAAC2J,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC;EAElE3J,SAAS,CAAC,MAAM;IACd,MAAM4J,YAAY,GAAGA,CAAA,KAAM;MACzBH,WAAW,CAACC,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC;IACvC,CAAC;IAEDD,MAAM,CAACG,gBAAgB,CAAC,QAAQ,EAAED,YAAY,CAAC;IAC/C,OAAO,MAAMF,MAAM,CAACI,mBAAmB,CAAC,QAAQ,EAAEF,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA5J,SAAS,CAAC,MAAM;IACd,MAAM+J,IAAI,GAAGf,QAAQ,CAACgB,QAAQ;IAC9B,IAAID,IAAI,CAAClE,QAAQ,CAAC,eAAe,CAAC,EAAE;MAClCsD,cAAc,CAAC,GAAG,CAAC;IACrB,CAAC,MAAM,IAAIY,IAAI,CAAClE,QAAQ,CAAC,gBAAgB,CAAC,EAAE;MAC1CsD,cAAc,CAAC,GAAG,CAAC;IACrB,CAAC,MAAM,IAAIY,IAAI,CAAClE,QAAQ,CAAC,SAAS,CAAC,EAAE;MACnCsD,cAAc,CAAC,GAAG,CAAC;IACrB,CAAC,MAAM,IAAIY,IAAI,CAAClE,QAAQ,CAAC,kBAAkB,CAAC,EAAE;MAC5CsD,cAAc,CAAC,GAAG,CAAC;IACrB,CAAC,MAAM,IAAIY,IAAI,CAAClE,QAAQ,CAAC,aAAa,CAAC,EAAE;MACvCsD,cAAc,CAAC,GAAG,CAAC;IACrB,CAAC,MAAM,IAAIY,IAAI,CAAClE,QAAQ,CAAC,iBAAiB,CAAC,EAAE;MAC3CsD,cAAc,CAAC,GAAG,CAAC;IACrB,CAAC,MAAM,IAAIY,IAAI,CAAClE,QAAQ,CAAC,WAAW,CAAC,EAAE;MACrCsD,cAAc,CAAC,GAAG,CAAC;IACrB,CAAC,MAAM,IAAIY,IAAI,CAAClE,QAAQ,CAAC,kBAAkB,CAAC,EAAE;MAC5CsD,cAAc,CAAC,GAAG,CAAC;IACrB,CAAC,MAAM,IAAIY,IAAI,CAAClE,QAAQ,CAAC,WAAW,CAAC,EAAE;MACrCsD,cAAc,CAAC,IAAI,CAAC;IACtB,CAAC,MAAM,IAAIY,IAAI,CAAClE,QAAQ,CAAC,sBAAsB,CAAC,EAAE;MAChDsD,cAAc,CAAC,qBAAqB,CAAC;IACvC,CAAC,MAAM,IAAIY,IAAI,CAACE,KAAK,CAAC,iBAAiB,CAAC,IAAIF,IAAI,CAACE,KAAK,CAAC,wBAAwB,CAAC,EAAE;MAChF;MACA,MAAMC,SAAS,GAAGjB,YAAY,CAACkB,GAAG,CAAC,MAAM,CAAC;MAC1C,IAAID,SAAS,KAAK,OAAO,EAAE;QACzBf,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC;MACvB,CAAC,MAAM,IAAIe,SAAS,KAAK,UAAU,EAAE;QACnCf,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC;MACvB,CAAC,MAAM;QACL;QACAA,cAAc,CAAC,GAAG,CAAC;MACrB;IACF,CAAC,MAAM,IAAIY,IAAI,KAAK,WAAW,IAAIA,IAAI,KAAK,kBAAkB,EAAE;MAC9D;MACAZ,cAAc,CAAC3F,IAAI,CAAC4F,UAAU,IAAIP,aAAa,GAAG,GAAG,GAAG,qBAAqB,CAAC;IAChF;EACF,CAAC,EAAE,CAACG,QAAQ,CAACgB,QAAQ,EAAExG,IAAI,CAAC4F,UAAU,EAAEP,aAAa,EAAEI,YAAY,CAAC,CAAC;;EAErE;EACA,MAAMmB,eAAe,GAAIC,CAAC,IAAK;IAC7B,MAAMC,GAAG,GAAGD,CAAC,CAACC,GAAG;IACjBnB,cAAc,CAACmB,GAAG,CAAC;;IAEnB;IACA,MAAMC,WAAW,GAAG1B,aAAa,GAAG,kBAAkB,GAAG,WAAW;IAEpE,QAAQyB,GAAG;MACT,KAAK,GAAG;QACNvB,QAAQ,CAAC,GAAGwB,WAAW,WAAW,CAAC;QACnC;MACF,KAAK,GAAG;QACNxB,QAAQ,CAAC,GAAGwB,WAAW,eAAe,CAAC;QACvC;MACF,KAAK,GAAG;QACN;QACAxB,QAAQ,CAAC,GAAGwB,WAAW,SAAS,EAAE;UAChCC,KAAK,EAAE;YACLvG,OAAO,EAAEoF,aAAa;YACtBR,aAAa,EAAEA;UACjB;QACF,CAAC,CAAC;QACF;MACF,KAAK,GAAG;QACNE,QAAQ,CAAC,GAAGwB,WAAW,kBAAkB,CAAC;QAC1C;MACF,KAAK,GAAG;QACNxB,QAAQ,CAAC,GAAGwB,WAAW,gBAAgB,CAAC;QACxC;MACF,KAAK,GAAG;QACN;QACA,IAAI1B,aAAa,EAAE;UACjBE,QAAQ,CAAC,2BAA2B,CAAC;QACvC,CAAC,MAAM;UACLA,QAAQ,CAAC,oBAAoB,CAAC;QAChC;QACA;MACF,KAAK,GAAG;QACNA,QAAQ,CAAC,GAAGwB,WAAW,iBAAiB,CAAC;QACzC;MACF,KAAK,GAAG;QACNxB,QAAQ,CAAC,GAAGwB,WAAW,WAAW,CAAC;QACnC;MACF,KAAK,GAAG;QACNxB,QAAQ,CAAC,GAAGwB,WAAW,kBAAkB,CAAC;QAC1C;MACF,KAAK,IAAI;QACPxB,QAAQ,CAAC,GAAGwB,WAAW,WAAW,CAAC;QACnC;MACF,KAAK,qBAAqB;QACxBxB,QAAQ,CAAC,GAAGwB,WAAW,sBAAsB,CAAC;QAC9C;MACF;QACE,IAAI/G,IAAI,CAAC4F,UAAU,IAAIP,aAAa,EAAE;UACpCE,QAAQ,CAAC,GAAGwB,WAAW,iBAAiB,CAAC;QAC3C,CAAC,MAAM;UACLxB,QAAQ,CAAC,GAAGwB,WAAW,sBAAsB,CAAC;QAChD;IACJ;EACF,CAAC;;EAED;EACA,IAAI,CAAC/G,IAAI,EAAE;IACT,oBAAOR,OAAA;MAAAwE,QAAA,EAAK;IAAM;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAC1B;;EAEA;EACA,MAAM6C,kBAAkB,GAAIxG,OAAO,IAAK;IACtCqF,gBAAgB,CAACrF,OAAO,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMyG,SAAS,GAAG7B,aAAa,GAAG,QAAQ,GAAIrF,IAAI,CAAC4F,UAAU,GAAG,MAAM,GAAG,MAAO;EAEhF,oBACEpG,OAAA,CAACM,aAAa,CAACqH,QAAQ;IAAC5D,KAAK,EAAEsC,aAAc;IAAA7B,QAAA,eAC3CxE,OAAA,CAACzC,MAAM;MAACqK,SAAS,EAAC,4BAA4B;MAAApD,QAAA,gBAC5CxE,OAAA,CAACvC,UAAU;QAACqH,KAAK,EAAE;UAAE+C,MAAM,EAAE;QAAS,CAAE;QAAArD,QAAA,gBACtCxE,OAAA,CAACvC,UAAU,CAACqK,IAAI;UAAAtD,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAiB,CAAC,eACrC5E,OAAA,CAACvC,UAAU,CAACqK,IAAI;UAAAtD,QAAA,EAAEkD;QAAS;UAAAjD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAkB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC,eAEb5E,OAAA,CAACG,KAAK;QAAC4H,KAAK,EAAE,CAAE;QAAAvD,QAAA,EACbkD;MAAS;QAAAjD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,EAGPiB,aAAa,iBACZ7F,OAAA,CAACO,oBAAoB;QACnBC,IAAI,EAAEA,IAAK;QACXC,cAAc,EAAEgH;MAAmB;QAAAhD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CACF,eAID5E,OAAA,CAACzC,MAAM;QAACqK,SAAS,EAAE/B,aAAa,GAAG,iCAAiC,GAAG,0BAA2B;QAACf,KAAK,EAAE;UAAEkD,UAAU,EAAE,MAAM;UAAEC,OAAO,EAAEzB,QAAQ,GAAG,GAAG,GAAG;QAAS,CAAE;QAAAhC,QAAA,GAEpKgC,QAAQ,KAAKhG,IAAI,CAAC4F,UAAU,IAAIP,aAAa,CAAC,iBAC7C7F,OAAA;UAAK4H,SAAS,EAAC,gBAAgB;UAAApD,QAAA,eAC7BxE,OAAA,CAAC9B,IAAI;YACHgK,SAAS,EAAEhC,WAAY;YACvBd,QAAQ,EAAGkC,GAAG,IAAKF,eAAe,CAAC;cAAEE;YAAI,CAAC,CAAE;YAC5Ca,IAAI,EAAC,MAAM;YACXtD,IAAI,EAAC,OAAO;YACZuD,KAAK,EAAE,CACL;cACEd,GAAG,EAAE,GAAG;cACRe,KAAK,EAAE,MAAM;cACb7C,IAAI,eAAExF,OAAA,CAACvB,mBAAmB;gBAAAgG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAC9B,CAAC,EACD;cACE0C,GAAG,EAAE,GAAG;cACRe,KAAK,EAAE,MAAM;cACb7C,IAAI,eAAExF,OAAA,CAAC3B,YAAY;gBAAAoG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YACvB,CAAC,EACD;cACE0C,GAAG,EAAE,GAAG;cACRe,KAAK,EAAE,MAAM;cACb7C,IAAI,eAAExF,OAAA,CAAC1B,gBAAgB;gBAAAmG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAC3B,CAAC,EACD;cACE0C,GAAG,EAAE,GAAG;cACRe,KAAK,EAAE,MAAM;cACb7C,IAAI,eAAExF,OAAA,CAACzB,mBAAmB;gBAAAkG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAC9B,CAAC,EACD;cACE0C,GAAG,EAAE,GAAG;cACRe,KAAK,EAAE,MAAM;cACb7C,IAAI,eAAExF,OAAA,CAAC7B,cAAc;gBAAAsG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YACzB,CAAC,EACD;cACE0C,GAAG,EAAE,GAAG;cACRe,KAAK,EAAE,MAAM;cACb7C,IAAI,eAAExF,OAAA,CAAC5B,gBAAgB;gBAAAqG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAC3B,CAAC,EACD;cACE0C,GAAG,EAAE,GAAG;cACRe,KAAK,EAAE,MAAM;cACb7C,IAAI,eAAExF,OAAA,CAACxB,gBAAgB;gBAAAiG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAC3B,CAAC,EACD;cACE0C,GAAG,EAAE,GAAG;cACRe,KAAK,EAAE,MAAM;cACb7C,IAAI,eAAExF,OAAA,CAACtB,gBAAgB;gBAAA+F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAC3B,CAAC,EACD;cACE0C,GAAG,EAAE,GAAG;cACRe,KAAK,EAAE,MAAM;cACb7C,IAAI,eAAExF,OAAA,CAACrB,yBAAyB;gBAAA8F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YACpC,CAAC;UACD;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,EAGA,CAAC4B,QAAQ,KAAKhG,IAAI,CAAC4F,UAAU,IAAIP,aAAa,CAAC,iBAC9C7F,OAAA,CAACC,KAAK;UAACkF,KAAK,EAAE,GAAI;UAACL,KAAK,EAAE;YAAEkD,UAAU,EAAE;UAAO,CAAE;UAAAxD,QAAA,eAC/CxE,OAAA,CAACxC,IAAI;YACH8K,IAAI,EAAC,QAAQ;YACbC,YAAY,EAAE,CAACrC,WAAW,CAAE;YAC5BpB,KAAK,EAAE;cAAE0D,MAAM,EAAE;YAAO,CAAE;YAC1B/C,OAAO,EAAE2B,eAAgB;YAAA5C,QAAA,gBAEzBxE,OAAA,CAACI,SAAS;cAACmE,KAAK,EAAC,0BAAM;cAAAC,QAAA,gBACrBxE,OAAA,CAACxC,IAAI,CAACsK,IAAI;gBAAStC,IAAI,eAAExF,OAAA,CAAC3B,YAAY;kBAAAoG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAJ,QAAA,EAAC;cAAM,GAAlC,GAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAA0C,CAAC,eAC7D5E,OAAA,CAACxC,IAAI,CAACsK,IAAI;gBAAStC,IAAI,eAAExF,OAAA,CAAC1B,gBAAgB;kBAAAmG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAJ,QAAA,EAAC;cAAI,GAApC,GAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAA4C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC,eAEZ5E,OAAA,CAACI,SAAS;cAACmE,KAAK,EAAEsB,aAAa,GAAG,QAAQ,GAAG,MAAO;cAAArB,QAAA,gBAClDxE,OAAA,CAACxC,IAAI,CAACsK,IAAI;gBAAStC,IAAI,eAAExF,OAAA,CAACvB,mBAAmB;kBAAAgG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAJ,QAAA,EAAC;cAAI,GAAvC,GAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAA+C,CAAC,eAClE5E,OAAA,CAACxC,IAAI,CAACsK,IAAI;gBAAStC,IAAI,eAAExF,OAAA,CAACzB,mBAAmB;kBAAAkG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAJ,QAAA,EAAC;cAAI,GAAvC,GAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAA+C,CAAC,eAClE5E,OAAA,CAACxC,IAAI,CAACsK,IAAI;gBAAStC,IAAI,eAAExF,OAAA,CAAC7B,cAAc;kBAAAsG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAJ,QAAA,EAAC;cAAI,GAAlC,GAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAA0C,CAAC,eAC7D5E,OAAA,CAACxC,IAAI,CAACsK,IAAI;gBAAStC,IAAI,eAAExF,OAAA,CAAC5B,gBAAgB;kBAAAqG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAJ,QAAA,EAAC;cAAI,GAApC,GAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAA4C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC,eAEZ5E,OAAA,CAACI,SAAS;cAACmE,KAAK,EAAC,0BAAM;cAAAC,QAAA,gBACrBxE,OAAA,CAACxC,IAAI,CAACsK,IAAI;gBAAStC,IAAI,eAAExF,OAAA,CAACxB,gBAAgB;kBAAAiG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAJ,QAAA,EAAC;cAAI,GAApC,GAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAA4C,CAAC,eAC/D5E,OAAA,CAACxC,IAAI,CAACsK,IAAI;gBAAStC,IAAI,eAAExF,OAAA,CAACtB,gBAAgB;kBAAA+F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAJ,QAAA,EAAC;cAAI,GAApC,GAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAA4C,CAAC,eAC/D5E,OAAA,CAACxC,IAAI,CAACsK,IAAI;gBAAStC,IAAI,eAAExF,OAAA,CAACrB,yBAAyB;kBAAA8F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAJ,QAAA,EAAC;cAAI,GAA7C,GAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAqD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CACR,eAED5E,OAAA,CAACE,OAAO;UACN0H,SAAS,EAAEpB,QAAQ,GAAG,gBAAgB,GAAG,EAAG;UAC5C1B,KAAK,EAAE;YACLmD,OAAO,EAAGzH,IAAI,CAAC4F,UAAU,IAAIP,aAAa,GAAKW,QAAQ,GAAG,GAAG,GAAG,QAAQ,GAAI,GAAG;YAC/EiC,SAAS,EAAE;UACb,CAAE;UAAAjE,QAAA,eAEFxE,OAAA,CAAC3C,MAAM;YAAAmH,QAAA,gBACLxE,OAAA,CAAC1C,KAAK;cAACoG,KAAK;cAACgF,OAAO,EAClBlI,IAAI,CAAC4F,UAAU,IAAIP,aAAa,gBAC9B7F,OAAA,CAACT,sBAAsB;gBAACiB,IAAI,EAAEA,IAAK;gBAACqF,aAAa,EAAEA;cAAc;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBACpE5E,OAAA,CAACN,6BAA6B;gBAACc,IAAI,EAAEA;cAAK;gBAAAiE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAC/C;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACJ5E,OAAA,CAAC1C,KAAK;cAACyJ,IAAI,EAAC,cAAc;cAAC2B,OAAO,eAAE1I,OAAA,CAAClB,cAAc;gBAAC0B,IAAI,EAAEA,IAAK;gBAACmI,SAAS,EAAE,IAAK;gBAAC9C,aAAa,EAAEA;cAAc;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrH5E,OAAA,CAAC1C,KAAK;cAACyJ,IAAI,EAAC,eAAe;cAAC2B,OAAO,eAAE1I,OAAA,CAACd,qBAAqB;gBAACsB,IAAI,EAAEA,IAAK;gBAACqF,aAAa,EAAEA;cAAc;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5G5E,OAAA,CAAC1C,KAAK;cAACyJ,IAAI,EAAC,QAAQ;cAAC2B,OAAO,eAAE1I,OAAA,CAACL,mBAAmB;gBAACa,IAAI,EAAEA;cAAK;gBAAAiE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EAEpE,CAACpE,IAAI,CAAC4F,UAAU,IAAI,CAACP,aAAa,iBACjC7F,OAAA,CAAC1C,KAAK;cAACyJ,IAAI,EAAC,SAAS;cAAC2B,OAAO,eAAE1I,OAAA,CAACJ,sBAAsB;gBAACY,IAAI,EAAEA;cAAK;gBAAAiE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CACzE,EAEA,CAACpE,IAAI,CAAC4F,UAAU,IAAI,CAACP,aAAa,iBACjC7F,OAAA,CAAC1C,KAAK;cAACyJ,IAAI,EAAC,QAAQ;cAAC2B,OAAO,eAAE1I,OAAA,CAACH,qBAAqB;gBAACW,IAAI,EAAEA;cAAK;gBAAAiE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CACvE,EAEA,CAACpE,IAAI,CAAC4F,UAAU,IAAI,CAACP,aAAa,iBACjC7F,OAAA,CAAC1C,KAAK;cAACyJ,IAAI,EAAC,oBAAoB;cAAC2B,OAAO,eAAE1I,OAAA,CAACF,qBAAqB;gBAACU,IAAI,EAAEA;cAAK;gBAAAiE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CACnF,eACD5E,OAAA,CAAC1C,KAAK;cAACyJ,IAAI,EAAC,aAAa;cAAC2B,OAAO,eAAE1I,OAAA,CAACjB,cAAc;gBAACyB,IAAI,EAAEA,IAAK;gBAACqF,aAAa,EAAEA;cAAc;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnG5E,OAAA,CAAC1C,KAAK;cAACyJ,IAAI,EAAC,QAAQ;cAAC2B,OAAO,eAAE1I,OAAA,CAAChB,wBAAwB;gBAACwB,IAAI,EAAEA,IAAK;gBAACqF,aAAa,EAAEA;cAAc;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxG5E,OAAA,CAAC1C,KAAK;cAACyJ,IAAI,EAAC,iBAAiB;cAAC2B,OAAO,eAAE1I,OAAA,CAACf,sBAAsB;gBAACuB,IAAI,EAAEA,IAAK;gBAACqF,aAAa,EAAEA;cAAc;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/G5E,OAAA,CAAC1C,KAAK;cAACyJ,IAAI,EAAC,qBAAqB;cAAC2B,OAAO,eAAE1I,OAAA,CAACN,6BAA6B;gBAACc,IAAI,EAAEA;cAAK;gBAAAiE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5F5E,OAAA,CAAC1C,KAAK;cAACyJ,IAAI,EAAC,iCAAiC;cAAC2B,OAAO,eAAE1I,OAAA,CAACb,wBAAwB;gBAACqB,IAAI,EAAEA,IAAK;gBAACqF,aAAa,EAAEA;cAAc;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjI5E,OAAA,CAAC1C,KAAK;cAACyJ,IAAI,EAAC,mCAAmC;cAAC2B,OAAO,eAAE1I,OAAA,CAACV,0BAA0B;gBAACkB,IAAI,EAAEA,IAAK;gBAACqF,aAAa,EAAEA;cAAc;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrI5E,OAAA,CAAC1C,KAAK;cAACyJ,IAAI,EAAC,gBAAgB;cAAC2B,OAAO,eAAE1I,OAAA,CAACZ,qBAAqB;gBAACoB,IAAI,EAAEA,IAAK;gBAACqF,aAAa,EAAEA;cAAc;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7G5E,OAAA,CAAC1C,KAAK;cAACyJ,IAAI,EAAC,YAAY;cAAC2B,OAAO,eAAE1I,OAAA,CAACX,kBAAkB;gBAACmB,IAAI,EAAEA,IAAK;gBAACqF,aAAa,EAAEA;cAAc;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtG5E,OAAA,CAAC1C,KAAK;cAACyJ,IAAI,EAAC,gBAAgB;cAAC2B,OAAO,eAAE1I,OAAA,CAACT,sBAAsB;gBAACiB,IAAI,EAAEA,IAAK;gBAACqF,aAAa,EAAEA;cAAc;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9G5E,OAAA,CAAC1C,KAAK;cAACyJ,IAAI,EAAC,UAAU;cAAC2B,OAAO,eAAE1I,OAAA,CAACP,iBAAiB;gBAACe,IAAI,EAAEA,IAAK;gBAACqF,aAAa,EAAEA;cAAc;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnG5E,OAAA,CAAC1C,KAAK;cAACyJ,IAAI,EAAC,SAAS;cAAC2B,OAAO,eAAE1I,OAAA,CAACjB,cAAc;gBAACyB,IAAI,EAAEA,IAAK;gBAACoI,WAAW,EAAE,IAAK;gBAAC/C,aAAa,EAAEA;cAAc;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClH5E,OAAA,CAAC1C,KAAK;cAACyJ,IAAI,EAAC,UAAU;cAAC2B,OAAO,eAAE1I,OAAA,CAACR,gBAAgB;gBAACgB,IAAI,EAAEA,IAAK;gBAACqF,aAAa,EAAEA;cAAc;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClG5E,OAAA,CAAC1C,KAAK;cAACyJ,IAAI,EAAC,iBAAiB;cAAC2B,OAAO,eAAE1I,OAAA;gBAAAwE,QAAA,EAAK;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpE5E,OAAA,CAAC1C,KAAK;cAACyJ,IAAI,EAAC,UAAU;cAAC2B,OAAO,eAAE1I,OAAA;gBAAAwE,QAAA,EAAK;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACe,CAAC;AAE7B,CAAC;AAACkB,GAAA,CA1SIH,kBAAkB;EAAA,QACLzI,WAAW,EACXC,WAAW,EACLC,eAAe;AAAA;AAAAyL,GAAA,GAHlClD,kBAAkB;AA4SxB,eAAeA,kBAAkB;AAAC,IAAAD,EAAA,EAAAmD,GAAA;AAAAC,YAAA,CAAApD,EAAA;AAAAoD,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}