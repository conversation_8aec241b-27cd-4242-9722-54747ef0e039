import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  Table, Tag, Button, Input, Select, DatePicker, Space, message,
  Alert, Empty, Spin, Typography, Popconfirm, Badge
} from 'antd';
import { 
  SearchOutlined, FileTextOutlined, CheckCircleOutlined, DeleteOutlined,
  SyncOutlined, ClockCircleOutlined, HistoryOutlined
} from '@ant-design/icons';
import { getHomeworks, deleteHomework, getHomeworkAssignments } from '../utils/api';

const { Option } = Select;
const { RangePicker } = DatePicker;
const { Title, Text } = Typography;

// 简单的防抖函数
const debounce = (fn, delay) => {
  let timer = null;
  return function(...args) {
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => {
      fn.apply(this, args);
    }, delay);
  };
};

const FinishedHomeworks = ({ user }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [loading, setLoading] = useState(false);
  const [homeworks, setHomeworks] = useState([]);
  const [assignments, setAssignments] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const [error, setError] = useState(null);
  const [assignmentsLoading, setAssignmentsLoading] = useState(false);
  const [lastSelectedAssignment, setLastSelectedAssignment] = useState(null);
  
  // 过滤条件
  const [filters, setFilters] = useState({
    status: '',  // 默认不过滤状态，显示所有已结束的作业
    search: '',
    dateRange: null,
    assignmentId: null
  });
  
  // 获取作业任务列表
  const fetchAssignments = async () => {
    try {
      setAssignmentsLoading(true);
      console.log('开始获取已结束作业任务列表');
      const response = await getHomeworkAssignments();
      console.log('获取作业任务列表响应:', response);
      
      let assignmentsList = [];
      
      if (response && response.items) {
        assignmentsList = response.items;
      } else if (Array.isArray(response)) {
        assignmentsList = response;
      }
      
      console.log('解析后的作业任务列表:', assignmentsList);

      // 处理作业任务数据，确保每个作业任务都有正确的status字段
      const processedAssignments = assignmentsList.map(assignment => {
        let status = assignment.status;

        // 如果没有status字段，从description中提取状态
        if (!status && assignment.description) {
          // 使用正则表达式提取状态
          const statusMatch = assignment.description.match(/【状态】(.*?)】/);
          if (statusMatch) {
            status = statusMatch[1];
          } else {
            status = 'active'; // 默认状态为active
          }
        } else if (!status) {
          status = 'active'; // 默认状态为active
        }

        return {
          ...assignment,
          status: status
        };
      });

      console.log('处理后的作业任务列表:', processedAssignments);

      // 过滤未结束的作业任务，只保留已结束的作业任务
      const finishedAssignments = processedAssignments.filter(assignment =>
        assignment.status === 'finished'
      );
      
      console.log('已结束的作业任务:', finishedAssignments);
      setAssignments(finishedAssignments);
      
      // 如果有作业任务，默认选择第一个
      if (finishedAssignments.length > 0 && !filters.assignmentId) {
        console.log('选择第一个作业任务:', finishedAssignments[0]);
        const firstAssignmentId = finishedAssignments[0].id;
        
        // 同时更新lastSelectedAssignment，避免重复刷新
        setLastSelectedAssignment(firstAssignmentId);
        
        setFilters(prev => ({
          ...prev,
          assignmentId: firstAssignmentId
        }));
        
        // 加载该作业任务的作业
        fetchHomeworks({
          assignment_id: firstAssignmentId
        });
      } else if (finishedAssignments.length === 0) {
        // 如果没有作业任务，清空作业列表
        console.log('没有已结束的作业任务，清空作业列表');
        setHomeworks([]);
        setLoading(false);
      }
    } catch (error) {
      console.error('获取已结束作业任务列表失败:', error);
      message.error('获取已结束作业任务列表失败');
      setHomeworks([]);
      setLoading(false);
    } finally {
      setAssignmentsLoading(false);
    }
  };
  
  // 获取作业列表
  const fetchHomeworks = async (params = {}) => {
    // 如果没有选择作业任务，不进行请求
    if (!params.assignment_id && !filters.assignmentId) {
      console.log('没有选择作业任务，不请求作业列表');
      setHomeworks([]);
      setLoading(false);
      return;
    }
    
    try {
      setLoading(true);
      setError(null);
      
      // 构建API参数
      const apiParams = {
        page: params.page || pagination.current,
        limit: params.pageSize || pagination.pageSize,
        status: params.status || filters.status,
        search: params.search || filters.search,
        assignment_id: params.assignment_id || filters.assignmentId
      };
      
      // 添加日期范围
      if (params.dateRange || filters.dateRange) {
        const dateRange = params.dateRange || filters.dateRange;
        if (dateRange && dateRange.length === 2) {
          apiParams.start_date = dateRange[0].format('YYYY-MM-DD');
          apiParams.end_date = dateRange[1].format('YYYY-MM-DD');
        }
      }
      
      console.log('请求已结束作业列表，参数:', apiParams);
      
      // 获取数据
      const data = await getHomeworks(apiParams);
      console.log('已结束作业列表响应:', data);
      
      // 检查响应数据
      if (data && Array.isArray(data.items)) {
        setHomeworks(data.items);
        setPagination({
          ...pagination,
          current: params.page || pagination.current,
          total: data.total || data.items.length
        });
      } else if (Array.isArray(data)) {
        setHomeworks(data);
        setPagination({
          ...pagination,
          current: params.page || pagination.current,
          total: data.length
        });
      } else if (data && typeof data === 'object' && data.id) {
        setHomeworks([data]);
        setPagination({
          ...pagination,
          current: 1,
          total: 1
        });
      } else {
        console.warn('已结束作业列表响应格式不符合预期:', data);
        setHomeworks([]);
        setPagination({
          ...pagination,
          current: 1,
          total: 0
        });
        setError('获取已结束作业列表格式异常，暂无数据');
        message.warning('暂无已结束作业数据');
      }
    } catch (error) {
      console.error('获取已结束作业列表失败:', error);
      setError(`获取已结束作业列表失败: ${error.message || '未知错误'}`);
      setHomeworks([]);
      setPagination({
        ...pagination,
        current: 1,
        total: 0
      });
      message.error('获取已结束作业列表失败，请稍后重试');
    } finally {
      // 确保无论如何都设置loading为false
      setLoading(false);
      console.log('已结束作业列表加载完成，loading状态已重置');
    }
  };
  
  // 首次加载时获取作业任务列表和作业列表
  useEffect(() => {
    fetchAssignments();
  }, []);
  
  // 处理location.state中的refresh参数
  useEffect(() => {
    if (location.state && location.state.refresh) {
      console.log('检测到需要刷新已结束作业列表');
      
      // 检查是否需要强制刷新
      const forceRefresh = location.state.forceRefresh === true;
      const timestamp = location.state.timestamp || new Date().getTime();
      console.log(`强制刷新: ${forceRefresh}, 时间戳: ${timestamp}`);
      
      // 先获取作业任务列表
      fetchAssignments();
      
      // 同时刷新当前选中的作业任务的作业列表
      if (filters.assignmentId) {
        console.log('同时刷新当前作业任务的作业列表:', filters.assignmentId);
        
        // 更新lastSelectedAssignment，避免刷新后重复请求
        setLastSelectedAssignment(filters.assignmentId);
        
        fetchHomeworks({
          assignment_id: filters.assignmentId,
          page: 1,  // 重置到第一页
          cache: false,  // 禁用缓存
          _t: timestamp  // 添加时间戳参数，确保不使用缓存
        });
      }
      
      // 清除state，避免重复刷新
      navigate(location.pathname, { replace: true, state: {} });
    }
  }, [location.state, navigate, location.pathname, filters.assignmentId]);
  
  // 处理表格变化
  const handleTableChange = (pagination, filters) => {
    fetchHomeworks({
      page: pagination.current,
      pageSize: pagination.pageSize,
      ...filters
    });
  };
  
  // 处理搜索
  const handleSearch = () => {
    fetchHomeworks({ page: 1, ...filters });
  };
  
  // 处理查看详情
  const handleViewDetail = (id) => {
    navigate(`/homework/${id}?from=finished`);
  };
  
  // 处理刷新
  const handleRefresh = () => {
    console.log('手动刷新已结束作业列表');
    fetchAssignments();
    
    // 同时刷新当前选中的作业任务的作业列表
    if (filters.assignmentId) {
      console.log('同时刷新当前作业任务的作业列表:', filters.assignmentId);
      
      // 更新lastSelectedAssignment，避免刷新后重复请求
      setLastSelectedAssignment(filters.assignmentId);
      
      fetchHomeworks({
        assignment_id: filters.assignmentId,
        page: 1,  // 重置到第一页
        cache: false  // 禁用缓存
      });
    }
    
    message.info('正在刷新已结束作业列表...');
  };
  
  // 处理作业任务选择变更
  const handleAssignmentChange = (value) => {
    // 如果选择的是同一个作业任务，不重复请求
    if (value === lastSelectedAssignment) {
      console.log('选择了相同的作业任务，跳过刷新');
      return;
    }
    
    // 更新上次选择的作业任务
    setLastSelectedAssignment(value);
    
    setFilters(prev => ({
      ...prev,
      assignmentId: value
    }));
    
    // 获取作业列表，只刷新一次
    fetchHomeworks({
      assignment_id: value,
      page: 1
    });
  };
  
  // 处理删除作业
  const handleDelete = async (id) => {
    try {
      await deleteHomework(id);
      message.success('作业删除成功');
      fetchHomeworks();
    } catch (error) {
      console.error('删除作业失败:', error);
      message.error(`删除作业失败: ${error.message || '未知错误'}`);
    }
  };
  
  // 获取状态标签
  const getStatusTag = (status) => {
    switch (status) {
      case 'submitted':
        return <Tag color="blue" icon={<ClockCircleOutlined />}>已提交</Tag>;
      case 'grading':
      case 'correcting':
        return <Tag color="orange" icon={<SyncOutlined spin />}>批改中</Tag>;
      case 'graded':
      case 'corrected':
        return <Tag color="green" icon={<CheckCircleOutlined />}>已批改</Tag>;
      default:
        return <Tag color="default">未知状态</Tag>;
    }
  };
  
  // 表格列定义
  const columns = [
    {
      title: '标题',
      dataIndex: 'title',
      key: 'title',
      render: (text, record) => (
        <a href={`/homework/${record.id}`} onClick={(e) => {
          e.preventDefault();
          handleViewDetail(record.id);
        }}>{text}</a>
      )
    },
    {
      title: user.is_teacher ? '学生' : '作业来源',
      dataIndex: user.is_teacher ? 'student_name' : 'assignment_title',
      key: user.is_teacher ? 'student_name' : 'assignment_title',
      render: (text, record) => {
        if (user.is_teacher) {
          return text || `学生ID: ${record.student_id}`;
        } else {
          return text || record.title || '-';
        }
      }
    },
    {
      title: '班级',
      dataIndex: 'class_name',
      key: 'class_name',
      render: (text, record) => {
        if (text) {
          return text;
        } else if (record.class_id) {
          return `班级ID: ${record.class_id}`;
        } else {
          return '-';
        }
      }
    },
    {
      title: '提交次数',
      dataIndex: 'version_count',
      key: 'version_count',
      render: (count, record) => (
        <Space>
          <Badge 
            count={count || 1} 
            style={{ 
              backgroundColor: count > 1 ? '#52c41a' : '#1890ff',
              fontSize: '12px',
              padding: '0 6px'
            }} 
            overflowCount={99}
          />
          {count > 1 && (
            <Button 
              type="link" 
              icon={<ClockCircleOutlined />} 
              size="small"
              onClick={(e) => {
                e.stopPropagation();
                navigate(`/homework/history?student_id=${record.student_id}&assignment_id=${record.assignment_id}`);
              }}
              title="查看提交历史"
              style={{ padding: '0 4px' }}
            />
          )}
        </Space>
      ),
      sorter: (a, b) => (a.version_count || 1) - (b.version_count || 1),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => getStatusTag(status)
    },
    {
      title: '提交时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date) => new Date(date).toLocaleString()
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Button
            type="primary"
            icon={<FileTextOutlined />}
            onClick={() => handleViewDetail(record.id)}
          >
            查看
          </Button>
          {user.is_teacher && record.status === 'submitted' && (
            <Button
              type="primary"
              icon={<CheckCircleOutlined />}
              onClick={() => navigate(`/homework/${record.id}?from=finished`, { state: { startCorrection: true } })}
            >
              批改
            </Button>
          )}
          {user.is_teacher && (
            <Popconfirm
              title="确定删除该作业吗？"
              onConfirm={() => handleDelete(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button
                type="primary"
                danger
                icon={<DeleteOutlined />}
              >
                删除
              </Button>
            </Popconfirm>
          )}
        </Space>
      )
    }
  ];

  // 渲染内容
  const renderContent = () => {
    // 如果正在加载作业任务列表
    if (assignmentsLoading) {
      return (
        <div style={{ textAlign: 'center', padding: '50px 0' }}>
          <Spin tip="加载作业任务列表..." />
        </div>
      );
    }
    
    // 如果没有作业任务
    if (assignments.length === 0) {
      return (
        <Empty 
          description="没有已结束的作业任务" 
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      );
    }
    
    // 如果有作业任务但没有作业
    return (
      <>
        {/* 筛选工具栏 */}
        <div className="filter-toolbar" style={{ marginBottom: 16, marginTop: 16 }}>
          <Space style={{ marginBottom: 16 }}>
            <Select
              placeholder="选择作业任务"
              style={{ width: 200 }}
              value={filters.assignmentId}
              onChange={handleAssignmentChange}
              loading={loading}
            >
              {assignments.map(assignment => (
                <Option key={assignment.id} value={assignment.id}>
                  {assignment.title}
                </Option>
              ))}
            </Select>
            
            <Input
              placeholder="搜索作业标题或学生"
              value={filters.search}
              onChange={(e) => setFilters({ ...filters, search: e.target.value })}
              style={{ width: 200 }}
            />
            
            <Select
              placeholder="选择状态"
              style={{ width: 120 }}
              value={filters.status}
              onChange={(value) => setFilters({ ...filters, status: value })}
            >
              <Option value="">全部</Option>
              <Option value="submitted">待批改</Option>
              <Option value="grading">批改中</Option>
              <Option value="graded">已批改</Option>
            </Select>
            
            <Button
              type="primary"
              icon={<SearchOutlined />}
              onClick={handleSearch}
            >
              搜索
            </Button>
            
            <Button onClick={handleRefresh}>
              刷新
            </Button>
          </Space>
        </div>
        
        {/* 错误提示 */}
        {error && (
          <Alert
            message="获取数据时出现错误"
            description={error}
            type="error"
            showIcon
            style={{ marginBottom: 16 }}
            action={
              <Button size="small" onClick={handleRefresh}>
                重试
              </Button>
            }
          />
        )}
        
        {/* 数据表格 */}
        <Spin spinning={loading}>
          {homeworks.length > 0 ? (
            <Table
              columns={columns}
              dataSource={homeworks}
              rowKey="id"
              pagination={pagination}
              onChange={handleTableChange}
              loading={loading}
            />
          ) : (
            <Empty description={
              loading ? "加载中..." : "暂无已结束作业"
            } />
          )}
        </Spin>
      </>
    );
  };

  return (
    <div className="finished-homeworks-container">
      <Title level={3}>往日作业</Title>
      <Text type="secondary">显示所有已结束作业任务的作业提交情况</Text>
      
      {renderContent()}
    </div>
  );
};

export default FinishedHomeworks; 