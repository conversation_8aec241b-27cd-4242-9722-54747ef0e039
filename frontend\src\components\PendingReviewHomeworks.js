import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useNavigate, useSearchParams, useLocation } from 'react-router-dom';
import {
  Table, Tag, Button, Input, Select, DatePicker, Space, message,
  Alert, Empty, Spin, Typography, Popconfirm, List, Card, Row, Col, Avatar
} from 'antd';
import {
  SearchOutlined, FileTextOutlined, CheckCircleOutlined, DeleteOutlined,
  SyncOutlined, ClockCircleOutlined, CalendarOutlined, UserOutlined
} from '@ant-design/icons';
import { getHomeworks, deleteHomework, getHomeworkAssignments } from '../utils/api';

const { Option } = Select;
const { RangePicker } = DatePicker;
const { Title, Text } = Typography;

// 简单的防抖函数
const debounce = (fn, delay) => {
  let timer = null;
  return function(...args) {
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => {
      fn.apply(this, args);
    }, delay);
  };
};

const PendingReviewHomeworks = ({ user }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [searchParams] = useSearchParams();
  const [loading, setLoading] = useState(false);
  const [homeworks, setHomeworks] = useState([]);
  const [assignments, setAssignments] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const [error, setError] = useState(null);
  const [assignmentsLoading, setAssignmentsLoading] = useState(false);
  const [lastSelectedAssignment, setLastSelectedAssignment] = useState(null);
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);
  
  // 过滤条件
  const [filters, setFilters] = useState({
    status: '',  // 默认不过滤状态，显示所有进行中的作业
    search: searchParams.get('search') || '',
    dateRange: null,
    assignmentId: null
  });

  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);
  
  // 获取作业任务列表
  const fetchAssignments = async () => {
    try {
      setAssignmentsLoading(true);
      console.log('开始获取作业任务列表');
      const response = await getHomeworkAssignments();
      console.log('获取作业任务列表响应:', response);
      
      let assignmentsList = [];
      
      if (response && response.items) {
        assignmentsList = response.items;
      } else if (Array.isArray(response)) {
        assignmentsList = response;
      }
      
      console.log('解析后的作业任务列表:', assignmentsList);

      // 处理作业任务数据，确保每个作业任务都有正确的status字段
      const processedAssignments = assignmentsList.map(assignment => {
        let status = assignment.status;

        // 如果没有status字段，从description中提取状态
        if (!status && assignment.description) {
          // 使用正则表达式提取状态
          const statusMatch = assignment.description.match(/【状态】(.*?)】/);
          if (statusMatch) {
            status = statusMatch[1];
          } else {
            status = 'active'; // 默认状态为active
          }
        } else if (!status) {
          status = 'active'; // 默认状态为active
        }

        return {
          ...assignment,
          status: status
        };
      });

      console.log('处理后的作业任务列表:', processedAssignments);

      // 过滤已结束的作业任务，只保留未结束的作业任务
      const activeAssignments = processedAssignments.filter(assignment =>
        assignment.status !== 'finished'
      );
      
      console.log('未结束的作业任务:', activeAssignments);
      setAssignments(activeAssignments);
      
      // 如果有作业任务，默认选择第一个
      if (activeAssignments.length > 0 && !filters.assignmentId) {
        console.log('选择第一个作业任务:', activeAssignments[0]);
        const firstAssignmentId = activeAssignments[0].id;
        
        // 同时更新lastSelectedAssignment，避免重复刷新
        setLastSelectedAssignment(firstAssignmentId);
        
        setFilters(prev => ({
          ...prev,
          assignmentId: firstAssignmentId
        }));
        
        // 加载该作业任务的作业
        fetchHomeworks({
          assignment_id: firstAssignmentId
        });
      } else if (activeAssignments.length === 0) {
        // 如果没有作业任务，清空作业列表
        console.log('没有未结束的作业任务，清空作业列表');
        setHomeworks([]);
        setLoading(false);
      }
    } catch (error) {
      console.error('获取作业任务列表失败:', error);
      message.error('获取作业任务列表失败');
      setHomeworks([]);
      setLoading(false);
    } finally {
      setAssignmentsLoading(false);
    }
  };
  
  // 获取作业列表
  const fetchHomeworks = async (params = {}) => {
    // 如果没有选择作业任务，不进行请求
    if (!params.assignment_id && !filters.assignmentId) {
      console.log('没有选择作业任务，不请求作业列表');
      setHomeworks([]);
      setLoading(false);
      return;
    }
    
    try {
      setLoading(true);
      setError(null);
      
      // 构建API参数
      const apiParams = {
        page: params.page || pagination.current,
        limit: params.pageSize || pagination.pageSize,
        status: params.status || filters.status,
        search: params.search || filters.search,
        assignment_id: params.assignment_id || filters.assignmentId
      };
      
      // 添加日期范围
      if (params.dateRange || filters.dateRange) {
        const dateRange = params.dateRange || filters.dateRange;
        if (dateRange && dateRange.length === 2) {
          apiParams.start_date = dateRange[0].format('YYYY-MM-DD');
          apiParams.end_date = dateRange[1].format('YYYY-MM-DD');
        }
      }
      
      console.log('请求待批改作业列表，参数:', apiParams);
      
      // 获取数据
      const data = await getHomeworks(apiParams);
      console.log('待批改作业列表响应:', data);
      
      // 检查响应数据
      if (data && Array.isArray(data.items)) {
        setHomeworks(data.items);
        setPagination({
          ...pagination,
          current: params.page || pagination.current,
          total: data.total || data.items.length
        });
      } else if (Array.isArray(data)) {
        setHomeworks(data);
        setPagination({
          ...pagination,
          current: params.page || pagination.current,
          total: data.length
        });
      } else if (data && typeof data === 'object' && data.id) {
        setHomeworks([data]);
        setPagination({
          ...pagination,
          current: 1,
          total: 1
        });
      } else {
        console.warn('待批改作业列表响应格式不符合预期:', data);
        setHomeworks([]);
        setPagination({
          ...pagination,
          current: 1,
          total: 0
        });
        setError('获取待批改作业列表格式异常，暂无数据');
        message.warning('暂无待批改作业数据');
      }
    } catch (error) {
      console.error('获取待批改作业列表失败:', error);
      setError(`获取待批改作业列表失败: ${error.message || '未知错误'}`);
      setHomeworks([]);
      setPagination({
        ...pagination,
        current: 1,
        total: 0
      });
      message.error('获取待批改作业列表失败，请稍后重试');
    } finally {
      // 确保无论如何都设置loading为false
      setLoading(false);
      console.log('待批改作业列表加载完成，loading状态已重置');
    }
  };
  
  // 首次加载时获取作业任务列表和作业列表
  useEffect(() => {
    fetchAssignments();
  }, []);
  
  // 处理location.state中的refresh参数
  useEffect(() => {
    if (location.state && location.state.refresh) {
      console.log('检测到需要刷新待批改作业列表');
      
      // 检查是否需要强制刷新
      const forceRefresh = location.state.forceRefresh === true;
      const timestamp = location.state.timestamp || new Date().getTime();
      console.log(`强制刷新: ${forceRefresh}, 时间戳: ${timestamp}`);
      
      // 先获取作业任务列表
      fetchAssignments();
      
      // 同时刷新当前选中的作业任务的作业列表
      if (filters.assignmentId) {
        console.log('同时刷新当前作业任务的作业列表:', filters.assignmentId);
        
        // 更新lastSelectedAssignment，避免刷新后重复请求
        setLastSelectedAssignment(filters.assignmentId);
        
        fetchHomeworks({
          assignment_id: filters.assignmentId,
          page: 1,  // 重置到第一页
          cache: false,  // 禁用缓存
          _t: timestamp  // 添加时间戳参数，确保不使用缓存
        });
      }
      
      // 清除state，避免重复刷新
      navigate(location.pathname, { replace: true, state: {} });
    }
  }, [location.state, navigate, location.pathname, filters.assignmentId]);
  
  // 处理表格变化
  const handleTableChange = (pagination, filters) => {
    fetchHomeworks({
      page: pagination.current,
      pageSize: pagination.pageSize,
      ...filters
    });
  };
  
  // 处理搜索
  const handleSearch = () => {
    fetchHomeworks({ page: 1, ...filters });
  };
  
  // 处理查看详情
  const handleViewDetail = (id) => {
    navigate(`/homework/${id}?from=today`);
  };
  
  // 处理刷新
  const handleRefresh = () => {
    console.log('手动刷新待批改作业列表');
    fetchAssignments();
    
    // 同时刷新当前选中的作业任务的作业列表
    if (filters.assignmentId) {
      console.log('同时刷新当前作业任务的作业列表:', filters.assignmentId);
      
      // 更新lastSelectedAssignment，避免刷新后重复请求
      setLastSelectedAssignment(filters.assignmentId);
      
      fetchHomeworks({
        assignment_id: filters.assignmentId,
        page: 1,  // 重置到第一页
        cache: false  // 禁用缓存
      });
    }
    
    message.info('正在刷新待批改作业列表...');
  };
  
  // 处理作业任务选择变更
  const handleAssignmentChange = (value) => {
    // 如果选择的是同一个作业任务，不重复请求
    if (value === lastSelectedAssignment) {
      console.log('选择了相同的作业任务，跳过刷新');
      return;
    }
    
    // 更新上次选择的作业任务
    setLastSelectedAssignment(value);
    
    setFilters(prev => ({
      ...prev,
      assignmentId: value
    }));
    
    // 获取作业列表，只刷新一次
    fetchHomeworks({
      assignment_id: value,
      page: 1
    });
  };
  
  // 处理删除作业
  const handleDelete = async (id) => {
    try {
      await deleteHomework(id);
      message.success('作业删除成功');
      fetchHomeworks();
    } catch (error) {
      console.error('删除作业失败:', error);
      message.error(`删除作业失败: ${error.message || '未知错误'}`);
    }
  };
  
  // 获取状态标签
  const getStatusTag = (status) => {
    switch (status) {
      case 'submitted':
        return <Tag color="blue" icon={<ClockCircleOutlined />}>已提交</Tag>;
      case 'grading':
      case 'correcting':
        return <Tag color="orange" icon={<SyncOutlined spin />}>批改中</Tag>;
      case 'graded':
      case 'corrected':
        return <Tag color="green" icon={<CheckCircleOutlined />}>已批改</Tag>;
      default:
        return <Tag color="default">未知状态</Tag>;
    }
  };
  
  // 移动端卡片式布局渲染
  const renderMobileCards = () => {
    if (homeworks.length === 0) {
      return (
        <div style={{ textAlign: 'center', padding: '40px 16px' }}>
          <FileTextOutlined style={{ fontSize: '48px', color: '#d9d9d9', marginBottom: '16px' }} />
          <div style={{ fontSize: '16px', color: '#666666', marginBottom: '8px' }}>
            暂无作业
          </div>
          <div style={{ fontSize: '14px', color: '#999999' }}>
            当前筛选条件下没有找到作业
          </div>
        </div>
      );
    }

    return (
      <List
        dataSource={homeworks}
        renderItem={(homework) => (
          <List.Item style={{ padding: 0, marginBottom: '12px' }}>
            <Card
              hoverable
              style={{
                width: '100%',
                borderRadius: '12px',
                border: '1px solid #e8e8e8',
                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)'
              }}
              bodyStyle={{ padding: '16px' }}
            >
              {/* 卡片头部 */}
              <div style={{ marginBottom: '12px' }}>
                <Row justify="space-between" align="top">
                  <Col flex="1">
                    <div style={{
                      fontSize: '16px',
                      fontWeight: '600',
                      color: '#262626',
                      marginBottom: '4px',
                      lineHeight: '1.4'
                    }}>
                      {homework.title}
                    </div>
                    <Space size="small" wrap>
                      {getStatusTag(homework.status)}
                      {homework.class_name && (
                        <Tag color="blue">
                          {homework.class_name}
                        </Tag>
                      )}
                    </Space>
                  </Col>
                </Row>
              </div>

              {/* 卡片内容 */}
              <Row gutter={[12, 8]} style={{ marginBottom: '12px' }}>
                <Col span={12}>
                  <div style={{ fontSize: '12px', color: '#8c8c8c' }}>学生</div>
                  <div style={{ fontSize: '14px', color: '#595959', fontWeight: '500' }}>
                    <UserOutlined style={{ marginRight: '4px' }} />
                    {user.is_teacher ?
                      (homework.student_name || `学生ID: ${homework.student_id}`) :
                      (homework.assignment_title || homework.title || '-')
                    }
                  </div>
                </Col>
                <Col span={12}>
                  <div style={{ fontSize: '12px', color: '#8c8c8c' }}>提交时间</div>
                  <div style={{ fontSize: '14px', color: '#595959', fontWeight: '500' }}>
                    <CalendarOutlined style={{ marginRight: '4px' }} />
                    {homework.created_at ?
                      new Date(homework.created_at).toLocaleDateString() :
                      '-'
                    }
                  </div>
                </Col>
              </Row>

              {/* 操作按钮 */}
              <div style={{ display: 'flex', gap: '8px', justifyContent: 'flex-end' }}>
                <Button
                  type="primary"
                  size="small"
                  icon={<FileTextOutlined />}
                  onClick={() => handleViewDetail(homework.id)}
                  style={{
                    borderRadius: '6px',
                    height: '36px',
                    fontSize: '14px',
                    fontWeight: '500',
                    touchAction: 'manipulation'
                  }}
                >
                  查看
                </Button>

                {user.is_teacher && homework.status === 'submitted' && (
                  <Button
                    type="primary"
                    size="small"
                    icon={<CheckCircleOutlined />}
                    onClick={() => navigate(`/homework/${homework.id}?from=today`, { state: { startCorrection: true } })}
                    style={{
                      borderRadius: '6px',
                      height: '36px',
                      fontSize: '14px',
                      fontWeight: '500',
                      background: '#52c41a',
                      borderColor: '#52c41a',
                      touchAction: 'manipulation'
                    }}
                  >
                    批改
                  </Button>
                )}

                {user.is_teacher && (
                  <Popconfirm
                    title="确定删除该作业吗？"
                    onConfirm={() => handleDelete(homework.id)}
                    okText="确定"
                    cancelText="取消"
                  >
                    <Button
                      danger
                      size="small"
                      icon={<DeleteOutlined />}
                      style={{
                        borderRadius: '6px',
                        height: '36px',
                        fontSize: '14px',
                        touchAction: 'manipulation'
                      }}
                    >
                      删除
                    </Button>
                  </Popconfirm>
                )}
              </div>
            </Card>
          </List.Item>
        )}
      />
    );
  };

  // 表格列定义
  const columns = [
    {
      title: '标题',
      dataIndex: 'title',
      key: 'title',
      render: (text, record) => (
        <a href={`/homework/${record.id}`} onClick={(e) => {
          e.preventDefault();
          handleViewDetail(record.id);
        }}>{text}</a>
      )
    },
    {
      title: user.is_teacher ? '学生' : '作业来源',
      dataIndex: user.is_teacher ? 'student_name' : 'assignment_title',
      key: user.is_teacher ? 'student_name' : 'assignment_title',
      render: (text, record) => {
        if (user.is_teacher) {
          return text || `学生ID: ${record.student_id}`;
        } else {
          return text || record.title || '-';
        }
      }
    },
    {
      title: '班级',
      dataIndex: 'class_name',
      key: 'class_name',
      render: (text, record) => {
        if (text) {
          return text;
        } else if (record.class_id) {
          return `班级ID: ${record.class_id}`;
        } else {
          return '-';
        }
      }
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => getStatusTag(status)
    },
    {
      title: '提交时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date) => new Date(date).toLocaleString()
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Button
            type="primary"
            icon={<FileTextOutlined />}
            onClick={() => handleViewDetail(record.id)}
          >
            查看
          </Button>
          {user.is_teacher && record.status === 'submitted' && (
            <Button
              type="primary"
              icon={<CheckCircleOutlined />}
              onClick={() => navigate(`/homework/${record.id}?from=today`, { state: { startCorrection: true } })}
            >
              批改
            </Button>
          )}
          {user.is_teacher && (
            <Popconfirm
              title="确定删除该作业吗？"
              onConfirm={() => handleDelete(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button
                type="primary"
                danger
                icon={<DeleteOutlined />}
              >
                删除
              </Button>
            </Popconfirm>
          )}
        </Space>
      )
    }
  ];

  // 渲染内容
  const renderContent = () => {
    // 如果正在加载作业任务列表
    if (assignmentsLoading) {
      return (
        <div style={{ textAlign: 'center', padding: '50px 0' }}>
          <Spin tip="加载作业任务列表..." />
        </div>
      );
    }
    
    // 如果没有作业任务
    if (assignments.length === 0) {
      return (
        <Empty 
          description="没有作业任务" 
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        >
          <Button type="primary" onClick={() => navigate('/homework-assignment/create')}>
            创建作业任务
          </Button>
        </Empty>
      );
    }
    
    // 如果有作业任务但没有作业
    return (
      <>
        {/* 筛选工具栏 */}
        <Card
          className="teacher-page"
          style={{
            marginBottom: isMobile ? '16px' : '24px',
            marginTop: '16px',
            borderRadius: '12px',
            border: '1px solid #E8E8E8'
          }}
          bodyStyle={{ padding: isMobile ? '16px' : '20px' }}
        >
          {isMobile ? (
            // 移动端筛选器布局
            <div>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                marginBottom: '16px'
              }}>
                <SearchOutlined style={{ color: '#1890ff' }} />
                <span style={{ fontWeight: 500, color: '#262626' }}>筛选条件</span>
              </div>

              <Row gutter={[12, 12]}>
                <Col span={24}>
                  <div style={{ marginBottom: '4px' }}>
                    <span style={{ fontSize: '14px', color: '#666' }}>作业任务</span>
                  </div>
                  <Select
                    placeholder="选择作业任务"
                    style={{ width: '100%' }}
                    value={filters.assignmentId}
                    onChange={handleAssignmentChange}
                    loading={loading}
                    size="large"
                  >
                    {assignments.map(assignment => (
                      <Option key={assignment.id} value={assignment.id}>
                        {assignment.title}
                      </Option>
                    ))}
                  </Select>
                </Col>

                <Col span={12}>
                  <div style={{ marginBottom: '4px' }}>
                    <span style={{ fontSize: '14px', color: '#666' }}>搜索</span>
                  </div>
                  <Input
                    placeholder="搜索作业标题或学生"
                    value={filters.search}
                    onChange={(e) => setFilters({ ...filters, search: e.target.value })}
                    style={{ width: '100%' }}
                    size="large"
                  />
                </Col>

                <Col span={12}>
                  <div style={{ marginBottom: '4px' }}>
                    <span style={{ fontSize: '14px', color: '#666' }}>状态</span>
                  </div>
                  <Select
                    placeholder="选择状态"
                    style={{ width: '100%' }}
                    value={filters.status}
                    onChange={(value) => setFilters({ ...filters, status: value })}
                    size="large"
                  >
                    <Option value="">全部</Option>
                    <Option value="submitted">待批改</Option>
                    <Option value="grading">批改中</Option>
                    <Option value="graded">已批改</Option>
                  </Select>
                </Col>

                <Col span={12}>
                  <Button
                    type="primary"
                    icon={<SearchOutlined />}
                    onClick={handleSearch}
                    block
                    size="large"
                    style={{ height: '44px' }}
                  >
                    搜索
                  </Button>
                </Col>

                <Col span={12}>
                  <Button
                    onClick={handleRefresh}
                    block
                    size="large"
                    style={{ height: '44px' }}
                  >
                    刷新
                  </Button>
                </Col>
              </Row>
            </div>
          ) : (
            // 桌面端筛选器布局
            <Space style={{ marginBottom: 16 }}>
              <Select
                placeholder="选择作业任务"
                style={{ width: 200 }}
                value={filters.assignmentId}
                onChange={handleAssignmentChange}
                loading={loading}
              >
                {assignments.map(assignment => (
                  <Option key={assignment.id} value={assignment.id}>
                    {assignment.title}
                  </Option>
                ))}
              </Select>

              <Input
                placeholder="搜索作业标题或学生"
                value={filters.search}
                onChange={(e) => setFilters({ ...filters, search: e.target.value })}
                style={{ width: 200 }}
              />

              <Select
                placeholder="选择状态"
                style={{ width: 120 }}
                value={filters.status}
                onChange={(value) => setFilters({ ...filters, status: value })}
              >
                <Option value="">全部</Option>
                <Option value="submitted">待批改</Option>
                <Option value="grading">批改中</Option>
                <Option value="graded">已批改</Option>
              </Select>

              <Button
                type="primary"
                icon={<SearchOutlined />}
                onClick={handleSearch}
              >
                搜索
              </Button>

              <Button onClick={handleRefresh}>
                刷新
              </Button>
            </Space>
          )}
        </Card>
        
        {/* 错误提示 */}
        {error && (
          <Alert
            message="获取数据时出现错误"
            description={error}
            type="error"
            showIcon
            style={{ marginBottom: 16 }}
            action={
              <Button size="small" onClick={handleRefresh}>
                重试
              </Button>
            }
          />
        )}
        
        {/* 数据表格 */}
        <div className="teacher-page">
          <Spin spinning={loading}>
            {homeworks.length > 0 ? (
              isMobile ? (
                // 移动端卡片式布局
                <div style={{ padding: '0 4px' }}>
                  {renderMobileCards()}
                  {/* 移动端分页 */}
                  {pagination.total > pagination.pageSize && (
                    <div style={{
                      textAlign: 'center',
                      marginTop: '20px',
                      padding: '16px 0'
                    }}>
                      <Space>
                        <Button
                          disabled={pagination.current === 1}
                          onClick={() => handleTableChange({
                            current: pagination.current - 1,
                            pageSize: pagination.pageSize
                          })}
                          style={{ height: '40px' }}
                        >
                          上一页
                        </Button>
                        <span style={{
                          padding: '0 16px',
                          fontSize: '14px',
                          color: '#666'
                        }}>
                          {pagination.current} / {Math.ceil(pagination.total / pagination.pageSize)}
                        </span>
                        <Button
                          disabled={pagination.current >= Math.ceil(pagination.total / pagination.pageSize)}
                          onClick={() => handleTableChange({
                            current: pagination.current + 1,
                            pageSize: pagination.pageSize
                          })}
                          style={{ height: '40px' }}
                        >
                          下一页
                        </Button>
                      </Space>
                    </div>
                  )}
                </div>
              ) : (
                // 桌面端表格布局
                <Table
                  columns={columns}
                  dataSource={homeworks}
                  rowKey="id"
                  pagination={pagination}
                  onChange={handleTableChange}
                  loading={false}
                />
              )
            ) : (
              <Empty description={
                loading ? "加载中..." : "暂无今日作业"
              } />
            )}
          </Spin>
        </div>
      </>
    );
  };

  return (
    <div className={`pending-review-homeworks-container teacher-page ${isMobile ? 'mobile' : 'desktop'}`}>
      <div style={{
        marginBottom: isMobile ? '16px' : '24px',
        padding: isMobile ? '0 4px' : '0'
      }}>
        <Title level={isMobile ? 4 : 3} style={{ marginBottom: '8px' }}>
          📚 今日作业
        </Title>
        <Text type="secondary" style={{ fontSize: isMobile ? '14px' : '16px' }}>
          显示所有未结束作业任务的作业提交，包括待批改和批改中的作业
        </Text>
      </div>

      {renderContent()}
    </div>
  );
};

export default PendingReviewHomeworks; 