import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Row, Col, Card, Typography, Button, Input,
  List, Avatar, message, Spin, Drawer
} from 'antd';
import AIAssistant from '../components/AIAssistant';
import {
  UploadOutlined, FileTextOutlined, BookOutlined,
  CheckCircleOutlined, SyncOutlined, BarChartOutlined,
  RobotOutlined, UserOutlined, TeamOutlined, SettingOutlined,
  ClockCircleOutlined, ExclamationCircleOutlined, TrophyOutlined,
  CalendarOutlined
} from '@ant-design/icons';
import { getHomeworks, getStudentHomeworkAssignments } from '../utils/api';

const { Title, Paragraph } = Typography;
const { Search } = Input;

// 模拟数据，当API请求失败时使用
const mockHomeworks = [
  {
    id: 1,
    title: "数学作业",
    status: "graded",
    student_name: "张三",
    created_at: new Date().toISOString()
  },
  {
    id: 2,
    title: "语文作业",
    status: "submitted",
    student_name: "李四",
    created_at: new Date().toISOString()
  }
];

const Home = ({ user, onLogout }) => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [recentHomeworks, setRecentHomeworks] = useState([]);
  const [studentStats, setStudentStats] = useState({});
  const [error, setError] = useState(null);
  const [aiDrawerVisible, setAiDrawerVisible] = useState(false);

  // 判断作业是否已结束（基于description中的状态标记）
  const isAssignmentEnded = (assignment) => {
    if (!assignment.description) return false;
    return assignment.description.includes('【状态】finished】');
  };

  // 计算学生作业统计数据
  const calculateStudentStats = (assignments) => {
    const unsubmittedAssignments = assignments.filter(item => item.submission_status === '未提交');
    const pending = unsubmittedAssignments.filter(item => !isAssignmentEnded(item)).length;
    const incomplete = unsubmittedAssignments.filter(item => isAssignmentEnded(item)).length;

    const submittedAssignments = assignments.filter(item => item.submission_status === '已提交');
    const submitted = submittedAssignments.filter(item => !isAssignmentEnded(item)).length;
    const makeup = submittedAssignments.filter(item => isAssignmentEnded(item)).length;

    const graded = assignments.filter(item => item.grading_status === '已批改').length;

    return { pending, incomplete, submitted, makeup, graded, total: assignments.length };
  };

  // 获取最近作业
  useEffect(() => {
    const fetchData = async () => {
      if (!user) {
        setLoading(false);
        return;
      }
      
      try {
        setLoading(true);
        setError(null);
        
        // 获取令牌，检查是否有效
        const token = localStorage.getItem('token');
        if (!token) {
          throw new Error("找不到认证令牌，请重新登录");
        }
        
        // 根据用户类型获取不同的数据
        if (user.is_teacher || user.is_admin) {
          // 教师和管理员获取作业数据
          try {
            const homeworksData = await getHomeworks({ limit: 5 });

            // 确保homeworksData是数组
            if (Array.isArray(homeworksData)) {
              setRecentHomeworks(homeworksData);
            } else if (homeworksData && Array.isArray(homeworksData.items)) {
              setRecentHomeworks(homeworksData.items);
            } else if (homeworksData && typeof homeworksData === 'object' && homeworksData.id) {
              setRecentHomeworks([homeworksData]);
            } else {
              setRecentHomeworks(mockHomeworks);
              message.warning("作业数据格式异常，显示模拟数据");
            }
          } catch (homeworkError) {
            console.error("获取作业数据失败:", homeworkError);
            setRecentHomeworks(mockHomeworks);
            message.warning("无法加载作业数据，显示模拟数据");
          }
        } else {
          // 学生获取作业任务数据
          try {
            const assignments = await getStudentHomeworkAssignments();
            console.log('获取到的学生作业任务:', assignments);

            // 计算统计数据
            const stats = calculateStudentStats(assignments);
            setStudentStats(stats);

            // 获取最近的作业任务作为展示
            const recent = assignments.slice(0, 5).map(assignment => ({
              id: assignment.homework_id || assignment.id, // 优先使用homework_id，如果没有则使用assignment_id
              assignment_id: assignment.id, // 保留作业任务ID
              homework_id: assignment.homework_id, // 作业ID
              title: assignment.title,
              status: assignment.submission_status === '已提交' ? 'submitted' : 'pending',
              student_name: user.username,
              created_at: assignment.created_at
            }));
            setRecentHomeworks(recent);
          } catch (studentError) {
            console.error("获取学生作业数据失败:", studentError);
            setRecentHomeworks(mockHomeworks);
            message.warning("无法加载作业数据，显示模拟数据");
          }
        }
      } catch (error) {
        console.error('获取数据失败:', error);
        setError(error.message || "获取数据失败，请稍后重试");
        
        // 使用模拟数据
        setRecentHomeworks(mockHomeworks);
        
        message.error('获取数据失败，请稍后刷新页面');
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, [user]);
  
  // 搜索处理
  const onSearch = (value) => {
    if (!value) return;
    navigate(`/homework?search=${encodeURIComponent(value)}`);
  };
  
  // 快捷操作处理
  const handleQuickAction = (action) => {
    console.log('Quick action:', action);
    
    switch (action) {
      case 'upload':
        navigate('/homework/upload');
        break;
      case 'submit':
        // 学生点击提交作业，跳转到作业管理页面
        navigate('/homework');
        break;
      case 'pending':
        // 跳转到作业管理页面并过滤待提交作业
        navigate('/homework', { state: { filter: 'pending' } });
        break;
      case 'incomplete':
        // 跳转到作业管理页面并过滤未完成作业
        navigate('/homework', { state: { filter: 'incomplete' } });
        break;
      case 'review':
        // 跳转到作业管理页面并过滤已批改作业
        navigate('/homework', { state: { filter: 'graded' } });
        break;
      case 'training':
        navigate('/training');
        break;
      case 'photo-solve':
        navigate('/photo-solve');
        break;
      case 'statistics':
        navigate('/statistics');
        break;
      case 'settings':
        navigate('/profile');
        break;
      case 'class':
        navigate('/class-management');
        break;
      case 'admin':
        navigate('/admin');
        break;
      case 'user':
        if (user.is_admin) {
          navigate('/user-management');
        } else if (user.is_teacher) {
          navigate('/teacher-user-management');
        }
        break;
      default:
        break;
    }
  };
  
  // 快捷操作项
  const quickActions = !user ? [] : user.is_admin
    ? [
        {
          key: 'admin',
          title: '管理后台',
          icon: <SettingOutlined />,
          color: '#ff4d4f'
        },
        {
          key: 'class',
          title: '班级管理',
          icon: <TeamOutlined />,
          color: '#52c41a'
        },
        {
          key: 'user',
          title: '用户管理',
          icon: <UserOutlined />,
          color: '#1890ff'
        },
        {
          key: 'statistics',
          title: '统计报表',
          icon: <BarChartOutlined />,
          color: '#faad14'
        }
      ]
    : user.is_teacher
    ? [
        {
          key: 'upload',
          title: '上传作业',
          icon: <UploadOutlined />,
          color: '#1890ff'
        },
        {
          key: 'class',
          title: '班级管理',
          icon: <TeamOutlined />,
          color: '#52c41a'
        },
        {
          key: 'user',
          title: '学生信息',
          icon: <UserOutlined />,
          color: '#722ed1'
        },
        {
          key: 'review',
          title: '批改结果',
          icon: <FileTextOutlined />,
          color: '#722ed1'
        },
        {
          key: 'statistics',
          title: '统计报表',
          icon: <BarChartOutlined />,
          color: '#faad14'
        }
      ]
    : [
        {
          key: 'pending',
          title: '待提交作业',
          subtitle: `${studentStats.pending || 0}份`,
          icon: '📝',
          color: '#FF9500',
          bgColor: 'rgba(255, 149, 0, 0.1)',
          description: '需要及时完成的作业'
        },
        {
          key: 'incomplete',
          title: '未完成作业',
          subtitle: `${studentStats.incomplete || 0}份`,
          icon: '⚠️',
          color: '#FF3B30',
          bgColor: 'rgba(255, 59, 48, 0.1)',
          description: '可以补交的作业'
        },
        {
          key: 'review',
          title: '查看批改',
          subtitle: `${studentStats.graded || 0}份已批改`,
          icon: '⭐',
          color: '#30D158',
          bgColor: 'rgba(48, 209, 88, 0.1)',
          description: '查看批改结果和成绩'
        },
        {
          key: 'training',
          title: '错题训练',
          subtitle: '强化练习',
          icon: '💪',
          color: '#AF52DE',
          bgColor: 'rgba(175, 82, 222, 0.1)',
          description: '针对性练习提升'
        },
        {
          key: 'submit',
          title: '作业管理',
          subtitle: `共${studentStats.total || 0}份作业`,
          icon: '📚',
          color: '#4A90E2',
          bgColor: 'rgba(74, 144, 226, 0.1)',
          description: '查看所有作业任务'
        },
        {
          key: 'photo-solve',
          title: '拍照解题',
          subtitle: 'AI智能解题',
          icon: '📸',
          color: '#FF6B6B',
          bgColor: 'rgba(255, 107, 107, 0.1)',
          description: '拍照上传题目，AI为您解答'
        },
        {
          key: 'settings',
          title: '个人中心',
          subtitle: '设置与信息',
          icon: '👤',
          color: '#34C759',
          bgColor: 'rgba(52, 199, 89, 0.1)',
          description: '个人信息和设置'
        }
      ];

  return (
    <div style={{ padding: '20px 0' }}>
      <Title level={2}>欢迎回来，{user?.username || '用户'}！</Title>
      <Paragraph>智教云端智能辅导平台，让学习更高效！</Paragraph>
      
      <Spin spinning={loading}>
        {/* 搜索框 */}
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <Search
              placeholder="搜索作业或批改记录..."
              allowClear
              enterButton="搜索"
              size="large"
              onSearch={onSearch}
            />
          </Col>
        </Row>
        
        {error && (
          <Row gutter={[16, 16]} style={{ marginTop: 20 }}>
            <Col span={24}>
              <Card style={{ backgroundColor: '#fff2f0', borderColor: '#ffccc7' }}>
                <div>数据加载出错: {error}</div>
                <Button 
                  type="primary" 
                  style={{ marginTop: 10 }}
                  onClick={() => window.location.reload()}
                >
                  刷新页面
                </Button>
              </Card>
            </Col>
          </Row>
        )}
        
        {/* 快捷操作 */}
        <div style={{ marginTop: 30 }}>
          <Title level={4}>
            {user && !user.is_teacher && !user.is_admin ? '📚 学习中心' : '快捷操作'}
          </Title>
          <Row gutter={[16, 16]}>
            {quickActions.map((action) => (
              <Col xs={12} sm={8} md={6} lg={6} key={action.key}>
                <Card
                  hoverable
                  onClick={() => handleQuickAction(action.key)}
                  style={{
                    textAlign: 'center',
                    background: action.bgColor || '#ffffff',
                    border: action.bgColor ? `1px solid ${action.color}20` : '1px solid #d9d9d9',
                    borderRadius: '12px',
                    transition: 'all 0.3s ease'
                  }}
                  className="quick-action-card"
                  bodyStyle={{ padding: '20px' }}
                >
                  <div style={{
                    fontSize: action.bgColor ? 28 : 32,
                    color: action.color,
                    marginBottom: action.bgColor ? 8 : 10
                  }}>
                    {action.icon}
                  </div>
                  <div style={{
                    fontSize: '16px',
                    fontWeight: 600,
                    color: action.color,
                    marginBottom: action.subtitle ? '4px' : '0'
                  }}>
                    {action.title}
                  </div>
                  {action.subtitle && (
                    <div style={{
                      fontSize: '12px',
                      color: action.color,
                      opacity: 0.8,
                      marginBottom: '8px'
                    }}>
                      {action.subtitle}
                    </div>
                  )}
                  {action.description && (
                    <div style={{
                      fontSize: '11px',
                      color: '#666666',
                      lineHeight: 1.3
                    }}>
                      {action.description}
                    </div>
                  )}
                </Card>
              </Col>
            ))}
          </Row>
        </div>
        
        {/* 最近作业 */}
        <div style={{ marginTop: 30 }}>
          <Title level={4}>最近作业</Title>
          <List
            itemLayout="horizontal"
            dataSource={Array.isArray(recentHomeworks) ? recentHomeworks : (recentHomeworks?.items || [])}
            renderItem={item => (
              <List.Item
                key={item.assignment_id}
                onClick={() => {
                  if (item.homework_id) {
                    // 如果已提交作业，跳转到作业详情页面
                    navigate(`/homework/${item.homework_id}`);
                  } else {
                    // 如果未提交作业，可以跳转到作业上传页面或显示提示
                    message.info('该作业尚未提交，请先提交作业');
                  }
                }}
                style={{ cursor: 'pointer' }}
              >
                <List.Item.Meta
                  avatar={<Avatar icon={<FileTextOutlined />} style={{ 
                    backgroundColor: 
                      item.status === 'graded' ? '#52c41a' : 
                      item.status === 'grading' ? '#faad14' : '#1890ff' 
                  }} />}
                  title={item.title}
                  description={`状态: ${
                    item.status === 'graded' ? '已批改' : 
                    item.status === 'grading' ? '批改中' : '已提交'
                  } | 学生: ${item.student_name || '未知'} | 提交时间: ${new Date(item.created_at).toLocaleString()}`}
                />
              </List.Item>
            )}
          />
        </div>
        
        {/* AI助手提示 */}
        <Card style={{ marginTop: 30, textAlign: 'center', background: '#f0f7ff' }}>
          <Row align="middle" gutter={16}>
            <Col xs={24} sm={4}>
              <RobotOutlined style={{ fontSize: 48, color: '#1890ff' }} />
            </Col>
            <Col xs={24} sm={16} style={{ textAlign: 'left' }}>
              <Title level={4}>AI智能助手随时为您服务</Title>
              <Paragraph>
                有任何问题，可以点击右上角的"AI助手"按钮，获取智能解答和辅导。
              </Paragraph>
            </Col>
            <Col xs={24} sm={4}>
              <Button
                type="primary"
                size="large"
                onClick={() => {
                  console.log('点击立即咨询按钮');
                  setAiDrawerVisible(true);
                }}
              >
                立即咨询
              </Button>
            </Col>
          </Row>
        </Card>
      </Spin>

      {/* AI助手抽屉 */}
      <Drawer
        title="AI智能助手"
        placement="right"
        onClose={() => setAiDrawerVisible(false)}
        open={aiDrawerVisible}
        width={window.innerWidth <= 768 ? '100%' : 400}
        style={{ zIndex: 1000 }}
      >
        <AIAssistant user={user} />
      </Drawer>
    </div>
  );
};

export default Home;