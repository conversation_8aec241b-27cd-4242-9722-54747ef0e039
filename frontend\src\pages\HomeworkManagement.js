import React, { useState, useEffect, createContext } from 'react';
import { useNavigate, useLocation, Routes, Route } from 'react-router-dom';
import {
  Layout, Menu, Breadcrumb, Typography, Card, Row, Col, Select, Space, Button, message, Tabs
} from 'antd';
import {
  UploadOutlined, FileTextOutlined, PlusOutlined, AppstoreOutlined, CloudUploadOutlined,
  BarChartOutlined, CheckCircleOutlined, CalendarOutlined, ExclamationCircleOutlined,
  FilterOutlined, ReloadOutlined
} from '@ant-design/icons';
import HomeworkUpload from '../components/HomeworkUpload';
import HomeworkDetail from '../components/HomeworkDetail';
import HomeworkAssignmentCreate from '../components/HomeworkAssignmentCreate';
import HomeworkAssignmentList from '../components/HomeworkAssignmentList';
import StudentHomeworkUpload from '../components/StudentHomeworkUpload';
import HomeworkAssignmentDetail from '../components/HomeworkAssignmentDetail';
import HomeworkUploadOptions from '../components/HomeworkUploadOptions';
import HomeworkStatistics from '../components/HomeworkStatistics';
import HomeworkAssignmentProgress from '../components/HomeworkAssignmentProgress';
import PendingReviewHomeworks from '../components/PendingReviewHomeworks';
import HomeworkCalendar from '../components/HomeworkCalendar';
import FinishedHomeworks from '../components/FinishedHomeworks';
import StudentHomeworkAssignmentList from '../components/StudentHomeworkAssignmentList';
import StudentSubmitChoice from '../components/StudentSubmitChoice';
import StudentHomeworkHistory from '../components/student/StudentHomeworkHistory';
import StudentHomeworkReview from '../components/student/StudentHomeworkReview';
import StudentHomeworkDetail from '../components/student/StudentHomeworkDetail';

const { Sider, Content } = Layout;
const { Title } = Typography;
const { ItemGroup } = Menu;
const { Option } = Select;

// 创建筛选上下文
const FilterContext = createContext();

// 筛选组件
const SystemHomeworkFilter = ({ user, onFilterChange }) => {
  const [schools, setSchools] = useState([]);
  const [grades, setGrades] = useState([]);
  const [loading, setLoading] = useState(false);
  const [filters, setFilters] = useState({
    schoolId: null,
    gradeId: null
  });

  // 是否为超级管理员
  const isSuperAdmin = user.is_admin && user.role === '超级管理员';



  // 获取学校列表
  const fetchSchools = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/schools', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      if (response.ok) {
        const data = await response.json();
        setSchools(data);

        // 设置默认学校为四川省双流中学（ID=1）
        const defaultSchool = data.find(school => school.id === 1);

        if (defaultSchool) {
          const defaultFilters = {
            schoolId: 1,
            gradeId: null,
            gradeName: null
          };
          setFilters(defaultFilters);
          fetchGrades(1);
          onFilterChange(defaultFilters);
        } else {
          // 过滤掉ID无效的学校
          const validSchools = data.filter(school =>
            school.id !== undefined &&
            school.id !== null &&
            school.name
          );

          // 查找四川省双流中学
          const shuangliuSchool = validSchools.find(school =>
            school.name.includes('双流中学')
          );

          let defaultSchool = null;
          if (shuangliuSchool) {
            defaultSchool = shuangliuSchool;
          } else if (validSchools.length > 0) {
            defaultSchool = validSchools[0];
          }

          if (defaultSchool) {
            const defaultFilters = {
              schoolId: defaultSchool.id,
              gradeId: null,
              gradeName: null
            };
            setFilters(defaultFilters);
            fetchGrades(defaultSchool.id);
            onFilterChange(defaultFilters);
          }
        }
      }
    } catch (error) {
      console.error('获取学校列表失败:', error);
      message.error('获取学校列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 年级排序函数
  const sortGrades = (grades) => {
    const gradeOrder = {
      // 小学
      '小学一年级': 1, '小学二年级': 2, '小学三年级': 3,
      '小学四年级': 4, '小学五年级': 5, '小学六年级': 6,
      // 初中
      '初中一年级': 7, '初中二年级': 8, '初中三年级': 9,
      '初一': 7, '初二': 8, '初三': 9,
      // 高中
      '高中一年级': 10, '高中二年级': 11, '高中三年级': 12,
      '高一': 10, '高二': 11, '高三': 12,
      // 其他常见格式
      '一年级': 1, '二年级': 2, '三年级': 3, '四年级': 4, '五年级': 5, '六年级': 6,
      '七年级': 7, '八年级': 8, '九年级': 9,
      '十年级': 10, '十一年级': 11, '十二年级': 12
    };

    return grades.sort((a, b) => {
      const orderA = gradeOrder[a.name] || 999;
      const orderB = gradeOrder[b.name] || 999;
      return orderA - orderB;
    });
  };

  // 获取年级列表
  const fetchGrades = async (schoolId) => {
    if (!schoolId) {
      setGrades([]);
      return;
    }

    try {
      const response = await fetch(`/api/admin/classes/grades?school_id=${schoolId}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      if (response.ok) {
        const data = await response.json();
        // 将年级名称转换为带ID的对象
        const gradeOptions = data.map((gradeName, index) => ({
          id: index + 1,
          name: gradeName
        }));
        // 对年级进行排序
        const sortedGrades = sortGrades(gradeOptions);
        setGrades(sortedGrades);
      } else {
        setGrades([]);
      }
    } catch (error) {
      console.error('获取年级列表失败:', error);
      message.error('获取年级列表失败');
      // 设置默认年级列表
      const defaultGrades = [
        { id: 1, name: '小学一年级' },
        { id: 2, name: '小学二年级' },
        { id: 3, name: '小学三年级' },
        { id: 4, name: '小学四年级' },
        { id: 5, name: '小学五年级' },
        { id: 6, name: '小学六年级' },
        { id: 7, name: '初中一年级' },
        { id: 8, name: '初中二年级' },
        { id: 9, name: '初中三年级' },
        { id: 10, name: '高中一年级' },
        { id: 11, name: '高中二年级' },
        { id: 12, name: '高中三年级' }
      ];
      setGrades(defaultGrades);
    }
  };



  // 初始化数据
  useEffect(() => {
    if (isSuperAdmin) {
      fetchSchools(); // fetchSchools内部会设置默认学校
    } else {
      // 学校管理员直接获取年级
      if (user.school_id) {
        fetchGrades(user.school_id);
        const defaultFilters = {
          schoolId: user.school_id,
          gradeId: null,
          gradeName: null
        };
        setFilters(defaultFilters);
        onFilterChange(defaultFilters);
      }
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user, isSuperAdmin]);

  // 处理学校变化
  const handleSchoolChange = (value) => {
    const newFilters = {
      schoolId: value,
      gradeId: null,
      gradeName: null
    };
    setFilters(newFilters);
    setGrades([]);

    if (value) {
      fetchGrades(value);
    }

    onFilterChange(newFilters);
  };

  // 处理年级变化
  const handleGradeChange = (value) => {
    const selectedGrade = grades.find(g => g.id === value);
    const newFilters = {
      ...filters,
      gradeId: value,
      gradeName: selectedGrade?.name
    };
    setFilters(newFilters);

    onFilterChange(newFilters);
  };

  // 重置筛选
  const handleReset = () => {
    let defaultSchoolId = user.school_id;

    if (isSuperAdmin) {
      // 超级管理员：使用四川省双流中学（ID=1）作为默认
      const defaultSchool = schools.find(school => school.id === 1);
      defaultSchoolId = defaultSchool ? 1 : (schools.length > 0 ? schools[0].id : null);
    }

    const resetFilters = {
      schoolId: defaultSchoolId,
      gradeId: null,
      gradeName: null
    };
    setFilters(resetFilters);
    setGrades([]);

    // 重新获取年级数据
    if (defaultSchoolId) {
      fetchGrades(defaultSchoolId);
    }

    onFilterChange(resetFilters);
  };

  return (
    <Card
      title={
        <Space>
          <FilterOutlined />
          筛选条件
        </Space>
      }
      size="small"
      style={{ marginBottom: 16 }}
    >
      <Row gutter={16}>
        {/* 超级管理员显示学校筛选 */}
        {isSuperAdmin && (
          <Col span={6}>
            <Select
              placeholder="选择学校"
              style={{ width: '100%' }}
              value={filters.schoolId}
              onChange={handleSchoolChange}
              loading={loading}
              allowClear
            >
              {schools
                .filter(school =>
                  school.id !== undefined &&
                  school.id !== null &&
                  school.name
                )
                .map(school => (
                  <Option key={school.id} value={school.id}>
                    {school.name}
                  </Option>
                ))
              }
            </Select>
          </Col>
        )}

        <Col span={isSuperAdmin ? 8 : 12}>
          <Select
            placeholder="选择年级"
            style={{ width: '100%' }}
            value={filters.gradeId}
            onChange={handleGradeChange}
            disabled={isSuperAdmin && !filters.schoolId}
            allowClear
          >
            {grades.map(grade => (
              <Option key={grade.id} value={grade.id}>
                {grade.name}
              </Option>
            ))}
          </Select>
        </Col>

        <Col span={isSuperAdmin ? 8 : 12}>
          <Button
            icon={<ReloadOutlined />}
            onClick={handleReset}
          >
            重置
          </Button>
        </Col>
      </Row>
    </Card>
  );
};

const HomeworkManagement = ({ user, onLogout, isSystemLevel = false }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [searchParams] = useSearchParams();
  const [selectedKey, setSelectedKey] = useState(user.is_teacher || isSystemLevel ? '7' : 'student-assignments');
  const [systemFilters, setSystemFilters] = useState({
    schoolId: null,
    gradeId: null,
    classId: null
  });

  // 移动端检测
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);
  
  // 根据当前路径设置选中的菜单项
  useEffect(() => {
    const path = location.pathname;
    if (path.includes('/upload/batch')) {
      setSelectedKey('2');
    } else if (path.includes('/upload/single')) {
      setSelectedKey('5');
    } else if (path.includes('/create')) {
      setSelectedKey('3');
    } else if (path.includes('/assignment-list')) {
      setSelectedKey('4');
    } else if (path.includes('/statistics')) {
      setSelectedKey('6');
    } else if (path.includes('/pending-review')) {
      setSelectedKey('7');
    } else if (path.includes('/calendar')) {
      setSelectedKey('8');
    } else if (path.includes('/wrong-questions')) {
      setSelectedKey('9');
    } else if (path.includes('/feedback')) {
      setSelectedKey('10');
    } else if (path.includes('/student-assignments')) {
      setSelectedKey('student-assignments');
    } else if (path.match(/\/homework\/\d+/) || path.match(/\/system-homework\/\d+/)) {
      // 根据查询参数决定选中哪个菜单
      const fromParam = searchParams.get('from');
      if (fromParam === 'today') {
        setSelectedKey('7'); // 今日作业
      } else if (fromParam === 'finished') {
        setSelectedKey('1'); // 往日作业
      } else {
        // 如果没有from参数，默认选择往日作业（保持原有行为）
        setSelectedKey('1');
      }
    } else if (path === '/homework' || path === '/system-homework') {
      // 如果是根路径，根据用户类型选择默认菜单
      setSelectedKey(user.is_teacher || isSystemLevel ? '7' : 'student-assignments');
    }
  }, [location.pathname, user.is_teacher, isSystemLevel, searchParams]);
  
  // 处理菜单点击
  const handleMenuClick = (e) => {
    const key = e.key;
    setSelectedKey(key);

    // 根据是否为系统级别构建不同的路由前缀
    const routePrefix = isSystemLevel ? '/system-homework' : '/homework';

    switch (key) {
      case '1':
        navigate(`${routePrefix}/finished`);
        break;
      case '2':
        navigate(`${routePrefix}/upload/batch`);
        break;
      case '3':
        // 传递筛选参数到创建页面
        navigate(`${routePrefix}/create`, {
          state: {
            filters: systemFilters,
            isSystemLevel: isSystemLevel
          }
        });
        break;
      case '4':
        navigate(`${routePrefix}/assignment-list`);
        break;
      case '5':
        navigate(`${routePrefix}/upload/single`);
        break;
      case '6':
        // 根据是否为系统级别决定跳转到不同的作业分析页面
        if (isSystemLevel) {
          navigate(`/system-homework-analysis`);
        } else {
          navigate(`/homework-analysis`);
        }
        break;
      case '7':
        navigate(`${routePrefix}/pending-review`);
        break;
      case '8':
        navigate(`${routePrefix}/calendar`);
        break;
      case '9':
        navigate(`${routePrefix}/wrong-questions`);
        break;
      case '10':
        navigate(`${routePrefix}/feedback`);
        break;
      case 'student-assignments':
        navigate(`${routePrefix}/student-assignments`);
        break;
      default:
        if (user.is_teacher || isSystemLevel) {
          navigate(`${routePrefix}/pending-review`);
        } else {
          navigate(`${routePrefix}/student-assignments`);
        }
    }
  };

  // 确保user存在
  if (!user) {
    return <div>加载中...</div>;
  }

  // 处理筛选变化
  const handleFilterChange = (filters) => {
    setSystemFilters(filters);
  };

  // 设置页面标题和面包屑
  const pageTitle = isSystemLevel ? '系统作业管理' : (user.is_teacher ? '作业管理' : '作业提交');

  return (
    <FilterContext.Provider value={systemFilters}>
      <Layout className="homework-management-layout">
        <Breadcrumb style={{ margin: '16px 0' }}>
          <Breadcrumb.Item>首页</Breadcrumb.Item>
          <Breadcrumb.Item>{pageTitle}</Breadcrumb.Item>
        </Breadcrumb>

        <Title level={2}>
          {pageTitle}
        </Title>

        {/* 系统级别显示筛选组件 */}
        {isSystemLevel && (
          <SystemHomeworkFilter
            user={user}
            onFilterChange={handleFilterChange}
          />
        )}



        <Layout className={isSystemLevel ? "system-homework-management-page" : "homework-management-page"} style={{ background: '#fff', padding: isMobile ? '0' : '24px 0' }}>
        {/* 移动端顶部导航 */}
        {isMobile && (user.is_teacher || isSystemLevel) && (
          <div className="mobile-top-nav">
            <Tabs
              activeKey={selectedKey}
              onChange={(key) => handleMenuClick({ key })}
              type="line"
              size="small"
              items={[
                {
                  key: '7',
                  label: '今日作业',
                  icon: <CheckCircleOutlined />
                },
                {
                  key: '3',
                  label: '添加任务',
                  icon: <PlusOutlined />
                },
                {
                  key: '4',
                  label: '作业任务',
                  icon: <AppstoreOutlined />
                },
                {
                  key: '5',
                  label: '单个上传',
                  icon: <CloudUploadOutlined />
                },
                {
                  key: '2',
                  label: '批量上传',
                  icon: <UploadOutlined />
                },
                {
                  key: '1',
                  label: '往日作业',
                  icon: <FileTextOutlined />
                },
                {
                  key: '6',
                  label: '作业分析',
                  icon: <BarChartOutlined />
                },
                {
                  key: '8',
                  label: '作业日历',
                  icon: <CalendarOutlined />
                },
                {
                  key: '9',
                  label: '错题收集',
                  icon: <ExclamationCircleOutlined />
                }
              ]}
            />
          </div>
        )}

        {/* 桌面端侧边菜单 */}
        {!isMobile && (user.is_teacher || isSystemLevel) && (
          <Sider width={200} style={{ background: '#fff' }}>
            <Menu
              mode="inline"
              selectedKeys={[selectedKey]}
              style={{ height: '100%' }}
              onClick={handleMenuClick}
            >
              <ItemGroup title="作业创建">
                <Menu.Item key="3" icon={<PlusOutlined />}>添加作业任务</Menu.Item>
                <Menu.Item key="4" icon={<AppstoreOutlined />}>作业任务</Menu.Item>
              </ItemGroup>

              <ItemGroup title={isSystemLevel ? "系统作业管理" : "作业管理"}>
                <Menu.Item key="7" icon={<CheckCircleOutlined />}>今日作业</Menu.Item>
                <Menu.Item key="5" icon={<CloudUploadOutlined />}>单个上传</Menu.Item>
                <Menu.Item key="2" icon={<UploadOutlined />}>批量上传</Menu.Item>
                <Menu.Item key="1" icon={<FileTextOutlined />}>往日作业</Menu.Item>
              </ItemGroup>

              <ItemGroup title="数据分析">
                <Menu.Item key="6" icon={<BarChartOutlined />}>作业分析</Menu.Item>
                <Menu.Item key="8" icon={<CalendarOutlined />}>作业日历</Menu.Item>
                <Menu.Item key="9" icon={<ExclamationCircleOutlined />}>错题收集</Menu.Item>
              </ItemGroup>
            </Menu>
          </Sider>
        )}

        <Content
          className={isMobile ? "mobile-content" : ""}
          style={{
            padding: (user.is_teacher || isSystemLevel) ? (isMobile ? '0' : '0 24px') : '0',
            minHeight: 280
          }}
        >
          <Routes>
            <Route index element={
              user.is_teacher || isSystemLevel ?
                <PendingReviewHomeworks user={user} isSystemLevel={isSystemLevel} /> :
                <StudentHomeworkAssignmentList user={user} />
            } />
            <Route path="upload/batch" element={<HomeworkUpload user={user} batchMode={true} isSystemLevel={isSystemLevel} />} />
            <Route path="upload/single" element={<StudentHomeworkUpload user={user} isSystemLevel={isSystemLevel} />} />
            <Route path="submit" element={<StudentSubmitChoice user={user} />} />
            {/* 学生专用的往日作业页面 */}
            {!user.is_teacher && !isSystemLevel && (
              <Route path="history" element={<StudentHomeworkHistory user={user} />} />
            )}
            {/* 学生专用的作业点评页面 */}
            {!user.is_teacher && !isSystemLevel && (
              <Route path="review" element={<StudentHomeworkReview user={user} />} />
            )}
            {/* 学生专用的作业详情页面 */}
            {!user.is_teacher && !isSystemLevel && (
              <Route path="detail/:homeworkId" element={<StudentHomeworkDetail user={user} />} />
            )}
            <Route path=":homeworkId" element={<HomeworkDetail user={user} isSystemLevel={isSystemLevel} />} />
            <Route path="create" element={<HomeworkAssignmentCreate user={user} isSystemLevel={isSystemLevel} />} />
            <Route path="assignment-list" element={<HomeworkAssignmentList user={user} isSystemLevel={isSystemLevel} />} />
            <Route path="student-assignments" element={<StudentHomeworkAssignmentList user={user} />} />
            <Route path="assignment-detail/:assignmentId" element={<HomeworkAssignmentDetail user={user} isSystemLevel={isSystemLevel} />} />
            <Route path="assignment-progress/:assignmentId" element={<HomeworkAssignmentProgress user={user} isSystemLevel={isSystemLevel} />} />
            <Route path="upload-options" element={<HomeworkUploadOptions user={user} isSystemLevel={isSystemLevel} />} />
            <Route path="statistics" element={<HomeworkStatistics user={user} isSystemLevel={isSystemLevel} />} />
            <Route path="pending-review" element={<PendingReviewHomeworks user={user} isSystemLevel={isSystemLevel} />} />
            <Route path="finished" element={<FinishedHomeworks user={user} isSystemLevel={isSystemLevel} />} />
            <Route path="history" element={<HomeworkDetail user={user} showHistory={true} isSystemLevel={isSystemLevel} />} />
            <Route path="calendar" element={<HomeworkCalendar user={user} isSystemLevel={isSystemLevel} />} />
            <Route path="wrong-questions" element={<div>错题收集功能正在开发中...</div>} />
            <Route path="feedback" element={<div>作业反馈功能正在开发中...</div>} />
          </Routes>
        </Content>
      </Layout>
    </Layout>
    </FilterContext.Provider>
  );
};

export default HomeworkManagement; 