#!/usr/bin/env python3
"""
测试批注图片权限控制的脚本
"""

import requests
import json

# 配置
BASE_URL = "http://localhost:8084/api"
HOMEWORK_ID = 388  # 测试的作业ID

def test_login(username, password):
    """测试登录"""
    login_data = {
        "username": username,
        "password": password
    }

    response = requests.post(f"{BASE_URL}/login", data=login_data)
    if response.status_code == 200:
        result = response.json()
        return result.get("access_token")
    else:
        print(f"登录失败: {response.status_code} - {response.text}")
        return None

def test_get_annotated_images(token, homework_id):
    """测试获取批注图片"""
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }

    response = requests.get(f"{BASE_URL}/homework/homework/{homework_id}/annotated-images", headers=headers)
    print(f"获取批注图片 - 状态码: {response.status_code}")

    if response.status_code == 200:
        result = response.json()
        print(f"成功获取批注图片，数量: {len(result)}")
        return result
    else:
        print(f"获取批注图片失败: {response.text}")
        return None

def main():
    print("=== 测试批注图片权限控制 ===")
    
    # 测试教师登录
    print("\n1. 测试教师登录...")
    teacher_token = test_login("teacher", "123456")  # 假设的教师账号
    if teacher_token:
        print("教师登录成功")
        print("测试教师获取批注图片...")
        teacher_result = test_get_annotated_images(teacher_token, HOMEWORK_ID)
    else:
        print("教师登录失败，跳过教师测试")
    
    # 测试学生登录
    print("\n2. 测试学生登录...")
    student_token = test_login("202501", "123456")  # 学生账号
    if student_token:
        print("学生登录成功")
        print("测试学生获取批注图片...")
        student_result = test_get_annotated_images(student_token, HOMEWORK_ID)
    else:
        print("学生登录失败，跳过学生测试")
    
    # 测试其他学生登录（如果有的话）
    print("\n3. 测试其他学生登录...")
    other_student_token = test_login("202502", "123456")  # 其他学生账号
    if other_student_token:
        print("其他学生登录成功")
        print("测试其他学生获取批注图片（应该被拒绝）...")
        other_student_result = test_get_annotated_images(other_student_token, HOMEWORK_ID)
    else:
        print("其他学生登录失败，跳过其他学生测试")

if __name__ == "__main__":
    main()
