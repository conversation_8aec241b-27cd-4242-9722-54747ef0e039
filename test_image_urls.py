#!/usr/bin/env python3
"""
测试图片URL构建的脚本
"""

import requests
import json

# 配置
BASE_URL = "http://danphy.xicp.net:23277/api"
HOMEWORK_ID = 388

def test_login(username, password):
    """测试登录"""
    login_data = {
        "username": username,
        "password": password
    }
    
    response = requests.post(f"{BASE_URL}/login", data=login_data)
    if response.status_code == 200:
        result = response.json()
        return result.get("access_token")
    else:
        print(f"登录失败: {response.status_code} - {response.text}")
        return None

def test_get_homework_detail(token, homework_id):
    """测试获取作业详情"""
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    response = requests.get(f"{BASE_URL}/homework/{homework_id}", headers=headers)
    print(f"获取作业详情 - 状态码: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print(f"作业标题: {result.get('title')}")
        
        # 检查作业图片
        images = result.get('images', [])
        print(f"\n作业图片数量: {len(images)}")
        for i, image in enumerate(images[:3]):  # 只显示前3张
            print(f"  图片 {i+1}: {image.get('image_path')}")
        
        return result
    else:
        print(f"获取作业详情失败: {response.text}")
        return None

def test_get_annotated_images(token, homework_id):
    """测试获取批注图片"""
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    response = requests.get(f"{BASE_URL}/homework/{homework_id}/annotated-images", headers=headers)
    print(f"获取批注图片 - 状态码: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print(f"批注图片数量: {len(result)}")
        for i, image in enumerate(result[:3]):  # 只显示前3张
            print(f"  批注图片 {i+1}: {image.get('image_path')}")
        
        return result
    else:
        print(f"获取批注图片失败: {response.text}")
        return None

def test_image_accessibility(image_path):
    """测试图片是否可访问"""
    if not image_path:
        return False
    
    # 构建完整的图片URL
    if image_path.startswith('/'):
        image_url = f"http://danphy.xicp.net:23277{image_path}"
    else:
        image_url = f"http://danphy.xicp.net:23277/{image_path}"
    
    try:
        response = requests.head(image_url, timeout=10)
        print(f"  图片URL: {image_url} - 状态码: {response.status_code}")
        return response.status_code == 200
    except Exception as e:
        print(f"  图片URL: {image_url} - 访问失败: {e}")
        return False

def main():
    print("=== 测试图片URL构建 ===")
    
    # 测试学生登录
    print("\n1. 测试学生登录...")
    student_token = test_login("202501", "123456")
    if not student_token:
        print("学生登录失败")
        return
    
    print("学生登录成功")
    
    # 测试获取作业详情
    print(f"\n2. 测试获取作业详情 (ID: {HOMEWORK_ID})...")
    homework_detail = test_get_homework_detail(student_token, HOMEWORK_ID)
    
    if homework_detail:
        # 测试作业图片可访问性
        print(f"\n3. 测试作业图片可访问性...")
        images = homework_detail.get('images', [])
        accessible_count = 0
        for i, image in enumerate(images[:3]):
            print(f"  测试作业图片 {i+1}:")
            if test_image_accessibility(image.get('image_path')):
                accessible_count += 1
        
        print(f"  作业图片可访问数量: {accessible_count}/{min(len(images), 3)}")
    
    # 测试获取批注图片
    print(f"\n4. 测试获取批注图片...")
    annotated_images = test_get_annotated_images(student_token, HOMEWORK_ID)
    
    if annotated_images:
        # 测试批注图片可访问性
        print(f"\n5. 测试批注图片可访问性...")
        accessible_count = 0
        for i, image in enumerate(annotated_images[:3]):
            print(f"  测试批注图片 {i+1}:")
            if test_image_accessibility(image.get('image_path')):
                accessible_count += 1
        
        print(f"  批注图片可访问数量: {accessible_count}/{min(len(annotated_images), 3)}")

if __name__ == "__main__":
    main()
