#!/usr/bin/env python3
"""
测试学生作业任务列表API的脚本
"""

import requests
import json

# 配置
BASE_URL = "http://localhost:8083/api"

def test_login(username, password):
    """测试登录"""
    login_data = {
        "username": username,
        "password": password
    }
    
    response = requests.post(f"{BASE_URL}/login", data=login_data)
    if response.status_code == 200:
        result = response.json()
        return result.get("access_token")
    else:
        print(f"登录失败: {response.status_code} - {response.text}")
        return None

def test_get_student_assignments(token):
    """测试获取学生作业任务列表"""
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    response = requests.get(f"{BASE_URL}/student/homework-assignments", headers=headers)
    print(f"获取学生作业任务列表 - 状态码: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print(f"成功获取作业任务列表，数量: {len(result)}")
        
        # 打印前几个作业任务的详细信息
        for i, assignment in enumerate(result[:3]):
            print(f"\n作业任务 {i+1}:")
            print(f"  ID: {assignment.get('id')}")
            print(f"  标题: {assignment.get('title')}")
            print(f"  提交状态: {assignment.get('submission_status')}")
            print(f"  批改状态: {assignment.get('grading_status')}")
            print(f"  作业ID (homework_id): {assignment.get('homework_id')}")
            print(f"  创建时间: {assignment.get('created_at')}")
        
        return result
    else:
        print(f"获取作业任务列表失败: {response.text}")
        return None

def main():
    print("=== 测试学生作业任务列表API ===")
    
    # 测试学生登录
    print("\n1. 测试学生登录...")
    student_token = test_login("202501", "123456")  # 学生账号
    if student_token:
        print("学生登录成功")
        print("测试获取学生作业任务列表...")
        assignments = test_get_student_assignments(student_token)
        
        if assignments:
            # 分析数据结构
            print(f"\n=== 数据分析 ===")
            submitted_count = len([a for a in assignments if a.get('homework_id')])
            total_count = len(assignments)
            print(f"总作业任务数: {total_count}")
            print(f"已提交作业数: {submitted_count}")
            print(f"未提交作业数: {total_count - submitted_count}")
            
            # 查找测试作业99
            test_assignment_99 = None
            for assignment in assignments:
                if "测试作业99" in assignment.get('title', ''):
                    test_assignment_99 = assignment
                    break
            
            if test_assignment_99:
                print(f"\n=== 找到测试作业99 ===")
                print(f"作业任务ID: {test_assignment_99.get('id')}")
                print(f"作业ID (homework_id): {test_assignment_99.get('homework_id')}")
                print(f"标题: {test_assignment_99.get('title')}")
                print(f"提交状态: {test_assignment_99.get('submission_status')}")
            else:
                print(f"\n=== 未找到测试作业99 ===")
                print("可用的作业任务:")
                for assignment in assignments:
                    print(f"  - {assignment.get('title')} (ID: {assignment.get('id')}, homework_id: {assignment.get('homework_id')})")
    else:
        print("学生登录失败")

if __name__ == "__main__":
    main()
