# 外网图片显示问题分析报告

## 问题描述

- **本地访问**：`http://localhost:3000/homework/388` 作业图片和批注图片正常显示
- **外网访问**：通过 `http://17learn.cn` 绑定本地端口3000，API使用 `danphy.xicp.net:23277` 绑定本地8083端口，登录后作业图片和批注图片都不显示

## 问题分析

### 1. 网络架构
```
用户浏览器 → 17learn.cn (前端) → danphy.xicp.net:23277 (后端API)
```

### 2. 图片URL构建逻辑
前端代码中的图片URL构建逻辑：
```javascript
// HomeworkDetail.js 第370行
const staticBaseUrl = (process.env.REACT_APP_API_URL || 'http://localhost:8083').replace('/api', '');
if (imagePath.startsWith('/')) {
  imagePath = `${staticBaseUrl}${imagePath}`;
} else {
  imagePath = `${staticBaseUrl}/${imagePath}`;
}
```

### 3. 可能的问题原因

#### 原因1：环境变量未正确设置
- 当前 `.env` 文件中 `REACT_APP_API_URL` 被注释掉
- 前端使用动态构建的API URL，可能不匹配实际的图片服务器地址

#### 原因2：静态文件服务跨域问题
- FastAPI的静态文件服务可能没有继承CORS设置
- 图片请求被浏览器的同源策略阻止

#### 原因3：图片路径问题
- 图片路径可能包含本地文件系统路径
- 外网无法访问本地文件系统路径

#### 原因4：网络代理问题
- 花生壳内网穿透可能对静态文件请求有限制
- 图片文件大小或类型可能被代理服务器过滤

## 建议的解决方案

### 方案1：配置正确的环境变量（最简单）
```bash
# 在 frontend/.env 文件中启用：
REACT_APP_API_URL=http://danphy.xicp.net:23277/api
```

### 方案2：检查图片路径格式
确保数据库中存储的图片路径格式正确：
- 应该是相对路径：`/uploads/...`
- 不应该包含绝对路径：`C:\...` 或 `/home/<USER>

### 方案3：验证静态文件服务
测试静态文件是否可以直接访问：
```
http://danphy.xicp.net:23277/uploads/[图片路径]
```

### 方案4：添加静态文件CORS支持（需要代码修改）
在后端添加静态文件的CORS支持，但这需要修改代码。

## 诊断步骤

### 1. 检查浏览器开发者工具
- 打开浏览器开发者工具的Network标签
- 查看图片请求的状态码和错误信息
- 检查是否有CORS错误

### 2. 检查图片URL
- 查看前端生成的图片URL是否正确
- 确认图片URL指向正确的服务器地址

### 3. 直接测试图片访问
- 复制图片URL在新标签页中直接访问
- 确认图片文件是否存在且可访问

### 4. 检查网络代理
- 确认花生壳代理是否正确转发静态文件请求
- 检查代理服务器的日志

## 临时解决方案

如果需要快速解决，可以考虑：

1. **使用CDN或对象存储**：将图片上传到阿里云OSS、腾讯云COS等
2. **配置反向代理**：使用Nginx等反向代理服务器统一处理前端和静态文件
3. **修改图片存储策略**：将图片转换为Base64编码存储在数据库中（不推荐大图片）

## 风险评估

- **低风险**：配置环境变量（方案1）
- **中风险**：修改静态文件服务配置
- **高风险**：修改图片存储和访问逻辑

## 建议

基于"禁止改动其它代码，以免影响其它功能"的要求，建议：

1. **首先尝试方案1**：配置正确的环境变量
2. **进行诊断**：使用浏览器开发者工具检查具体错误
3. **联系网络服务提供商**：确认花生壳代理配置是否正确

如果方案1无效，需要进一步的网络层面诊断，而不是代码修改。
